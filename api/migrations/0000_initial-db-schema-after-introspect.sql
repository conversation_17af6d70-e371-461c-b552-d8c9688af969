CREATE TABLE "careers" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"created_at" date DEFAULT now() NOT NULL,
	"updated_at" date DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "student_club_memberships" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"student_id" uuid NOT NULL,
	"club_id" uuid NOT NULL,
	"role" text DEFAULT 'member',
	"joined_at" timestamp DEFAULT now() NOT NULL,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "student_club_memberships_student_id_club_id_unique" UNIQUE("student_id","club_id")
);
--> statement-breakpoint
CREATE TABLE "clubs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"club_admin" uuid,
	"institution_id" uuid NOT NULL,
	"country_id" uuid NOT NULL,
	"is_active" boolean DEFAULT false,
	"club_logo_url" text,
	"club_banner_url" text,
	"created_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "clubs_name_unique" UNIQUE("name"),
	CONSTRAINT "clubs_name_institution_id_unique" UNIQUE("name","institution_id")
);
--> statement-breakpoint
CREATE TABLE "countries" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"disabled" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "countries_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "institutions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"location" text NOT NULL,
	"address" text NOT NULL,
	"city" text NOT NULL,
	"state" text,
	"country_id" uuid NOT NULL,
	"disabled" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "institutions_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "organisations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"user_id" uuid,
	"email" text NOT NULL,
	"contact" text,
	"address" text,
	"organization_banner_url" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"disabled" boolean DEFAULT false,
	"deleted_at" timestamp,
	"deleted" text DEFAULT 'false',
	CONSTRAINT "organisations_user_id_unique" UNIQUE("user_id"),
	CONSTRAINT "organisations_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "student_profiles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"first_name" text NOT NULL,
	"last_name" text NOT NULL,
	"other_name" text,
	"country_id" uuid NOT NULL,
	"date_of_birth" date,
	"phone_number" text,
	"institution_id" uuid NOT NULL,
	"enrollment_date" integer NOT NULL,
	"graduation_date" integer NOT NULL,
	"degree" text DEFAULT 'Bachelors' NOT NULL,
	"programme" text DEFAULT 'ICT' NOT NULL,
	"github_profile" text,
	"linkedin_profile" text,
	"club_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "token" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"token" varchar NOT NULL,
	"type" text NOT NULL,
	"expires_at" timestamp NOT NULL,
	CONSTRAINT "token_user_id_unique" UNIQUE("user_id"),
	CONSTRAINT "token_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" text NOT NULL,
	"role" text DEFAULT 'student' NOT NULL,
	"state" text DEFAULT 'inactive' NOT NULL,
	"profile_pic_url" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "event" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"startingDate" date,
	"startingTime" time,
	"endingDate" date,
	"endingTime" time,
	"virtualLink" text,
	"postId" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "opportunity" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"eligibility" integer[] DEFAULT ARRAY[]::integer[] NOT NULL,
	"applicationUrl" text,
	"startingDate" date,
	"startingTime" time,
	"endingDate" date,
	"endingTime" time,
	"postId" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "post_to_countries" (
	"postId" uuid NOT NULL,
	"countryId" uuid NOT NULL,
	CONSTRAINT "post_to_countries_postId_countryId_pk" PRIMARY KEY("postId","countryId")
);
--> statement-breakpoint
CREATE TABLE "post_to_institutions" (
	"postId" uuid NOT NULL,
	"institutionId" uuid NOT NULL,
	CONSTRAINT "post_to_institutions_postId_institutionId_pk" PRIMARY KEY("postId","institutionId")
);
--> statement-breakpoint
CREATE TABLE "post" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" varchar NOT NULL,
	"description" text NOT NULL,
	"imageUrl" text,
	"status" text DEFAULT 'active',
	"disabled" boolean DEFAULT false,
	"type" text DEFAULT 'general',
	"postedBy" uuid NOT NULL,
	"club_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "question_bank" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar NOT NULL,
	"stack" varchar NOT NULL,
	"framework" varchar NOT NULL,
	"imageUrl" varchar NOT NULL,
	"created_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "question_bank_name_unique" UNIQUE("name"),
	CONSTRAINT "question_bank_name_framework_unique" UNIQUE("name","framework")
);
--> statement-breakpoint
CREATE TABLE "questions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"question" text NOT NULL,
	"option_a" text NOT NULL,
	"option_b" text NOT NULL,
	"option_c" text NOT NULL,
	"option_d" text NOT NULL,
	"answer" text NOT NULL,
	"questionBank_id" uuid NOT NULL,
	"is_golden" boolean DEFAULT false,
	"created_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "question_questionbank_unique" UNIQUE("question","questionBank_id")
);
--> statement-breakpoint
CREATE TABLE "quiz" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" text NOT NULL,
	"question_bank_id" uuid NOT NULL,
	"created_by" uuid NOT NULL,
	"total_questions" integer NOT NULL,
	"time_per_question" integer,
	"start_time" timestamp,
	"end_at" timestamp,
	"status" text DEFAULT 'inactive' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "quiz_score" (
	"id" uuid DEFAULT gen_random_uuid(),
	"quiz_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"score" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "quiz_score_pkey" PRIMARY KEY("quiz_id","user_id")
);
--> statement-breakpoint
CREATE TABLE "raffle_participants" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"raffle_id" uuid,
	"student_id" uuid NOT NULL,
	"entry_date" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "raffle_prizes" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"raffle_id" uuid,
	"type" text NOT NULL,
	"details" json NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "raffle_institutions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"raffle_id" uuid,
	"institution_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "raffles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"start_date" timestamp NOT NULL,
	"end_date" timestamp NOT NULL,
	"max_participants" integer NOT NULL,
	"total_winners" integer NOT NULL,
	"institution_id" uuid,
	"degree" jsonb,
	"scheduled" boolean DEFAULT false NOT NULL,
	"programme" jsonb,
	"level" jsonb,
	"created_by" uuid,
	"status" text DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "raffle_winners" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"raffle_id" uuid,
	"student_id" uuid,
	"selection_date" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "post_engagements" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"student_profile_id" uuid NOT NULL,
	"postId" uuid NOT NULL,
	"post_engagement_type" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "point_rules" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"module" varchar(50) NOT NULL,
	"action" varchar(50) NOT NULL,
	"points_config_id" uuid NOT NULL,
	"frequency" varchar(50) NOT NULL,
	"deleted" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "points_config" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"point_name" varchar(50) NOT NULL,
	"point_value" integer NOT NULL,
	"description" text NOT NULL,
	"created_by" uuid,
	"deleted" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "points_config_point_name_unique" UNIQUE("point_name")
);
--> statement-breakpoint
CREATE TABLE "points_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"student_id" uuid NOT NULL,
	"point_rule_id" uuid NOT NULL,
	"points" integer NOT NULL,
	"description" text NOT NULL,
	"deleted" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "students_careers" (
	"student_profile_id" uuid NOT NULL,
	"career_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "students_careers_student_profile_id_career_id_pk" PRIMARY KEY("student_profile_id","career_id")
);
--> statement-breakpoint
ALTER TABLE "student_club_memberships" ADD CONSTRAINT "student_club_memberships_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_club_memberships" ADD CONSTRAINT "student_club_memberships_club_id_clubs_id_fk" FOREIGN KEY ("club_id") REFERENCES "public"."clubs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clubs" ADD CONSTRAINT "clubs_club_admin_users_id_fk" FOREIGN KEY ("club_admin") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clubs" ADD CONSTRAINT "clubs_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clubs" ADD CONSTRAINT "clubs_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clubs" ADD CONSTRAINT "clubs_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "institutions" ADD CONSTRAINT "institutions_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "organisations" ADD CONSTRAINT "organisations_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_club_id_clubs_id_fk" FOREIGN KEY ("club_id") REFERENCES "public"."clubs"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "token" ADD CONSTRAINT "token_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "event" ADD CONSTRAINT "event_postId_post_id_fk" FOREIGN KEY ("postId") REFERENCES "public"."post"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "opportunity" ADD CONSTRAINT "opportunity_postId_post_id_fk" FOREIGN KEY ("postId") REFERENCES "public"."post"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "post_to_countries" ADD CONSTRAINT "post_to_countries_postId_post_id_fk" FOREIGN KEY ("postId") REFERENCES "public"."post"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "post_to_countries" ADD CONSTRAINT "post_to_countries_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "post_to_institutions" ADD CONSTRAINT "post_to_institutions_postId_post_id_fk" FOREIGN KEY ("postId") REFERENCES "public"."post"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "post_to_institutions" ADD CONSTRAINT "post_to_institutions_institutionId_institutions_id_fk" FOREIGN KEY ("institutionId") REFERENCES "public"."institutions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "post" ADD CONSTRAINT "post_postedBy_users_id_fk" FOREIGN KEY ("postedBy") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "post" ADD CONSTRAINT "post_club_id_clubs_id_fk" FOREIGN KEY ("club_id") REFERENCES "public"."clubs"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "question_bank" ADD CONSTRAINT "question_bank_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "questions" ADD CONSTRAINT "questions_questionBank_id_question_bank_id_fk" FOREIGN KEY ("questionBank_id") REFERENCES "public"."question_bank"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "questions" ADD CONSTRAINT "questions_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_question_bank_id_question_bank_id_fk" FOREIGN KEY ("question_bank_id") REFERENCES "public"."question_bank"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_score" ADD CONSTRAINT "quiz_score_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_score" ADD CONSTRAINT "quiz_score_user_id_student_profiles_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."student_profiles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_participants" ADD CONSTRAINT "raffle_participants_raffle_id_raffles_id_fk" FOREIGN KEY ("raffle_id") REFERENCES "public"."raffles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_participants" ADD CONSTRAINT "raffle_participants_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_prizes" ADD CONSTRAINT "raffle_prizes_raffle_id_raffles_id_fk" FOREIGN KEY ("raffle_id") REFERENCES "public"."raffles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_institutions" ADD CONSTRAINT "raffle_institutions_raffle_id_raffles_id_fk" FOREIGN KEY ("raffle_id") REFERENCES "public"."raffles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_institutions" ADD CONSTRAINT "raffle_institutions_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffles" ADD CONSTRAINT "raffles_institution_id_institutions_id_fk" FOREIGN KEY ("institution_id") REFERENCES "public"."institutions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffles" ADD CONSTRAINT "raffles_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_winners" ADD CONSTRAINT "raffle_winners_raffle_id_raffles_id_fk" FOREIGN KEY ("raffle_id") REFERENCES "public"."raffles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_winners" ADD CONSTRAINT "raffle_winners_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "post_engagements" ADD CONSTRAINT "post_engagements_student_profile_id_student_profiles_id_fk" FOREIGN KEY ("student_profile_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "post_engagements" ADD CONSTRAINT "post_engagements_postId_post_id_fk" FOREIGN KEY ("postId") REFERENCES "public"."post"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "point_rules" ADD CONSTRAINT "point_rules_points_config_id_points_config_id_fk" FOREIGN KEY ("points_config_id") REFERENCES "public"."points_config"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "points_config" ADD CONSTRAINT "points_config_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "points_logs" ADD CONSTRAINT "points_logs_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "points_logs" ADD CONSTRAINT "points_logs_point_rule_id_point_rules_id_fk" FOREIGN KEY ("point_rule_id") REFERENCES "public"."point_rules"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students_careers" ADD CONSTRAINT "students_careers_student_profile_id_student_profiles_id_fk" FOREIGN KEY ("student_profile_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students_careers" ADD CONSTRAINT "students_careers_career_id_careers_id_fk" FOREIGN KEY ("career_id") REFERENCES "public"."careers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_all_time" AS (
  SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    quiz_score.created_at >= '2024-01-01'
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme);--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_current_quarter" AS (  SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('quarter', quiz_score.created_at) = date_trunc('quarter', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
);--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_day" AS (
  SELECT student_profiles.id AS student_id,
  student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('day', quiz_score.created_at) = date_trunc('day', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
  );--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_first_quarter" AS (SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    EXTRACT(QUARTER FROM quiz_score.created_at) = 1
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme);--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_fourth_quarter" AS (SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    EXTRACT(QUARTER FROM quiz_score.created_at) = 4
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme);--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_month" AS (
   SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('month', quiz_score.created_at) = date_trunc('month', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
  );--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_second_quarter" AS (SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    EXTRACT(QUARTER FROM quiz_score.created_at) = 2
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme);--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_third_quarter" AS (SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    EXTRACT(QUARTER FROM quiz_score.created_at) = 3
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme);--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_week" AS (
  SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('week', quiz_score.created_at) = date_trunc('week', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
  );--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."leaderboard_year" AS (
  SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('year', quiz_score.created_at) = date_trunc('year', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
  );