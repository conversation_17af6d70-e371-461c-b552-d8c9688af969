{"id": "88b28f88-c6a1-4baa-86d6-609afa09be90", "prevId": "48d137a1-39ad-4004-9915-bd4bb4f47692", "version": "7", "dialect": "postgresql", "tables": {"public.careers": {"name": "careers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "date", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_club_memberships": {"name": "student_club_memberships", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "club_id": {"name": "club_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "default": "'member'"}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_club_memberships_student_id_student_profiles_id_fk": {"name": "student_club_memberships_student_id_student_profiles_id_fk", "tableFrom": "student_club_memberships", "tableTo": "student_profiles", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_club_memberships_club_id_clubs_id_fk": {"name": "student_club_memberships_club_id_clubs_id_fk", "tableFrom": "student_club_memberships", "tableTo": "clubs", "columnsFrom": ["club_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"student_club_memberships_student_id_club_id_unique": {"name": "student_club_memberships_student_id_club_id_unique", "nullsNotDistinct": false, "columns": ["student_id", "club_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clubs": {"name": "clubs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "club_admin": {"name": "club_admin", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": true}, "country_id": {"name": "country_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "club_logo_url": {"name": "club_logo_url", "type": "text", "primaryKey": false, "notNull": false}, "club_banner_url": {"name": "club_banner_url", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"clubs_club_admin_users_id_fk": {"name": "clubs_club_admin_users_id_fk", "tableFrom": "clubs", "tableTo": "users", "columnsFrom": ["club_admin"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "clubs_institution_id_institutions_id_fk": {"name": "clubs_institution_id_institutions_id_fk", "tableFrom": "clubs", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "clubs_country_id_countries_id_fk": {"name": "clubs_country_id_countries_id_fk", "tableFrom": "clubs", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "clubs_created_by_users_id_fk": {"name": "clubs_created_by_users_id_fk", "tableFrom": "clubs", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"clubs_name_unique": {"name": "clubs_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "clubs_name_institution_id_unique": {"name": "clubs_name_institution_id_unique", "nullsNotDistinct": false, "columns": ["name", "institution_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.countries": {"name": "countries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"countries_name_unique": {"name": "countries_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institutions": {"name": "institutions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "country_id": {"name": "country_id", "type": "uuid", "primaryKey": false, "notNull": true}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"institutions_country_id_countries_id_fk": {"name": "institutions_country_id_countries_id_fk", "tableFrom": "institutions", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"institutions_name_unique": {"name": "institutions_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organisations": {"name": "organisations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "contact": {"name": "contact", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "organization_banner_url": {"name": "organization_banner_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}}, "indexes": {}, "foreignKeys": {"organisations_user_id_users_id_fk": {"name": "organisations_user_id_users_id_fk", "tableFrom": "organisations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organisations_user_id_unique": {"name": "organisations_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}, "organisations_email_unique": {"name": "organisations_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_profiles": {"name": "student_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "other_name": {"name": "other_name", "type": "text", "primaryKey": false, "notNull": false}, "country_id": {"name": "country_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": true}, "enrollment_date": {"name": "enrollment_date", "type": "integer", "primaryKey": false, "notNull": true}, "graduation_date": {"name": "graduation_date", "type": "integer", "primaryKey": false, "notNull": true}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": true, "default": "'Bachelors'"}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": true, "default": "'ICT'"}, "github_profile": {"name": "github_profile", "type": "text", "primaryKey": false, "notNull": false}, "linkedin_profile": {"name": "linkedin_profile", "type": "text", "primaryKey": false, "notNull": false}, "club_id": {"name": "club_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"student_profiles_user_id_users_id_fk": {"name": "student_profiles_user_id_users_id_fk", "tableFrom": "student_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "student_profiles_country_id_countries_id_fk": {"name": "student_profiles_country_id_countries_id_fk", "tableFrom": "student_profiles", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "student_profiles_institution_id_institutions_id_fk": {"name": "student_profiles_institution_id_institutions_id_fk", "tableFrom": "student_profiles", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "student_profiles_club_id_clubs_id_fk": {"name": "student_profiles_club_id_clubs_id_fk", "tableFrom": "student_profiles", "tableTo": "clubs", "columnsFrom": ["club_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.token": {"name": "token", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"token_user_id_users_id_fk": {"name": "token_user_id_users_id_fk", "tableFrom": "token", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"token_user_id_unique": {"name": "token_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}, "token_token_unique": {"name": "token_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'student'"}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true, "default": "'inactive'"}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"email_unique_when_not_deleted": {"name": "email_unique_when_not_deleted", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "deleted = false", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.event": {"name": "event", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "startingDate": {"name": "startingDate", "type": "date", "primaryKey": false, "notNull": false}, "startingTime": {"name": "startingTime", "type": "time", "primaryKey": false, "notNull": false}, "endingDate": {"name": "endingDate", "type": "date", "primaryKey": false, "notNull": false}, "endingTime": {"name": "endingTime", "type": "time", "primaryKey": false, "notNull": false}, "virtualLink": {"name": "virtualLink", "type": "text", "primaryKey": false, "notNull": false}, "postId": {"name": "postId", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"event_postId_post_id_fk": {"name": "event_postId_post_id_fk", "tableFrom": "event", "tableTo": "post", "columnsFrom": ["postId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.opportunity": {"name": "opportunity", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "eligibility": {"name": "eligibility", "type": "integer[]", "primaryKey": false, "notNull": true, "default": "ARRAY[]::integer[]"}, "applicationUrl": {"name": "applicationUrl", "type": "text", "primaryKey": false, "notNull": false}, "startingDate": {"name": "startingDate", "type": "date", "primaryKey": false, "notNull": false}, "startingTime": {"name": "startingTime", "type": "time", "primaryKey": false, "notNull": false}, "endingDate": {"name": "endingDate", "type": "date", "primaryKey": false, "notNull": false}, "endingTime": {"name": "endingTime", "type": "time", "primaryKey": false, "notNull": false}, "postId": {"name": "postId", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"opportunity_postId_post_id_fk": {"name": "opportunity_postId_post_id_fk", "tableFrom": "opportunity", "tableTo": "post", "columnsFrom": ["postId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.post_to_countries": {"name": "post_to_countries", "schema": "", "columns": {"postId": {"name": "postId", "type": "uuid", "primaryKey": false, "notNull": true}, "countryId": {"name": "countryId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"post_to_countries_postId_post_id_fk": {"name": "post_to_countries_postId_post_id_fk", "tableFrom": "post_to_countries", "tableTo": "post", "columnsFrom": ["postId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "post_to_countries_countryId_countries_id_fk": {"name": "post_to_countries_countryId_countries_id_fk", "tableFrom": "post_to_countries", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"post_to_countries_postId_countryId_pk": {"name": "post_to_countries_postId_countryId_pk", "columns": ["postId", "countryId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.post_to_institutions": {"name": "post_to_institutions", "schema": "", "columns": {"postId": {"name": "postId", "type": "uuid", "primaryKey": false, "notNull": true}, "institutionId": {"name": "institutionId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"post_to_institutions_postId_post_id_fk": {"name": "post_to_institutions_postId_post_id_fk", "tableFrom": "post_to_institutions", "tableTo": "post", "columnsFrom": ["postId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "post_to_institutions_institutionId_institutions_id_fk": {"name": "post_to_institutions_institutionId_institutions_id_fk", "tableFrom": "post_to_institutions", "tableTo": "institutions", "columnsFrom": ["institutionId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"post_to_institutions_postId_institutionId_pk": {"name": "post_to_institutions_postId_institutionId_pk", "columns": ["postId", "institutionId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.post": {"name": "post", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false, "default": "'general'"}, "postedBy": {"name": "postedBy", "type": "uuid", "primaryKey": false, "notNull": true}, "club_id": {"name": "club_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"post_postedBy_users_id_fk": {"name": "post_postedBy_users_id_fk", "tableFrom": "post", "tableTo": "users", "columnsFrom": ["postedBy"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "post_club_id_clubs_id_fk": {"name": "post_club_id_clubs_id_fk", "tableFrom": "post", "tableTo": "clubs", "columnsFrom": ["club_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.question_bank": {"name": "question_bank", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "stack": {"name": "stack", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "framework": {"name": "framework", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"question_bank_created_by_users_id_fk": {"name": "question_bank_created_by_users_id_fk", "tableFrom": "question_bank", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"question_bank_name_unique": {"name": "question_bank_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "question_bank_name_framework_unique": {"name": "question_bank_name_framework_unique", "nullsNotDistinct": false, "columns": ["name", "framework"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.questions": {"name": "questions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question": {"name": "question", "type": "text", "primaryKey": false, "notNull": true}, "option_a": {"name": "option_a", "type": "text", "primaryKey": false, "notNull": true}, "option_b": {"name": "option_b", "type": "text", "primaryKey": false, "notNull": true}, "option_c": {"name": "option_c", "type": "text", "primaryKey": false, "notNull": true}, "option_d": {"name": "option_d", "type": "text", "primaryKey": false, "notNull": true}, "answer": {"name": "answer", "type": "text", "primaryKey": false, "notNull": true}, "questionBank_id": {"name": "questionBank_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_golden": {"name": "is_golden", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"questions_questionBank_id_question_bank_id_fk": {"name": "questions_questionBank_id_question_bank_id_fk", "tableFrom": "questions", "tableTo": "question_bank", "columnsFrom": ["questionBank_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "questions_created_by_users_id_fk": {"name": "questions_created_by_users_id_fk", "tableFrom": "questions", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"question_questionbank_unique": {"name": "question_questionbank_unique", "nullsNotDistinct": false, "columns": ["question", "questionBank_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz": {"name": "quiz", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "question_bank_id": {"name": "question_bank_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "total_questions": {"name": "total_questions", "type": "integer", "primaryKey": false, "notNull": true}, "time_per_question": {"name": "time_per_question", "type": "integer", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_at": {"name": "end_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'inactive'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"quiz_question_bank_id_question_bank_id_fk": {"name": "quiz_question_bank_id_question_bank_id_fk", "tableFrom": "quiz", "tableTo": "question_bank", "columnsFrom": ["question_bank_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_score": {"name": "quiz_score", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "quiz_id": {"name": "quiz_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"quiz_score_quiz_id_quiz_id_fk": {"name": "quiz_score_quiz_id_quiz_id_fk", "tableFrom": "quiz_score", "tableTo": "quiz", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quiz_score_user_id_student_profiles_id_fk": {"name": "quiz_score_user_id_student_profiles_id_fk", "tableFrom": "quiz_score", "tableTo": "student_profiles", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"quiz_score_pkey": {"name": "quiz_score_pkey", "columns": ["quiz_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.raffle_participants": {"name": "raffle_participants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "raffle_id": {"name": "raffle_id", "type": "uuid", "primaryKey": false, "notNull": false}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "entry_date": {"name": "entry_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"raffle_participants_raffle_id_raffles_id_fk": {"name": "raffle_participants_raffle_id_raffles_id_fk", "tableFrom": "raffle_participants", "tableTo": "raffles", "columnsFrom": ["raffle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raffle_participants_student_id_student_profiles_id_fk": {"name": "raffle_participants_student_id_student_profiles_id_fk", "tableFrom": "raffle_participants", "tableTo": "student_profiles", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.raffle_prizes": {"name": "raffle_prizes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "raffle_id": {"name": "raffle_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "details": {"name": "details", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"raffle_prizes_raffle_id_raffles_id_fk": {"name": "raffle_prizes_raffle_id_raffles_id_fk", "tableFrom": "raffle_prizes", "tableTo": "raffles", "columnsFrom": ["raffle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.raffle_institutions": {"name": "raffle_institutions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "raffle_id": {"name": "raffle_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"raffle_institutions_raffle_id_raffles_id_fk": {"name": "raffle_institutions_raffle_id_raffles_id_fk", "tableFrom": "raffle_institutions", "tableTo": "raffles", "columnsFrom": ["raffle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raffle_institutions_institution_id_institutions_id_fk": {"name": "raffle_institutions_institution_id_institutions_id_fk", "tableFrom": "raffle_institutions", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.raffles": {"name": "raffles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "max_participants": {"name": "max_participants", "type": "integer", "primaryKey": false, "notNull": true}, "total_winners": {"name": "total_winners", "type": "integer", "primaryKey": false, "notNull": true}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "jsonb", "primaryKey": false, "notNull": false}, "scheduled": {"name": "scheduled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "programme": {"name": "programme", "type": "jsonb", "primaryKey": false, "notNull": false}, "level": {"name": "level", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"raffles_institution_id_institutions_id_fk": {"name": "raffles_institution_id_institutions_id_fk", "tableFrom": "raffles", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raffles_created_by_users_id_fk": {"name": "raffles_created_by_users_id_fk", "tableFrom": "raffles", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.raffle_winners": {"name": "raffle_winners", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "raffle_id": {"name": "raffle_id", "type": "uuid", "primaryKey": false, "notNull": false}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "selection_date": {"name": "selection_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"raffle_winners_raffle_id_raffles_id_fk": {"name": "raffle_winners_raffle_id_raffles_id_fk", "tableFrom": "raffle_winners", "tableTo": "raffles", "columnsFrom": ["raffle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "raffle_winners_student_id_student_profiles_id_fk": {"name": "raffle_winners_student_id_student_profiles_id_fk", "tableFrom": "raffle_winners", "tableTo": "student_profiles", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.post_engagements": {"name": "post_engagements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_profile_id": {"name": "student_profile_id", "type": "uuid", "primaryKey": false, "notNull": true}, "postId": {"name": "postId", "type": "uuid", "primaryKey": false, "notNull": true}, "post_engagement_type": {"name": "post_engagement_type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"post_engagements_student_profile_id_student_profiles_id_fk": {"name": "post_engagements_student_profile_id_student_profiles_id_fk", "tableFrom": "post_engagements", "tableTo": "student_profiles", "columnsFrom": ["student_profile_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "post_engagements_postId_post_id_fk": {"name": "post_engagements_postId_post_id_fk", "tableFrom": "post_engagements", "tableTo": "post", "columnsFrom": ["postId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.point_rules": {"name": "point_rules", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "module": {"name": "module", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "points_config_id": {"name": "points_config_id", "type": "uuid", "primaryKey": false, "notNull": true}, "frequency": {"name": "frequency", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"point_rules_points_config_id_points_config_id_fk": {"name": "point_rules_points_config_id_points_config_id_fk", "tableFrom": "point_rules", "tableTo": "points_config", "columnsFrom": ["points_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.points_config": {"name": "points_config", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "point_name": {"name": "point_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "point_value": {"name": "point_value", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"points_config_created_by_users_id_fk": {"name": "points_config_created_by_users_id_fk", "tableFrom": "points_config", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"points_config_point_name_unique": {"name": "points_config_point_name_unique", "nullsNotDistinct": false, "columns": ["point_name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.points_logs": {"name": "points_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "point_rule_id": {"name": "point_rule_id", "type": "uuid", "primaryKey": false, "notNull": true}, "points": {"name": "points", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "deleted": {"name": "deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"points_logs_student_id_student_profiles_id_fk": {"name": "points_logs_student_id_student_profiles_id_fk", "tableFrom": "points_logs", "tableTo": "student_profiles", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "points_logs_point_rule_id_point_rules_id_fk": {"name": "points_logs_point_rule_id_point_rules_id_fk", "tableFrom": "points_logs", "tableTo": "point_rules", "columnsFrom": ["point_rule_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.students_careers": {"name": "students_careers", "schema": "", "columns": {"student_profile_id": {"name": "student_profile_id", "type": "uuid", "primaryKey": false, "notNull": true}, "career_id": {"name": "career_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"students_careers_student_profile_id_student_profiles_id_fk": {"name": "students_careers_student_profile_id_student_profiles_id_fk", "tableFrom": "students_careers", "tableTo": "student_profiles", "columnsFrom": ["student_profile_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "students_careers_career_id_careers_id_fk": {"name": "students_careers_career_id_careers_id_fk", "tableFrom": "students_careers", "tableTo": "careers", "columnsFrom": ["career_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"students_careers_student_profile_id_career_id_pk": {"name": "students_careers_student_profile_id_career_id_pk", "columns": ["student_profile_id", "career_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {"public.leaderboard_all_time": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "\n  SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    quiz_score.created_at >= '2024-01-01'\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme", "name": "leaderboard_all_time", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_current_quarter": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "  SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    date_trunc('quarter', quiz_score.created_at) = date_trunc('quarter', CURRENT_DATE)\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme\n", "name": "leaderboard_current_quarter", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_day": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "\n  SELECT student_profiles.id AS student_id,\n  student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    date_trunc('day', quiz_score.created_at) = date_trunc('day', CURRENT_DATE)\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme\n  ", "name": "leaderboard_day", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_first_quarter": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    EXTRACT(QUARTER FROM quiz_score.created_at) = 1\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme", "name": "leaderboard_first_quarter", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_fourth_quarter": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    EXTRACT(QUARTER FROM quiz_score.created_at) = 4\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme", "name": "leaderboard_fourth_quarter", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_month": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "\n   SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    date_trunc('month', quiz_score.created_at) = date_trunc('month', CURRENT_DATE)\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme\n  ", "name": "leaderboard_month", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_second_quarter": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    EXTRACT(QUARTER FROM quiz_score.created_at) = 2\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme", "name": "leaderboard_second_quarter", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_third_quarter": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    EXTRACT(QUARTER FROM quiz_score.created_at) = 3\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme", "name": "leaderboard_third_quarter", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_week": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "\n  SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    date_trunc('week', quiz_score.created_at) = date_trunc('week', CURRENT_DATE)\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme\n  ", "name": "leaderboard_week", "schema": "public", "isExisting": false, "materialized": true}, "public.leaderboard_year": {"columns": {"student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_pic_url": {"name": "profile_pic_url", "type": "text", "primaryKey": false, "notNull": false}, "graduation_date": {"name": "graduation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "institution_name": {"name": "institution_name", "type": "text", "primaryKey": false, "notNull": false}, "degree": {"name": "degree", "type": "text", "primaryKey": false, "notNull": false}, "programme": {"name": "programme", "type": "text", "primaryKey": false, "notNull": false}, "total_score": {"name": "total_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rank": {"name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "first_quiz_date": {"name": "first_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_quiz_date": {"name": "last_quiz_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "definition": "\n  SELECT\n    student_profiles.id AS student_id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.graduation_date,\n    student_profiles.enrollment_date,\n    student_profiles.institution_id,\n    institutions.name AS institution_name,\n    student_profiles.degree,\n    student_profiles.programme,\n    SUM(quiz_score.score) AS total_score,\n    DENSE_RANK() OVER (\n        ORDER BY SUM(quiz_score.score) DESC\n    ) AS rank,\n    MIN(quiz_score.created_at) AS first_quiz_date,\n    MAX(quiz_score.updated_at) AS last_quiz_date\n  FROM quiz_score\n    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id\n    JOIN public.users ON student_profiles.user_id = users.id\n    JOIN public.institutions ON student_profiles.institution_id = institutions.id\n  WHERE\n    date_trunc('year', quiz_score.created_at) = date_trunc('year', CURRENT_DATE)\n  GROUP BY\n    student_profiles.id,\n    student_profiles.first_name,\n    student_profiles.last_name,\n    users.profile_pic_url,\n    student_profiles.enrollment_date,\n    student_profiles.graduation_date,\n    student_profiles.institution_id,\n    institutions.name,\n    student_profiles.degree,\n    student_profiles.programme\n  ", "name": "leaderboard_year", "schema": "public", "isExisting": false, "materialized": true}}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}