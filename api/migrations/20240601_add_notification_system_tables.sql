-- Create notification templates table
CREATE TABLE "notification_templates" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "name" varchar(100) NOT NULL,
  "description" text NOT NULL,
  "title_template" text NOT NULL,
  "body_template" text NOT NULL,
  "email_subject_template" text,
  "email_body_template" text,
  "created_by" uuid REFERENCES "users"("id"),
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL,
  CONSTRAINT "notification_templates_name_unique" UNIQUE("name")
);

-- Create notification types table
CREATE TABLE "notification_types" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "code" varchar(50) NOT NULL,
  "name" varchar(100) NOT NULL,
  "description" text NOT NULL,
  "module" varchar(50) NOT NULL,
  "template_id" uuid REFERENCES "notification_templates"("id"),
  "default_channels" text[] NOT NULL DEFAULT '{}',
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL,
  CONSTRAINT "notification_types_code_unique" UNIQUE("code")
);

-- Create notification preferences table
CREATE TABLE "notification_preferences" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "user_id" uuid NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "notification_type_id" uuid NOT NULL REFERENCES "notification_types"("id") ON DELETE CASCADE,
  "email_enabled" boolean NOT NULL DEFAULT true,
  "push_enabled" boolean NOT NULL DEFAULT true,
  "in_app_enabled" boolean NOT NULL DEFAULT true,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create notification logs table
CREATE TABLE "notification_logs" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "notification_type_id" uuid REFERENCES "notification_types"("id"),
  "user_id" uuid REFERENCES "users"("id") ON DELETE SET NULL,
  "title" text NOT NULL,
  "body" text NOT NULL,
  "data" jsonb,
  "channels" text[] NOT NULL,
  "status" varchar(20) NOT NULL DEFAULT 'sent',
  "error" text,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "read_at" timestamp
);

-- Create scheduled notifications table
CREATE TABLE "scheduled_notifications" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "notification_type_id" uuid REFERENCES "notification_types"("id"),
  "title" text NOT NULL,
  "body" text NOT NULL,
  "data" jsonb,
  "channels" text[] NOT NULL,
  "target_audience" jsonb NOT NULL,
  "scheduled_for" timestamp NOT NULL,
  "status" varchar(20) NOT NULL DEFAULT 'pending',
  "created_by" uuid NOT NULL REFERENCES "users"("id"),
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create device tokens table
CREATE TABLE "device_tokens" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "user_id" uuid NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "token" text NOT NULL,
  "device_type" varchar(20) NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX "idx_notification_preferences_user_id" ON "notification_preferences"("user_id");
CREATE INDEX "idx_notification_logs_user_id" ON "notification_logs"("user_id");
CREATE INDEX "idx_notification_logs_created_at" ON "notification_logs"("created_at");
CREATE INDEX "idx_scheduled_notifications_status" ON "scheduled_notifications"("status");
CREATE INDEX "idx_scheduled_notifications_scheduled_for" ON "scheduled_notifications"("scheduled_for");
CREATE INDEX "idx_device_tokens_user_id" ON "device_tokens"("user_id");
CREATE UNIQUE INDEX "idx_device_tokens_user_token" ON "device_tokens"("user_id", "token");
