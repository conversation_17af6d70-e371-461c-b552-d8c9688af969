ALTER TABLE "student_club_memberships" DROP CONSTRAINT "student_club_memberships_student_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "student_club_memberships" DROP CONSTRAINT "student_club_memberships_club_id_clubs_id_fk";
--> statement-breakpoint
ALTER TABLE "post" ADD COLUMN "isGlobal" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "student_club_memberships" ADD CONSTRAINT "student_club_memberships_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_club_memberships" ADD CONSTRAINT "student_club_memberships_club_id_clubs_id_fk" FOREIGN KEY ("club_id") REFERENCES "public"."clubs"("id") ON DELETE cascade ON UPDATE no action;