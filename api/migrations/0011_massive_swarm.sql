ALTER TABLE "quiz_score" DROP CONSTRAINT "quiz_score_user_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "raffle_participants" DROP CONSTRAINT "raffle_participants_student_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "raffle_winners" DROP CONSTRAINT "raffle_winners_student_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "quiz_score" ADD CONSTRAINT "quiz_score_user_id_student_profiles_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_participants" ADD CONSTRAINT "raffle_participants_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "raffle_winners" ADD CONSTRAINT "raffle_winners_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;