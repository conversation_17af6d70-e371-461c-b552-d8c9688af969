CREATE TABLE "skillCategories" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"skillCategoryName" text NOT NULL,
	"created_at" date DEFAULT now() NOT NULL,
	"updated_at" date DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "careers" RENAME TO "skills";--> statement-breakpoint
ALTER TABLE "students_careers" RENAME TO "studentSkills";--> statement-breakpoint
ALTER TABLE "skills" RENAME COLUMN "name" TO "skillName";--> statement-breakpoint
ALTER TABLE "studentSkills" RENAME COLUMN "career_id" TO "skillId";--> statement-breakpoint
ALTER TABLE "studentSkills" DROP CONSTRAINT "students_careers_student_profile_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "studentSkills" DROP CONSTRAINT "students_careers_career_id_careers_id_fk";
--> statement-breakpoint
ALTER TABLE "studentSkills" DROP CONSTRAINT "students_careers_student_profile_id_career_id_pk";--> statement-breakpoint
ALTER TABLE "studentSkills" ALTER COLUMN "created_at" SET DATA TYPE date;--> statement-breakpoint
ALTER TABLE "studentSkills" ALTER COLUMN "updated_at" SET DATA TYPE date;--> statement-breakpoint
ALTER TABLE "studentSkills" ALTER COLUMN "updated_at" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "studentSkills" ADD CONSTRAINT "studentSkills_student_profile_id_skillId_pk" PRIMARY KEY("student_profile_id","skillId");--> statement-breakpoint
ALTER TABLE "skills" ADD COLUMN "skillCategoryId" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "skills" ADD COLUMN "specialization" text;--> statement-breakpoint
ALTER TABLE "studentSkills" ADD COLUMN "proficiency" text DEFAULT 'beginner';--> statement-breakpoint
ALTER TABLE "skills" ADD CONSTRAINT "skills_skillCategoryId_skillCategories_id_fk" FOREIGN KEY ("skillCategoryId") REFERENCES "public"."skillCategories"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "studentSkills" ADD CONSTRAINT "studentSkills_student_profile_id_student_profiles_id_fk" FOREIGN KEY ("student_profile_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "studentSkills" ADD CONSTRAINT "studentSkills_skillId_skills_id_fk" FOREIGN KEY ("skillId") REFERENCES "public"."skills"("id") ON DELETE cascade ON UPDATE no action;