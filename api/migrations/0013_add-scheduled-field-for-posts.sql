ALTER TABLE "studentSkills" ALTER COLUMN "proficiency" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "event" ALTER COLUMN "startingDate" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "event" ALTER COLUMN "endingDate" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "opportunity" ALTER COLUMN "startingDate" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "opportunity" ALTER COLUMN "endingDate" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN IF NOT EXISTS "username_last_updated" timestamp DEFAULT now() NOT NULL;
ALTER TABLE "post" ADD COLUMN IF NOT EXISTS "scheduledAt" timestamp;