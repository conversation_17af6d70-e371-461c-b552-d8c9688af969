-- Fix foreign key constraints to prevent orphaned records when users are deleted
-- Update NO ACTION constraints to SET NULL for user-related tables

-- 1. Fix organisations.user_id constraint
ALTER TABLE "organisations" DROP CONSTRAINT "organisations_user_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "organisations" ADD CONSTRAINT "organisations_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;

-- 2. Fix points_config.created_by constraint  
ALTER TABLE "points_config" DROP CONSTRAINT "points_config_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "points_config" ADD CONSTRAINT "points_config_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;

-- 3. Fix notification_templates.created_by constraint
ALTER TABLE "notification_templates" DROP CONSTRAINT "notification_templates_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "notification_templates" ADD CONSTRAINT "notification_templates_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;

-- 4. Fix scheduled_notifications.created_by constraint
ALTER TABLE "scheduled_notifications" DROP CONSTRAINT "scheduled_notifications_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "scheduled_notifications" ADD CONSTRAINT "scheduled_notifications_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;

-- 5. Fix raffles.createdBy constraint (if it exists)
-- Note: This constraint might not exist yet, so we'll add it with proper SET NULL behavior
-- First, let's check if the constraint exists and drop it if it does
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'raffles_created_by_users_id_fk'
    ) THEN
        ALTER TABLE "raffles" DROP CONSTRAINT "raffles_created_by_users_id_fk";
    END IF;
END $$;
--> statement-breakpoint
ALTER TABLE "raffles" ADD CONSTRAINT "raffles_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;
