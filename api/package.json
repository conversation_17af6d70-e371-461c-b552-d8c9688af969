{"name": "touching-student-life-api", "version": "0.0.1", "description": "Touching Lives Backend API", "author": "<PERSON><PERSON> Koomson & Kevin <PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "node dist/src/pre-main.js", "start:dev": "ts-node -r tsconfig-paths/register src/pre-main.ts", "start:debug": "node --inspect-brk dist/src/pre-main.js", "start:prod": "node dist/src/pre-main.js", "start:nest": "nest start", "start:nest:dev": "nest start --watch", "start:seed": "nest start seed", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --maxWorkers=50% --cache", "test:watch": "jest --watch --maxWorkers=25%", "test:cov": "jest --coverage --maxWorkers=50%", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand --no-cache", "test:ci": "jest --maxWorkers=50% --cache --ci --coverage --watchAll=false", "test:e2e": "jest --config ./test/jest-e2e.json", "drizzle:push": "npx drizzle-kit push", "drizzle:generate": "npx drizzle-kit generate", "seed:notifications": "ts-node -r tsconfig-paths/register scripts/seed-notifications-unified.ts", "seed:notifications:direct": "ts-node scripts/seed-notifications-direct.ts", "drizzle:migrate": "npx drizzle-kit migrate", "seed": "node ./dist/src/seed/seed", "prepare": "cd ../ && husky api/.husky", "precommit": "lint-staged", "instrospect": "npx drizzle-kit introspect", "drizzle:push:no-ssl": "USE_SSL=false npx drizzle-kit push", "drizzle:migrate:no-ssl": "USE_SSL=false npx drizzle-kit migrate"}, "lint-staged": {"*.ts": "eslint --fix"}, "dependencies": {"@aws-sdk/client-s3": "^3.623.0", "@aws-sdk/client-ssm": "^3.826.0", "@bull-board/api": "^6.9.2", "@bull-board/express": "^6.9.2", "@bull-board/nestjs": "^6.9.2", "@epic-web/totp": "^1.1.2", "@golevelup/ts-jest": "^0.5.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.4.0", "@nestjs/throttler": "^6.1.1", "@sentry/node": "^8.17.0", "@types/cookie-parser": "^1.4.7", "@types/jsonwebtoken": "^9.0.6", "@types/nodemailer": "^6.4.15", "bullmq": "^5.51.1", "cookie-parser": "^1.4.6", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.39.2", "drizzle-zod": "^0.5.1", "fast-csv": "^5.0.1", "firebase-admin": "^12.7.0", "handlebars": "^4.7.8", "helmet": "^7.1.0", "hpp": "^0.2.3", "i": "^0.3.7", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "nest-access-control": "^3.1.0", "nest-commander": "^3.17.0", "nest-typed-config": "^2.9.3", "nestjs-pino": "^4.1.0", "nestjs-zod": "^3.0.0", "nodemailer": "^6.9.14", "npm": "^11.1.0", "pg": "^8.12.0", "pino-http": "^10.2.0", "pino-pretty": "^11.2.1", "pino-sentry-transport": "^1.4.0", "postgres": "^3.4.4", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "zod": "^3.23.8", "zod-csv": "^0.0.8"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/async-retry": "^1.4.9", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.11", "@types/node": "^20.14.10", "@types/pg": "^8.11.6", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "async-retry": "^1.3.3", "concurrently": "^9.0.1", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.4", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.7", "jest": "^29.5.0", "lint-staged": "^15.2.10", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "./", "modulePaths": ["<rootDir>"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"isolatedModules": true, "tsconfig": {"incremental": false, "skipLibCheck": true}}]}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"], "moduleNameMapper": {"^defaultLibraryPrefix/shared(|/.*)$": "<rootDir>/libs/shared/src/$1", "^@app/shared(|/.*)$": "<rootDir>/libs/shared/src/$1", "^shared/shared(|/.*)$": "<rootDir>/libs/shared/src/$1", "^@/(.*)$": "<rootDir>/src/$1"}, "maxWorkers": "50%", "testTimeout": 10000, "setupFilesAfterEnv": ["<rootDir>/test/jest.setup.ts"], "cache": true, "cacheDirectory": "<rootDir>/node_modules/.cache/jest", "clearMocks": true, "restoreMocks": true, "resetMocks": false}, "overrides": {"@liaoliaots/nestjs-redis": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0"}}}