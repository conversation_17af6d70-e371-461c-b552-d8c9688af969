# Mailtrap Integration for Email Testing

This document explains how to use Mailtrap for testing emails in development environments.

## Overview

Mailtrap is a fake SMTP server for development teams to test, view and share emails sent from the development and staging environments without sending them to real customers. We've integrated Mailtrap into our application to make email testing easier and more reliable.

## How It Works

1. In development environments, emails are sent to Mailtrap instead of real recipients
2. In production environments, emails are sent to the actual recipients
3. The environment is controlled by the `EMAIL_ENVIRONMENT` setting in the `.env` file

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```
# Email environment settings
# Set to PRODUCTION for real emails, DEVELOPMENT for Mailtrap
EMAIL_ENVIRONMENT=development

# Mailtrap settings (for development)
MAILTRAP_HOST=sandbox.smtp.mailtrap.io
MAILTRAP_PORT=2525
MAILTRAP_USER=your_mailtrap_username
MAILTRAP_PASS=your_mailtrap_password
```

### Getting Mailtrap Credentials

1. Sign up for a free account at [Mailtrap.io](https://mailtrap.io/)
2. Create a new inbox or use the default one
3. Go to the SMTP Settings tab
4. Copy the credentials from the "Integrations" dropdown, selecting "Nodemailer"
5. Use these credentials in your `.env` file

## Usage

### Development Environment

1. Set `EMAIL_ENVIRONMENT=DEVELOPMENT` in your `.env` file
2. Configure your Mailtrap credentials
3. Run your application
4. When emails are sent, they will be captured by Mailtrap
5. View the emails in your Mailtrap inbox

### Production Environment

1. Set `EMAIL_ENVIRONMENT=PRODUCTION` in your `.env` file
2. Configure your production SMTP settings
3. Run your application
4. Emails will be sent to the actual recipients

## Viewing Emails in Mailtrap

1. Log in to your Mailtrap account
2. Go to your inbox
3. You'll see all emails sent from your application
4. Click on an email to view its content, headers, and other details
5. You can also check HTML/CSS rendering, spam score, and more

## Benefits of Using Mailtrap

1. **Safety**: No risk of sending test emails to real users
2. **Visibility**: Easy to see all sent emails in one place
3. **Debugging**: Helps identify and fix email rendering issues
4. **Collaboration**: Team members can share access to the same inbox
5. **Analysis**: Provides spam analysis and HTML/CSS validation

## Implementation Details

The Mailtrap integration is implemented in two main files:

1. **EmailModule**: Configures the email transport based on the environment
2. **EmailService**: Sends emails and logs appropriate messages based on the environment

The system automatically detects the environment and routes emails accordingly:

- In development: Emails go to Mailtrap
- In production: Emails go to real recipients

## Troubleshooting

If emails are not appearing in Mailtrap:

1. Check that `EMAIL_ENVIRONMENT` is set to `DEVELOPMENT`
2. Verify your Mailtrap credentials are correct
3. Make sure your application is actually sending emails
4. Check the application logs for any error messages
5. Ensure your Mailtrap account is active and not over quota
