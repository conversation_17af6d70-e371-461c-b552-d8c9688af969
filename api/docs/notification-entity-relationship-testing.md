# Notification Entity Relationship Testing

## Overview

This document provides comprehensive testing instructions for verifying that notification history logs contain proper ID fields and can be used to fetch corresponding entities.

## What We've Implemented

### 1. **Notification Data Standardization**
- **NotificationHistoryService**: Enhanced with `standardizeNotificationData()` method
- **PostNotificationService**: Updated to include proper ID fields
- **EventNotificationService**: Ensures `event_id` and `post_id` are present
- **OpportunitySelectionService**: Includes `opportunity_id` in notifications

### 2. **ID Field Requirements by Module**
| Module | Required Fields | Description |
|--------|----------------|-------------|
| `event` | `event_id`, `post_id` | Events are posts, so both IDs needed |
| `post` | `post_id` | Standard post notifications |
| `opportunity` | `opportunity_id`, `post_id` | Opportunities are posts |
| `quiz` | `quiz_id` | Quiz-specific notifications |
| `raffle` | `raffle_id` | Raffle-specific notifications |

### 3. **Testing Endpoints**
- **GET** `/api/v1/notifications/test-entity-relationships` - Test ID relationships
- **SQL Script**: `scripts/check-notification-data.sql` - Database analysis

## Testing Methods

### Method 1: API Endpoint Testing

#### Test Entity Relationships Endpoint
```bash
# Test the notification ID relationships
curl -X GET "http://localhost:3000/api/v1/notifications/test-entity-relationships" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Notification entity relationship test results",
  "data": {
    "totalTested": 10,
    "results": [
      {
        "notificationId": "notification-123",
        "title": "New Event Created",
        "module": "event",
        "dataStructure": {
          "hasEventId": true,
          "hasPostId": true,
          "hasOpportunityId": false,
          "allKeys": ["eventId", "event_id", "postId", "post_id", "title", "description"]
        },
        "expectedFields": ["event_id", "post_id"],
        "isValid": true
      }
    ],
    "summary": {
      "total": 10,
      "valid": 8,
      "invalid": 2,
      "byModule": {
        "event": { "total": 5, "valid": 4, "invalid": 1 },
        "post": { "total": 3, "valid": 3, "invalid": 0 },
        "opportunity": { "total": 2, "valid": 1, "invalid": 1 }
      }
    }
  }
}
```

### Method 2: Database Analysis

#### Run SQL Analysis Script
```bash
# Connect to your database and run the analysis
psql -d your_database -f scripts/check-notification-data.sql
```

#### Key Queries to Check:

1. **Recent Notifications Structure**
```sql
SELECT 
    nl.id,
    nl.title,
    nt.module,
    nl.data->>'eventId' as event_id_camel,
    nl.data->>'event_id' as event_id_snake,
    nl.data->>'postId' as post_id_camel,
    nl.data->>'post_id' as post_id_snake,
    nl.created_at
FROM notification_logs nl
LEFT JOIN notification_types nt ON nl.notification_type_id = nt.id
WHERE nt.module = 'event'
    AND nl.data IS NOT NULL
ORDER BY nl.created_at DESC
LIMIT 5;
```

2. **Missing ID Fields Check**
```sql
SELECT 
    'Missing event_id in event notifications' as issue,
    COUNT(*) as count
FROM notification_logs nl
LEFT JOIN notification_types nt ON nl.notification_type_id = nt.id
WHERE nt.module = 'event'
    AND nl.data IS NOT NULL
    AND nl.data->>'eventId' IS NULL
    AND nl.data->>'event_id' IS NULL
    AND nl.created_at > NOW() - INTERVAL '7 days';
```

### Method 3: Manual Testing Steps

#### Step 1: Create Test Notifications
1. Create a new event through the API
2. Create a new post through the API  
3. Create a new opportunity through the API
4. Check that notifications are generated

#### Step 2: Verify Notification Data
1. Call the test endpoint to check data structure
2. Verify that all required ID fields are present
3. Check that both camelCase and snake_case formats exist

#### Step 3: Test Entity Fetching
1. Extract IDs from notification data
2. Use those IDs to fetch the actual entities:
   ```bash
   # Fetch event by event_id
   curl -X GET "http://localhost:3000/api/v1/events/{event_id}"
   
   # Fetch post by post_id  
   curl -X GET "http://localhost:3000/api/v1/posts/{post_id}"
   
   # Fetch opportunity by opportunity_id
   curl -X GET "http://localhost:3000/api/v1/opportunities/{opportunity_id}"
   ```

## Expected Results

### ✅ **Success Indicators**
1. **Complete ID Coverage**: All notifications have required ID fields
2. **Valid Entity References**: All IDs reference existing entities
3. **Consistent Format**: Both camelCase and snake_case formats present
4. **Cross-Module Queries**: Can fetch related entities (e.g., event + post)

### ❌ **Failure Indicators**
1. **Missing IDs**: Notifications lack required ID fields
2. **Orphaned References**: IDs don't reference existing entities
3. **Inconsistent Data**: Some notifications have IDs, others don't
4. **Fetch Failures**: Cannot retrieve entities using notification IDs

## Validation Checklist

- [ ] **Event Notifications**
  - [ ] Contains `event_id` field
  - [ ] Contains `post_id` field  
  - [ ] Can fetch event using `event_id`
  - [ ] Can fetch post using `post_id`

- [ ] **Post Notifications**
  - [ ] Contains `post_id` field
  - [ ] Can fetch post using `post_id`

- [ ] **Opportunity Notifications**
  - [ ] Contains `opportunity_id` field
  - [ ] Contains `post_id` field
  - [ ] Can fetch opportunity using `opportunity_id`
  - [ ] Can fetch post using `post_id`

- [ ] **Data Integrity**
  - [ ] No orphaned IDs (all reference existing entities)
  - [ ] Consistent ID format across notifications
  - [ ] Recent notifications follow new standardization

## Troubleshooting

### Issue: Missing ID Fields
**Solution**: Check if the notification was created before the standardization was implemented. New notifications should have all required fields.

### Issue: Orphaned IDs
**Solution**: Verify that the entities still exist in the database. Some may have been deleted after the notification was created.

### Issue: Inconsistent Data
**Solution**: Run the standardization migration or update the notification services to ensure consistent data structure.

## Next Steps

1. **Monitor New Notifications**: Ensure all new notifications follow the standardized format
2. **Migrate Old Data**: Consider updating existing notifications to include missing ID fields
3. **Add Entity Enrichment**: Implement the `NotificationEntityService` to automatically include entity details
4. **Performance Optimization**: Add caching for frequently accessed entity details

## API Documentation

The test endpoints are documented in Swagger at:
- `http://localhost:3000/api/docs#/Notifications/test-entity-relationships`

## Conclusion

The notification standardization system ensures that:
1. All notification logs contain appropriate ID fields based on their module type
2. These IDs can be used to fetch the corresponding entities
3. The data structure is consistent and predictable
4. Cross-module relationships are maintained (events/opportunities → posts)

This enables rich notification experiences where users can easily navigate from notifications to the actual content they reference.
