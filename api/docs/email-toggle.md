# Email Toggle Feature

This document explains how the email toggle feature works in the application.

## Overview

The email toggle feature allows you to control whether real emails are sent or just previewed in different environments. This is particularly useful for development and testing, where you don't want to send actual emails to users.

## Configuration

The feature is controlled by two environment variables:

1. `EMAIL_TOGGLE`: Can be set to either "ON" or "OFF"
   - "ON": Real emails are sent to the actual recipients
   - "OFF": Emails are not sent but instead saved as JSON files for preview

2. `EMAIL_PREVIEW_RECIPIENT`: An optional email address to redirect all emails to in development/staging
   - If set, all emails will be redirected to this address when `EMAIL_TOGGLE` is "OFF"
   - If not set, the original recipient will be used in the preview files

## Environment-Specific Settings

### Production
```
EMAIL_TOGGLE=ON
```

### Staging
```
EMAIL_TOGGLE=OFF
EMAIL_PREVIEW_RECIPIENT=<EMAIL>
```

### Development
```
EMAIL_TOGGLE=OFF
EMAIL_PREVIEW_RECIPIENT=<EMAIL>
```

## How It Works

When `EMAIL_TOGGLE` is set to "OFF":

1. Instead of sending real emails, the system creates JSON files in the `email-previews` directory
2. Each file contains the email content, including:
   - The original recipient
   - The email subject
   - The template used
   - The context data
   - A timestamp

This allows developers to inspect the emails that would have been sent without actually sending them.

## Viewing Email Previews

Email previews are saved as JSON files in the `email-previews` directory at the root of the project. The files are named using the format:

```
YYYY-MM-DDTHH-MM-SS-MMMZ-recipient_at_example.com.json
```

You can open these files in any text editor to view the email content.

## Testing Real Email Sending

If you need to test actual email sending in a development environment, you can temporarily set `EMAIL_TOGGLE=ON` in your `.env` file. Just remember to set it back to "OFF" when you're done testing.

## Implementation Details

The email toggle feature is implemented in the `EmailService` class. The service checks the `EMAIL_TOGGLE` environment variable before sending any email. If it's set to "OFF", it calls the `previewEmail` method instead of actually sending the email.

The `previewEmail` method creates a JSON file with the email content and saves it to the `email-previews` directory.
