# Notification Data Standardization

## Overview

This document describes the notification data standardization feature that ensures notification logs contain the appropriate ID fields based on the notification type.

## Problem

Previously, notification logs had inconsistent data structures where:
- Event notifications might be missing `event_id` 
- Post notifications might be missing `post_id`
- Opportunity notifications might be missing `opportunity_id`

This made it difficult to track and query notifications by their associated entities.

## Solution

The notification system now automatically standardizes notification data based on the notification type's module:

### Automatic ID Field Addition

| Module Type | Required ID Fields | Description |
|-------------|-------------------|-------------|
| `event` | `event_id`, `post_id` | Events are posts, so both IDs are included |
| `post` | `post_id` | Standard post notifications |
| `opportunity` | `opportunity_id`, `post_id` | Opportunities are posts, so both IDs are included |
| `quiz` | `quiz_id` | Quiz-specific notifications |
| `raffle` | `raffle_id` | Raffle-specific notifications |

### Implementation Details

1. **NotificationHistoryService**: The `logNotification` method now includes a `standardizeNotificationData` function that:
   - Checks the notification type's module
   - Adds missing ID fields based on the module type
   - Preserves existing ID fields (no duplication)
   - Handles both camelCase (`eventId`) and snake_case (`event_id`) formats

2. **Service Updates**: Updated notification services to include proper IDs:
   - **PostNotificationService**: Ensures `post_id` and type-specific IDs are included
   - **EventNotificationService**: Ensures `event_id` and `post_id` are included
   - **OpportunitySelectionService**: Ensures `opportunity_id` is included

3. **Utility Functions**: Created validation utilities in `notification-data.util.ts`:
   - `validateNotificationData()`: Validates data structure
   - `getExpectedIdFields()`: Returns expected fields for a module
   - `hasRequiredIdFields()`: Checks if all required fields are present
   - `getNotificationDataSummary()`: Provides debugging information

## Examples

### Before (Inconsistent)
```json
{
  "title": "New Event",
  "body": "A new event has been created",
  "data": {
    "eventId": "event-123",
    "title": "Test Event",
    "description": "Event description"
    // Missing event_id and post_id
  }
}
```

### After (Standardized)
```json
{
  "title": "New Event", 
  "body": "A new event has been created",
  "data": {
    "eventId": "event-123",
    "event_id": "event-123",    // Added automatically
    "postId": "post-456",
    "post_id": "post-456",      // Added automatically
    "title": "Test Event",
    "description": "Event description"
  }
}
```

## Benefits

1. **Consistent Data Structure**: All notification logs now have predictable ID fields
2. **Better Querying**: Can easily filter notifications by entity IDs
3. **Improved Debugging**: Clear relationship between notifications and their entities
4. **Backward Compatibility**: Existing data is preserved, new fields are added
5. **Type Safety**: Validation utilities help catch missing data early

## Usage

### For Developers

When creating notifications, the system automatically handles ID standardization. However, you should still include the appropriate IDs in your notification data:

```typescript
// Event notification
await notificationService.logNotification({
  notificationTypeId: 'event-type-id',
  userId: 'user-123',
  title: 'New Event',
  body: 'Event description',
  data: {
    eventId: 'event-123',  // Will be standardized to include event_id
    postId: 'post-456',    // Will be standardized to include post_id
    title: 'Event Title'
  },
  channels: ['push', 'email'],
  status: 'sent'
});
```

### Validation

Use the utility functions to validate notification data:

```typescript
import { validateNotificationData } from '@app/shared/notification/utils/notification-data.util';

const validation = validateNotificationData(data, 'event');
if (!validation.isValid) {
  console.log('Missing fields:', validation.missingFields);
  console.log('Recommendations:', validation.recommendations);
}
```

## Testing

Run the test script to verify the standardization works:

```bash
npx ts-node scripts/test-notification-data-standardization.ts
```

## Migration

No migration is needed. The standardization happens automatically for new notifications. Existing notifications remain unchanged but new ones will have the standardized structure.
