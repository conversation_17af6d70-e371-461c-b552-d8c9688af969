# AWS Parameter Store Multi-Environment Configuration Guide

## Table of Contents
1. [Overview & Architecture](#overview--architecture)
2. [Environment Configuration](#environment-configuration)
3. [Implementation Details](#implementation-details)
4. [Error Handling & Fallback Behavior](#error-handling--fallback-behavior)
5. [Developer Workflow](#developer-workflow)
6. [Migration Guide](#migration-guide)
7. [Code Examples](#code-examples)
8. [Troubleshooting](#troubleshooting)

## Overview & Architecture

### Multi-Environment Parameter Store System

The AWS Parameter Store multi-environment implementation provides a robust configuration management system that supports development, staging, and production environments with intelligent fallback mechanisms.

#### Key Features
- **Multi-environment support**: Development, staging, and production
- **Environment-specific path mapping**: Dynamic Parameter Store paths based on NODE_ENV
- **Intelligent fallback**: .env file fallback for non-production environments
- **Production-grade reliability**: Mandatory Parameter Store loading in production
- **Performance optimization**: 5-minute caching with TTL management
- **Backward compatibility**: Fully compatible with existing production setup

#### Architecture Components

```
┌─────────────────────────────────────────────────────────────┐
│                    NestJS Application                       │
├─────────────────────────────────────────────────────────────┤
│  bootstrap-config.ts                                       │
│  ├─ Environment Detection                                  │
│  ├─ Parameter Store Loading                                │
│  └─ Fallback Management                                    │
├─────────────────────────────────────────────────────────────┤
│  ParameterStoreConfigProvider                              │
│  ├─ Multi-environment Path Mapping                        │
│  ├─ AWS SSM Client Integration                             │
│  ├─ Parameter Processing & Caching                        │
│  └─ Error Handling & Validation                           │
├─────────────────────────────────────────────────────────────┤
│                    AWS Parameter Store                      │
│  ├─ /react/dev/api/     (Development)                     │
│  ├─ /react/staging/api/ (Staging)                         │
│  └─ /react/prod/api/    (Production)                      │
└─────────────────────────────────────────────────────────────┘
```

### Environment-Specific Path Mapping

| Environment | NODE_ENV              | Parameter Store Path  | Fallback Behavior           |
| ----------- | --------------------- | --------------------- | --------------------------- |
| Development | `development`         | `/react/dev/api/`     | .env file on failure        |
| Staging     | `staging`             | `/react/staging/api/` | .env file on failure        |
| Production  | `production`          | `/react/prod/api/`    | **FAIL-FAST** (no fallback) |
| Local/Test  | `test`, `local`, etc. | N/A                   | .env file only              |

## Environment Configuration

### AWS Region & Authentication

- **Region**: `eu-west-1` (Ireland)
- **Authentication**: IAM roles (no hardcoded credentials)
- **Service**: AWS Systems Manager Parameter Store

### Required IAM Permissions

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter",
        "ssm:GetParameters",
        "ssm:GetParametersByPath"
      ],
      "Resource": [
        "arn:aws:ssm:eu-west-1:*:parameter/react/dev/api/*",
        "arn:aws:ssm:eu-west-1:*:parameter/react/staging/api/*",
        "arn:aws:ssm:eu-west-1:*:parameter/react/prod/api/*"
      ]
    }
  ]
}
```

### Required Parameters by Environment

#### Core Parameters (All Environments)
- `BULL_BOARD_PASSWORD` (SecureString)
- `ACCESS_KEY` (SecureString)
- `DATABASE_URL` (SecureString)
- `PORT` (String)
- `NODE_ENV` (String)
- `AWS_REGION` (String)

#### Environment-Specific Parameters

**Development (`/react/dev/api/`)**
```
/react/dev/api/BULL_BOARD_PASSWORD
/react/dev/api/ACCESS_KEY
/react/dev/api/DATABASE_URL
/react/dev/api/PORT
/react/dev/api/NODE_ENV
/react/dev/api/AWS_REGION
```

**Staging (`/react/staging/api/`)**
```
/react/staging/api/BULL_BOARD_PASSWORD
/react/staging/api/ACCESS_KEY
/react/staging/api/DATABASE_URL
/react/staging/api/PORT
/react/staging/api/NODE_ENV
/react/staging/api/AWS_REGION
```

**Production (`/react/prod/api/`)**
```
/react/prod/api/BULL_BOARD_PASSWORD
/react/prod/api/ACCESS_KEY
/react/prod/api/DATABASE_URL
/react/prod/api/PORT
/react/prod/api/NODE_ENV
/react/prod/api/AWS_REGION
```

### Parameter Types Supported

| Type           | Description            | Use Case                     |
| -------------- | ---------------------- | ---------------------------- |
| `String`       | Plain text values      | Non-sensitive configuration  |
| `SecureString` | KMS-encrypted values   | Passwords, API keys, secrets |
| `StringList`   | Comma-separated values | Lists, arrays                |

## Implementation Details

### Key Classes

#### ParameterStoreConfigProvider

**Location**: `api/src/aws/parameter-store-config.provider.ts`

**Key Methods**:
- `loadParametersAndMergeWithEnv()`: Main entry point for parameter loading
- `isParameterStoreEnvironment()`: Environment validation
- `getParameterStorePath()`: Dynamic path resolution
- `validateAndNormalizeEnvironment()`: Environment normalization

**Environment Path Mapping**:
```typescript
private static readonly ENVIRONMENT_PATH_MAP = {
  development: '/react/dev/api/',
  staging: '/react/staging/api/',
  production: '/react/prod/api/',
} as const;
```

#### Bootstrap Configuration

**Location**: `api/src/aws/bootstrap-config.ts`

**Responsibilities**:
- Environment detection and validation
- Parameter Store loading orchestration
- Error handling and fallback management
- Application startup configuration

### Environment Detection Logic

```typescript
// Supported environments for Parameter Store loading
const supportedEnvironments = ['development', 'staging', 'production'];
const isParameterStoreEnvironment = nodeEnv &&
  supportedEnvironments.includes(nodeEnv.toLowerCase());
```

### Caching Mechanism

- **TTL**: 5 minutes (300,000 ms)
- **Scope**: Environment-agnostic (works across all environments)
- **Invalidation**: Manual cache clearing supported
- **Performance**: Reduces AWS API calls and improves startup time

```typescript
private static readonly CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes
```

## Error Handling & Fallback Behavior

### Production Environment (NODE_ENV=production)

**Behavior**: **MANDATORY** Parameter Store loading
- ❌ **No fallback** to .env files
- 🚨 **Application exits** with code 1 on Parameter Store failure
- 📋 **Detailed error logging** with troubleshooting steps

**Error Response Example**:
```
💥 CRITICAL ERROR: Parameter Store loading failed in production environment
🚨 Application startup ABORTED - Parameter Store is mandatory for production

📋 Error Details:
   Error Type: AccessDenied
   Error Message: User is not authorized to perform: ssm:GetParametersByPath
   Error Code: AccessDenied

🔍 Parameter Store Configuration:
   Path: /react/prod/api/
   Region: eu-west-1
   Environment: production

❌ Application startup failed - exiting with code 1
```

### Development/Staging Environments

**Behavior**: Parameter Store with **.env fallback**
- ✅ **Graceful fallback** to .env files on Parameter Store failure
- ⚠️ **Warning logs** for Parameter Store failures
- 📄 **Continues startup** with local configuration

**Fallback Response Example**:
```
⚠️  Parameter Store loading failed for development environment
📄 Falling back to .env file configuration...

📋 Error Details:
   Error Type: NetworkingError
   Error Message: Connection timeout

✅ .env fallback configuration loaded successfully
```

### Unsupported Environments

**Behavior**: **.env file only**
- 📄 **Direct .env loading** (no Parameter Store attempt)
- 🔧 **Local development mode**
- ✅ **Standard startup process**

## Developer Workflow

### Adding New Parameters

#### 1. Add to Parameter Store (All Environments)

**Development**:
```bash
aws ssm put-parameter \
  --region eu-west-1 \
  --name "/react/dev/api/NEW_PARAMETER" \
  --value "development-value" \
  --type "String"
```

**Staging**:
```bash
aws ssm put-parameter \
  --region eu-west-1 \
  --name "/react/staging/api/NEW_PARAMETER" \
  --value "staging-value" \
  --type "String"
```

**Production**:
```bash
aws ssm put-parameter \
  --region eu-west-1 \
  --name "/react/prod/api/NEW_PARAMETER" \
  --value "production-value" \
  --type "SecureString"
```

#### 2. Add to .env Template

Create `.env.example` entry for local development:
```bash
# New parameter description
NEW_PARAMETER=local-development-value
```

#### 3. Update Documentation

Add parameter to this guide's required parameters section.

### Local Development Setup

#### 1. Create .env File

```bash
cp .env.example .env
```

#### 2. Configure Environment

```bash
# .env file
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://localhost:5432/myapp_dev
# ... other parameters
```

#### 3. Test Configuration Loading

```bash
# Test development environment
NODE_ENV=development npm run start:dev

# Test staging environment
NODE_ENV=staging npm run start:dev

# Test production environment (requires Parameter Store)
NODE_ENV=production npm run start:prod
```

### Testing Configuration Across Environments

#### Environment Validation Script

```bash
# Test all environments
for env in development staging production; do
  echo "Testing $env environment..."
  NODE_ENV=$env node -e "
    require('./dist/src/aws/bootstrap-config').bootstrapConfig()
      .then(() => console.log('✅ $env: SUCCESS'))
      .catch(err => console.log('❌ $env: FAILED -', err.message))
  "
done
```

## Migration Guide

### Backward Compatibility

✅ **Fully backward compatible** - existing production setup unchanged
✅ **No breaking changes** to existing Parameter Store configuration
✅ **Preserved behavior** for production environments

### Migration Steps

#### 1. Verify Current Production Setup

```bash
# Verify production parameters exist
aws ssm get-parameters-by-path \
  --region eu-west-1 \
  --path "/react/prod/api/" \
  --recursive
```

#### 2. Create Development Environment Parameters

```bash
# Copy production parameters to development
aws ssm get-parameters-by-path \
  --region eu-west-1 \
  --path "/react/prod/api/" \
  --recursive \
  --with-decryption | \
jq -r '.Parameters[] |
  "aws ssm put-parameter --region eu-west-1 --name \"" +
  (.Name | sub("/react/prod/api/"; "/react/dev/api/")) +
  "\" --value \"" + .Value + "\" --type \"" + .Type + "\""'
```

#### 3. Create Staging Environment Parameters

```bash
# Similar process for staging
# Replace /react/prod/api/ with /react/staging/api/
```

#### 4. Validation Checklist

- [ ] Production parameters exist at `/react/prod/api/`
- [ ] Development parameters exist at `/react/dev/api/`
- [ ] Staging parameters exist at `/react/staging/api/`
- [ ] IAM permissions updated for all paths
- [ ] Application starts successfully in all environments
- [ ] Error handling works correctly (test with invalid parameters)
- [ ] Caching mechanism functions properly
- [ ] Fallback behavior works for development/staging

## Code Examples

### Environment-Specific Startup

#### Development Environment
```typescript
// NODE_ENV=development
// Loads from: /react/dev/api/
// Fallback: .env file

console.log('🏭 Development environment detected');
console.log('📡 Parameter Store loading enabled for development');
console.log('📡 Parameter Store loading with fallback to .env for non-production');
```

#### Production Environment
```typescript
// NODE_ENV=production
// Loads from: /react/prod/api/
// Behavior: MANDATORY (no fallback)

console.log('🏭 Production environment detected');
console.log('📡 Parameter Store loading enabled for production');
console.log('📡 Parameter Store loading is MANDATORY for production');
console.log('🚨 Application will NOT start if Parameter Store fails to load');
```

### Parameter Store Path Configuration

```typescript
// Dynamic path resolution
private static getParameterStorePath(nodeEnv: string): string {
  const normalizedEnv = nodeEnv.toLowerCase();

  if (normalizedEnv in this.ENVIRONMENT_PATH_MAP) {
    return this.ENVIRONMENT_PATH_MAP[
      normalizedEnv as keyof typeof this.ENVIRONMENT_PATH_MAP
    ];
  }

  // Fallback to production path for unknown environments
  this.logger.warn(
    `Unknown environment '${nodeEnv}', falling back to production path`,
  );
  return this.ENVIRONMENT_PATH_MAP.production;
}
```

### Error Handling Scenarios

#### Production Error (Fail-Fast)
```typescript
if (nodeEnv === 'production') {
  console.error('💥 CRITICAL ERROR: Parameter Store loading failed');
  console.error('🚨 Application startup ABORTED');
  process.exit(1); // Exit immediately - NO fallback
}
```

#### Development Error (Fallback)
```typescript
else {
  console.warn('⚠️  Parameter Store loading failed for development');
  console.warn('📄 Falling back to .env file configuration...');
  dotenv.config(); // Load .env as fallback
  console.log('✅ .env fallback configuration loaded successfully');
}
```

## Troubleshooting

### Common Issues

#### 1. Parameter Store Access Denied

**Error**: `User is not authorized to perform: ssm:GetParametersByPath`

**Solution**:
- Verify IAM permissions for Parameter Store access
- Check resource ARN paths match environment paths
- Ensure AWS credentials are properly configured

#### 2. Parameters Not Found

**Error**: `No parameters found at path: /react/dev/api/`

**Solution**:
- Verify parameters exist in correct environment path
- Check parameter naming conventions
- Ensure AWS region is set to `eu-west-1`

#### 3. Network Connectivity Issues

**Error**: `Connection timeout` or `NetworkingError`

**Solution**:
- Check internet connectivity
- Verify AWS service endpoints are accessible
- Test with AWS CLI: `aws ssm get-parameters-by-path --path "/react/dev/api/"`

#### 4. Environment Detection Issues

**Error**: Environment not detected correctly

**Solution**:
- Verify `NODE_ENV` is set correctly
- Check supported environments: `development`, `staging`, `production`
- Ensure environment names are lowercase

#### 5. Caching Issues

**Error**: Stale configuration values

**Solution**:
- Clear Parameter Store cache manually
- Restart application to refresh cache
- Check cache TTL settings (5 minutes default)

### Debug Commands

#### Test Parameter Store Access
```bash
# Test development environment access
aws ssm get-parameters-by-path \
  --region eu-west-1 \
  --path "/react/dev/api/" \
  --recursive

# Test with decryption
aws ssm get-parameters-by-path \
  --region eu-west-1 \
  --path "/react/dev/api/" \
  --recursive \
  --with-decryption
```

#### Validate Environment Configuration
```bash
# Check current environment
echo "NODE_ENV: $NODE_ENV"

# Test configuration loading
NODE_ENV=development node -e "
  require('./dist/src/aws/bootstrap-config').bootstrapConfig()
    .then(() => console.log('Configuration loaded successfully'))
    .catch(console.error)
"
```

#### Monitor Application Logs
```bash
# Watch for Parameter Store loading logs
tail -f logs/application.log | grep "Parameter Store"

# Check for error patterns
tail -f logs/application.log | grep -E "(ERROR|CRITICAL|FAILED)"
```

### Performance Monitoring

#### Parameter Store Metrics
```bash
# Monitor Parameter Store API calls
aws logs filter-log-events \
  --region eu-west-1 \
  --log-group-name "/aws/lambda/parameter-store-access" \
  --start-time $(date -d '1 hour ago' +%s)000

# Check application startup times
grep "Configuration loading completed" logs/application.log | \
  awk '{print $1, $2, "Startup completed"}'
```

#### Cache Performance
```typescript
// Check cache hit rate
console.log('Cache valid:', ParameterStoreConfigProvider.isCacheValid());

// Clear cache for testing
ParameterStoreConfigProvider.clearCache();
```

### Security Best Practices

#### Parameter Store Security
- Use `SecureString` type for sensitive values (passwords, API keys)
- Implement least-privilege IAM policies
- Rotate sensitive parameters regularly
- Monitor Parameter Store access logs

#### Environment Isolation
- Separate Parameter Store paths for each environment
- Use different AWS accounts for production isolation
- Implement environment-specific IAM roles
- Regular security audits of parameter access

### Deployment Considerations

#### CI/CD Integration
```yaml
# Example GitHub Actions workflow
- name: Deploy to Development
  env:
    NODE_ENV: development
    AWS_REGION: eu-west-1
  run: |
    npm run build
    npm run start:dev

- name: Deploy to Production
  env:
    NODE_ENV: production
    AWS_REGION: eu-west-1
  run: |
    npm run build
    npm run start:prod
```

#### Health Checks
```typescript
// Parameter Store health check endpoint
@Get('health/parameter-store')
async checkParameterStore() {
  try {
    const isValid = ParameterStoreConfigProvider.isCacheValid();
    return { status: 'healthy', cacheValid: isValid };
  } catch (error) {
    return { status: 'unhealthy', error: error.message };
  }
}
```

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Maintainer**: Development Team

## Quick Reference

### Environment Commands
```bash
# Development
NODE_ENV=development npm run start:dev

# Staging
NODE_ENV=staging npm run start

# Production
NODE_ENV=production npm run start:prod
```

### Parameter Store Paths
- Development: `/react/dev/api/`
- Staging: `/react/staging/api/`
- Production: `/react/prod/api/`

### AWS CLI Commands
```bash
# List parameters
aws ssm get-parameters-by-path --path "/react/dev/api/" --recursive

# Add parameter
aws ssm put-parameter --name "/react/dev/api/NEW_PARAM" --value "value" --type "String"

# Update parameter
aws ssm put-parameter --name "/react/dev/api/EXISTING_PARAM" --value "new-value" --overwrite
```
