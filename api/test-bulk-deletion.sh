#!/bin/bash

# Test script for bulk user deletion with email notification
# This script tests that the completion email is sent to the authenticated admin user

echo "🧪 Testing Bulk User Deletion Email Notification System"
echo "======================================================="

# Test configuration
API_URL="http://localhost:8000/api/v1/auth"
ADMIN_EMAIL="<EMAIL>"

# Test user IDs (these should be valid user IDs in your database)
USER_IDS='[
    "4ca282d0-588a-4b6e-bb3e-1cab254ef8fe",
    "528d0381-6915-46af-8399-33242a383cb4",
    "0a0335c6-816f-44f7-8824-37dfc8efec36"
]'

echo "📧 Admin Email: $ADMIN_EMAIL"
echo "🎯 Target Users: $(echo $USER_IDS | jq -r '. | length') users"
echo ""

# Check if JWT tokens are provided as environment variables
if [ -z "$JWT_TOKEN" ] || [ -z "$REFRESH_TOKEN" ]; then
    echo "❌ Error: JWT_TOKEN and REFRESH_TOKEN environment variables are required"
    echo ""
    echo "Usage:"
    echo "  export JWT_TOKEN='your_jwt_token_here'"
    echo "  export REFRESH_TOKEN='your_refresh_token_here'"
    echo "  ./test-bulk-deletion.sh"
    echo ""
    exit 1
fi

echo "🔐 Using provided JWT tokens..."
echo ""

# Make the bulk deletion request
echo "🚀 Initiating bulk user deletion..."
RESPONSE=$(curl -s -w "\n%{http_code}" \
    --location \
    --request DELETE "${API_URL}/users/bulk" \
    --header 'x-client-type: web' \
    --header 'Content-Type: application/json' \
    --header "Authorization: Bearer $JWT_TOKEN" \
    --header "Cookie: refreshToken=$REFRESH_TOKEN" \
    --data "{
        \"userIds\": $USER_IDS
    }")

# Extract response body and status code
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "📊 Response Status: $HTTP_CODE"
echo "📄 Response Body:"
echo "$RESPONSE_BODY" | jq '.' 2>/dev/null || echo "$RESPONSE_BODY"
echo ""

# Check if the request was successful
if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ Bulk deletion request successful!"
    
    # Extract job ID from response
    JOB_ID=$(echo "$RESPONSE_BODY" | jq -r '.jobId' 2>/dev/null)
    
    if [ "$JOB_ID" != "null" ] && [ -n "$JOB_ID" ]; then
        echo "🆔 Job ID: $JOB_ID"
        echo ""
        echo "📧 Expected Email Notification:"
        echo "   - Recipient: $ADMIN_EMAIL (authenticated admin)"
        echo "   - Subject: Bulk User Deletion Report - Operation Complete"
        echo "   - Content: Personalized completion statistics"
        echo ""
        echo "⏳ The email notification will be sent once the job completes."
        echo "   Check your email inbox for the completion report."
        echo ""
        echo "🔍 You can monitor the job progress in the queue dashboard:"
        echo "   http://localhost:8000/admin/queues"
    else
        echo "⚠️  Warning: Could not extract job ID from response"
    fi
else
    echo "❌ Bulk deletion request failed!"
    echo "   Status Code: $HTTP_CODE"
    echo "   Response: $RESPONSE_BODY"
fi

echo ""
echo "🎯 Test Summary:"
echo "   - Endpoint: DELETE $API_URL/users/bulk"
echo "   - Admin: $ADMIN_EMAIL"
echo "   - Expected: Email sent to authenticated admin user"
echo "   - Status: $([ "$HTTP_CODE" -eq 200 ] && echo "✅ PASSED" || echo "❌ FAILED")"
echo ""
echo "📝 Note: This test verifies that the email notification is sent to the"
echo "   authenticated admin user who initiated the bulk deletion, not to any"
echo "   hardcoded email address."
