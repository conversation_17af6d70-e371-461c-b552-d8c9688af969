import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class BullBoardAuthMiddleware implements NestMiddleware {
  constructor(private readonly configService: ConfigService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers.authorization;

    // Get credentials from environment variables
    const username = this.configService.get('BULL_BOARD_USERNAME', 'admin');
    const password = this.configService.get('BULL_BOARD_PASSWORD', 'admin');

    // Create expected auth header
    const expectedAuth = `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`;

    if (authHeader !== expectedAuth) {
      res.setHeader('WWW-Authenticate', 'Basic');
      res.status(401).send('Unauthorized');
      return;
    }

    next();
  }
}
