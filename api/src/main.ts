import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import {
  Logger as NestLogger,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';
import { AppModule } from './app.module';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { GlobalExceptionFilterFilter } from '@app/shared/global-exception-filter/global-exception-filter.filter';
import helmet from 'helmet';
import cookieParser from 'cookie-parser';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino';
import { patchNestJsSwagger } from 'nestjs-zod';
import hpp from 'hpp';
import { runLeaderboardScripts } from './mcq/leader-board/leader-board-script';
import { registerHandlebarsHelpers } from './mail/handlebars-helpers';
import { BullBoardAuthMiddleware } from './middleware/bull-board-auth.middleware';
import { Request, Response, NextFunction } from 'express';
import { setupGlobalErrorHandlers } from './error-handler';

export async function bootstrap() {
  const logger = new NestLogger('Bootstrap');

  // Set up global error handlers for uncaught exceptions and unhandled promise rejections
  setupGlobalErrorHandlers();

  try {
    // Register Handlebars helpers for email templates
    registerHandlebarsHelpers();

    // Create the NestJS application
    const app = await NestFactory.create(AppModule, {
      bufferLogs: false,
      abortOnError: true,
    });

    // Set up global exception filter
    const { httpAdapter } = app.get(HttpAdapterHost);
    patchNestJsSwagger();

    // Configure logging and global settings
    app.useLogger(app.get(Logger));
    app.useGlobalInterceptors(new LoggerErrorInterceptor());
    app.setGlobalPrefix('api');
    app.useGlobalFilters(new GlobalExceptionFilterFilter(httpAdapter));
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    app.use(hpp());
    app.use(cookieParser());

    // Get environment configuration
    const envConfig = app.get(EnvConfig);

    // Apply Bull Board Auth middleware
    const bullBoardAuthMiddleware = app.get(BullBoardAuthMiddleware);
    app.use(
      '/admin/queues',
      (req: Request, res: Response, next: NextFunction) =>
        bullBoardAuthMiddleware.use(req, res, next),
    );

    if (
      envConfig.NODE_ENV === 'development' ||
      envConfig.NODE_ENV === 'local'
    ) {
      const options = new DocumentBuilder()
        .setTitle('Touching Student App')
        .setDescription('Touching Student API')
        .addServer('http://localhost:8000/api/v1', 'Local environment')
        .addServer('https://api-tslp.amalitech-dev.net/', 'Staging')
        .setVersion('0.0.1')
        .addBearerAuth({
          type: 'http',
        })
        .build();
      const document = SwaggerModule.createDocument(app, options, {});
      SwaggerModule.setup('api-docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          tagsSorter: 'alpha',
          operationsSorter: 'alpha',
        },
        customSiteTitle: 'Touching Life Student API Documentation',
      });
    }
    // Configure Helmet to allow Swagger

    app.use(
      helmet({
        hidePoweredBy: true,
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", 'data:'],
            connectSrc: ["'self'"],
            fontSrc: ["'self'", 'data:'],
            objectSrc: ["'none'"],
            upgradeInsecureRequests: [],
          },
        },
      }),
    );
    app.enableCors();
    app.enableVersioning({
      type: VersioningType.URI,
    });
    try {
      // Start the application
      await app.listen(envConfig.PORT);

      // Load leaderBoard scripts
      await runLeaderboardScripts();

      // Log successful startup
      const appUrl = await app.getUrl();
      logger.log(`🚀 Application started at ${appUrl} (${envConfig.NODE_ENV})`);

      return app;
    } catch (error: any) {
      // Log detailed error information
      logger.error('Failed to start the application', {
        error: {
          message: error.message,
          name: error.name,
          code: error.code,
          errno: error.errno,
          syscall: error.syscall,
          address: error.address,
          port: error.port,
          stack: error.stack,
        },
      });

      // Exit with error code
      process.exit(1);
    }
  } catch (error: any) {
    // Handle errors during initialization
    logger.error('Fatal error during application initialization', {
      error: {
        message: error.message,
        name: error.name,
        code: error.code,
        errno: error.errno,
        syscall: error.syscall,
        address: error.address,
        port: error.port,
        stack: error.stack,
      },
    });

    // Exit with error code
    process.exit(1);
  }
}

// Start the application
// Only call bootstrap if this file is run directly (not imported)
if (require.main === module) {
  bootstrap()
    .then(() => {})
    .catch((error) => {
      // This will catch any unhandled promise rejections
      const logger = new NestLogger('Bootstrap');
      logger.error('Unhandled error during bootstrap', {
        error: {
          message: error.message,
          stack: error.stack,
        },
      });
      process.exit(1);
    });
}
