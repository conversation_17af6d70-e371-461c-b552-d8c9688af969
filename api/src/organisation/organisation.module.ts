import { Module } from '@nestjs/common';
import { OrganisationService } from './organisation.service';
import { OrganisationController } from './organisation.controller';
import { AuthModule } from 'src/auth/auth.module';
import { JwtHelperModule } from 'src/jwt-helper/jwt-helper.module';
import { UploadModule } from '@/upload/upload.module';
@Module({
  imports: [AuthModule, JwtHelperModule, UploadModule],
  providers: [OrganisationService],
  exports: [OrganisationService],
  controllers: [OrganisationController],
})
export class OrganisationModule {}
