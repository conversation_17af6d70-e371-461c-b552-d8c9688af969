import {
  ConflictException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Organisation, organisations } from 'src/db/schema/organisation';
import { and, asc, desc, eq, ilike, or } from 'drizzle-orm';
import { EmailService } from 'src/mail/email.service';
import {
  CreateOrganisationDto,
  organisationQueryParamsDto,
  UpdateOrganisationProfileDto,
} from './organisation.dto';
import { User, user_roles, users } from 'src/db/schema/users';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { otpTypes, token } from 'src/db/schema/token';
import { JwtHelperService } from '@/jwt-helper/jwt-helper.service';
import { CacheService } from '@app/shared/redis/cache.service';
import { CACHE_TTL } from '@app/shared/constants/cache.constant';
import {
  generateOrganisationKey,
  invalidateOrganisationCache,
  invalidateOrganisationCaches,
} from './utils/cache.utils';

@Injectable()
export class OrganisationService {
  private readonly CACHE_PREFIX = 'organisation';
  private readonly CACHE_TTL = CACHE_TTL;

  constructor(
    private jwtHelperService: JwtHelperService,
    private readonly drizzle: DrizzleService,
    private readonly emailService: EmailService,
    private readonly envConfig: EnvConfig,
    private readonly cacheService: CacheService,
  ) {}
  private readonly logger = new Logger(OrganisationService.name);

  async addOrganisation(organisationInput: CreateOrganisationDto) {
    const { name, email, address, contact } = organisationInput;

    try {
      let user: any;
      user = await this.drizzle.db
        .select()
        .from(users)
        .where(eq(users.email, email));
      if (user.length) {
        throw new ConflictException(
          `Organisation with email ${email} already exists`,
        );
      }
      if (!user || Object.keys(user).length === 0) {
        [user] = await this.drizzle.db
          .insert(users)
          .values({
            email,
            role: user_roles.ADMIN,
            profile_pic_url: '',
          })
          .returning();
      }
      const accessToken = await this.jwtHelperService.generateAccessToken({
        userId: user.id,
        expiresIn: this.envConfig.MAGIC_LINK_EXPIRY,
      });

      await this.drizzle.db
        .insert(token)
        .values({
          user_id: user.id,
          token: accessToken,
          type: otpTypes.MAGIC_LINK,
          expiresAt: new Date(Date.now() + this.envConfig.OTP_EXPIRY_TIME),
        })
        .onConflictDoUpdate({
          target: [token.user_id],
          set: {
            token: accessToken,
            expiresAt: new Date(Date.now() + this.envConfig.OTP_EXPIRY_TIME),
          },
        });

      await this.emailService.newOrganisation(
        email,
        name,
        `${process.env.FRONTEND_URL}/auth/verify-magic-link?token=${accessToken}`,
      );

      const [organisation] = await this.drizzle.db
        .insert(organisations)
        .values({
          name: name,
          email: email,
          address: address,
          contact: contact,
          user_id: user.id,
        })
        .onConflictDoUpdate({
          target: [organisations.email],
          set: {
            name: name,
            address: address,
            contact: contact,
            user_id: user.id,
          },
        })
        .returning();

      // Invalidate the list cache since we added a new organisation
      await invalidateOrganisationCache(this.cacheService, this.CACHE_PREFIX);

      // Cache the new organisation
      try {
        await this.cacheService.set(
          generateOrganisationKey(
            this.cacheService,
            organisation?.id ?? '',
            this.CACHE_PREFIX,
          ),
          organisation,
          this.CACHE_TTL.ONE_MONTH,
        );
      } catch (error) {
        this.logger.warn('Failed to set organisation cache', error);
      }

      return organisation;
    } catch (error: any) {
      this.logger.error('Failed to add organisation', error.stack);
      if (error?.status === HttpStatus.CONFLICT)
        throw new ConflictException(error.message);
      throw error;
    }
  }

  /**
   * Retrieves all organisations from the database.
   * @param req - The request object.
   * @returns An array of organisations.
   */

  async getOrganisations({
    sort,
    limit,
    page,
    order,
    all,
    search,
  }: organisationQueryParamsDto & {
    sort: keyof Organisation;
  }) {
    if (search || !all) {
      return this.getOrganisationsFromDB({
        sort,
        limit,
        page,
        order,
        all,
        search,
      });
    }

    const cacheKey = this.cacheService.generateKey(['all'], this.CACHE_PREFIX);

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved organisations from cache');
      return cachedData;
    }

    const organisations = await this.getOrganisationsFromDB({
      sort,
      limit,
      page,
      order,
      all,
      search,
    });

    await this.cacheService.set(
      cacheKey,
      organisations,
      this.CACHE_TTL.SEVEN_DAYS,
    );

    return organisations;
  }

  private async getOrganisationsFromDB({
    sort,
    limit,
    page,
    order,
    all,
    search,
  }: organisationQueryParamsDto & {
    sort: keyof Organisation;
  }) {
    const filters = [];
    if (search)
      filters.push(
        or(
          ilike(organisations.name, `%${search}%`),
          ilike(organisations.email, `%${search}%`),
          ilike(organisations.address, `%${search}%`),
          ilike(organisations.contact, `%${search}%`),
        ),
      );
    const data = await this.drizzle.db.query.organisations.findMany({
      with: {
        creator: {
          columns: {
            profile_pic_url: true,
            state: true!,
          },
        },
      },
      where: and(...filters),
      orderBy:
        order === 'asc' ? asc(organisations[sort]) : desc(organisations[sort]),
      ...(!all && { limit, offset: (page - 1) * limit }),
    });

    const total = await this.drizzle.db.$count(organisations);
    const paginatedResponse = {
      data,
      total,
    };
    return all ? data : paginatedResponse;
  }
  /**
   * Retrieves an organisation by its ID from the database.
   * @param req - The request object.
   * @param id - The ID of the organisation.
   * @returns The organisation with the specified ID.
   */
  async getOrganisationById(id: string) {
    const cacheKey = generateOrganisationKey(
      this.cacheService,
      id,
      this.CACHE_PREFIX,
    );

    // Try to get from cache first
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      this.logger.debug('Retrieved organisation from cache');
      return cached;
    }

    // If not in cache, fetch from database
    const organisation = await this.drizzle.db.query.organisations.findFirst({
      where: eq(organisations.id, id),
      with: {
        creator: true,
      },
    });

    if (!organisation) {
      throw new NotFoundException(`Organisation with id ${id} not found.`);
    }

    // Cache the result
    await this.cacheService.set(
      cacheKey,
      organisation,
      this.CACHE_TTL.ONE_MONTH,
    );

    return organisation;
  }

  /**
   * Updates an organisation in the database.
   * @param id - The ID of the organisation to update.
   * @param organisationInput - The updated data for the organisation.
   * @returns The updated organisation.
   */
  async updateOrganisation(
    id: string,
    organisationInput: CreateOrganisationDto,
  ): Promise<any> {
    const { name, email, address, contact } = organisationInput;

    const existingOrganisation =
      await this.drizzle.db.query.organisations.findFirst({
        where: eq(organisations.id, id),
      });

    if (!existingOrganisation) {
      this.logger.warn(`Organisation with id ${id} not found.`);
      throw new NotFoundException(`Organisation with id ${id} not found.`);
    }

    const [updatedOrganisation] = await this.drizzle.db
      .update(organisations)
      .set({
        name,
        email,
        address,
        contact,
      })
      .where(eq(organisations.id, id))
      .returning();

    try {
      await invalidateOrganisationCaches(
        this.cacheService,
        id,
        this.CACHE_PREFIX,
      );

      // Cache the updated organization
      const completeOrganisation = await this.getOrganisationById(id);
      if (completeOrganisation) {
        const cacheKey = generateOrganisationKey(
          this.cacheService,
          id,
          this.CACHE_PREFIX,
        );
        await this.cacheService.set(
          cacheKey,
          completeOrganisation,
          this.CACHE_TTL.ONE_MONTH,
        );
      }
    } catch (error) {
      this.logger.warn('Failed to update organisation caches', error);
    }

    this.logger.log(`Organisation with id ${id} updated successfully.`);
    return updatedOrganisation;
  }

  /**
   * Deletes an organisation from the database.
   * @param req - The request object.
   * @param id - The ID of the organisation to delete.
   * @returns The deleted organisation.
   */
  async deleteOrganisation(id: string): Promise<any> {
    const result = await this.drizzle.db.transaction(async (tx) => {
      const org = (await tx.query.organisations.findFirst({
        where: eq(organisations.id, id),
        columns: { user_id: true },
      })) as Organisation | undefined;

      if (!org) {
        throw new NotFoundException(`Organisation with ID ${id} not found`);
      }

      if (org.user_id) {
        await tx
          .update(users)
          .set({ state: 'inactive' })
          .where(eq(users.id, org.user_id));
      }

      const organisationResult = await tx
        .delete(organisations)
        .where(eq(organisations.id, id))
        .returning();

      if (organisationResult.length === 0) {
        throw new NotFoundException(`Organisation with ID ${id} not found`);
      }

      return organisationResult;
    });

    try {
      await invalidateOrganisationCaches(
        this.cacheService,
        id,
        this.CACHE_PREFIX,
      );
    } catch (error) {
      this.logger.warn('Failed to invalidate organisation caches', error);
    }

    return {
      message: `Organisation with ID ${id} and its users have been deleted`,
      result,
    };
  }
  async disabledOrganisation(id: string): Promise<any> {
    const result = await this.drizzle.db
      .update(organisations)
      .set({ disabled: true })
      .where(eq(organisations.id, id))
      .returning();
    return result;
  }

  async updateOrganisationProfile(
    data: UpdateOrganisationProfileDto,
    user: User,
  ) {
    const [organisationExists] = await this.drizzle.db
      .select({
        userId: users.id,
        organizationId: organisations.id,
      })
      .from(users)
      .leftJoin(organisations, eq(users.id, organisations.user_id))
      .where(eq(users.id, user.id));

    if (!organisationExists || !organisationExists.organizationId)
      throw new NotFoundException('Organisation does not exist');
    const { organizationId } = organisationExists;
    const updateOrganisation = await this.drizzle.db.transaction(async (tx) => {
      const [organization] = await tx
        .update(organisations)
        .set({
          name: data.name,
          address: data.address,
          contact: data.contact,
          organization_banner_url: data.organization_banner_url,
        })
        .where(eq(organisations.id, organizationId))
        .returning();
      const [user] = await tx
        .update(users)
        .set({ profile_pic_url: data.organization_profile_url })
        .where(eq(users.id, organisationExists!.userId as string))
        .returning();

      return {
        id: user?.id,
        email: user?.email,
        role: user?.role,
        state: user?.state,
        profile_pic_url: user?.profile_pic_url,
        admin_profile: organization,
      };
    });

    return updateOrganisation;
  }
}
