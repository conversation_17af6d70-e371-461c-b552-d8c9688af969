import { OrganisationService } from './organisation.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CacheService } from '@app/shared/redis/cache.service';
import { JwtHelperService } from '@/jwt-helper/jwt-helper.service';
import { ConflictException, NotFoundException } from '@nestjs/common';

// Mock the Logger constructor
jest.mock('@nestjs/common', () => {
  const original = jest.requireActual('@nestjs/common');
  return {
    ...original,
    Logger: jest.fn().mockImplementation(() => ({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    })),
  };
});
import { user_roles } from '@/db/schema/users';
import { CreateOrganisationDto } from './organisation.dto';
import { EmailService } from '@/mail/email.service';
import { EnvConfig } from '@app/shared/dto/env-config.dto';

describe('OrganisationService', () => {
  let service: OrganisationService;

  // Updated DrizzleService mock with proper query structure
  const mockDrizzleService = {
    db: {
      select: jest.fn(),
      insert: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      transaction: jest.fn(),
      $count: jest.fn().mockResolvedValue(2),
      query: {
        organisations: {
          findFirst: jest.fn(),
          findMany: jest.fn().mockImplementation(() => Promise.resolve([])),
        },
      },
    },
  };

  const mockCacheService = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    generateKey: jest.fn(),
    generateResourceKey: jest.fn(),
  };

  const mockJwtHelperService = {
    generateAccessToken: jest.fn(),
  };

  // Updated EmailService mock with the correct method
  const mockEmailService = {
    newOrganisation: jest.fn().mockResolvedValue(undefined),
    sendEmail: jest.fn(),
  };

  const mockEnvConfig = {
    MAGIC_LINK_EXPIRY: '1h',
    OTP_EXPIRY_TIME: 3600000,
    FRONTEND_URL: 'http://localhost:3000',
  };

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Create the service directly with mocks
    service = new OrganisationService(
      mockJwtHelperService as unknown as JwtHelperService,
      mockDrizzleService as unknown as DrizzleService,
      mockEmailService as unknown as EmailService,
      mockEnvConfig as unknown as EnvConfig,
      mockCacheService as unknown as CacheService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addOrganisation', () => {
    const createOrgDto: CreateOrganisationDto = {
      name: 'Test Org',
      email: '<EMAIL>',
      address: 'Test Address',
      contact: '1234567890',
    };

    it('should create a new organisation successfully', async () => {
      const mockUser = {
        id: 'user-123',
        email: createOrgDto.email,
        role: user_roles.ADMIN,
      };

      const mockOrg = {
        id: 'org-123',
        ...createOrgDto,
        user_id: mockUser.id,
      };

      // Mock the select query to return empty array (no existing user)
      mockDrizzleService.db.select.mockImplementation(
        () =>
          ({
            from: () => ({
              where: () => [],
            }),
          }) as any,
      );

      // Mock user insertion
      mockDrizzleService.db.insert.mockImplementationOnce(
        () =>
          ({
            values: () => ({
              returning: () => [mockUser],
            }),
          }) as any,
      );

      // Mock token insertion
      mockDrizzleService.db.insert.mockImplementationOnce(
        () =>
          ({
            values: () => ({
              onConflictDoUpdate: () => ({}),
            }),
          }) as any,
      );

      // Mock organisation insertion
      mockDrizzleService.db.insert.mockImplementationOnce(
        () =>
          ({
            values: () => ({
              onConflictDoUpdate: () => ({
                returning: () => [mockOrg],
              }),
            }),
          }) as any,
      );

      mockJwtHelperService.generateAccessToken.mockResolvedValue('mock-token');

      const result = await service.addOrganisation(createOrgDto);

      expect(result).toEqual(mockOrg);
      expect(mockEmailService.newOrganisation).toHaveBeenCalledWith(
        createOrgDto.email,
        createOrgDto.name,
        expect.stringContaining('/auth/verify-magic-link?token=mock-token'),
      );
    });

    it('should throw ConflictException if organisation email already exists', async () => {
      mockDrizzleService.db.select.mockImplementation(
        () =>
          ({
            from: () => ({
              where: () => [{ id: 'existing-user' }],
            }),
          }) as any,
      );

      try {
        await service.addOrganisation(createOrgDto);
        // If we reach here, the test should fail
        fail('Expected ConflictException was not thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(ConflictException);
        if (error instanceof ConflictException) {
          expect(error.message).toBe(
            `Organisation with email ${createOrgDto.email} already exists`,
          );
        }
      }
    });
  });

  describe('getOrganisations', () => {
    const queryParams = {
      sort: 'name' as const,
      limit: 10,
      page: 1,
      order: 'asc' as const,
      all: true,
      search: '',
    };

    it('should return cached organisations if available', async () => {
      const cachedOrgs = [{ id: '1', name: 'Cached Org' }];
      mockCacheService.get.mockResolvedValue(cachedOrgs);

      const result = await service.getOrganisations(queryParams);
      expect(result).toEqual(cachedOrgs);
    });

    it('should fetch and cache organisations if not in cache', async () => {
      const orgs = [{ id: '1', name: 'Fresh Org' }];
      mockCacheService.get.mockResolvedValue(null);

      // Mock the findMany method to return the orgs
      mockDrizzleService.db.query.organisations.findMany.mockImplementation(
        () => {
          return Promise.resolve(orgs);
        },
      );

      const result = await service.getOrganisations(queryParams);

      expect(result).toEqual(orgs);
      expect(mockCacheService.set).toHaveBeenCalled();
    });
  });

  describe('getOrganisationById', () => {
    const orgId = 'org-123';

    it('should return organisation from cache if available', async () => {
      const cachedOrg = { id: orgId, name: 'Cached Org' };
      mockCacheService.get.mockResolvedValue(cachedOrg);

      const result = await service.getOrganisationById(orgId);
      expect(result).toEqual(cachedOrg);
    });

    it('should fetch and cache organisation if not in cache', async () => {
      const org = { id: orgId, name: 'Fresh Org', creator: {} };
      mockCacheService.get.mockResolvedValue(null);
      mockDrizzleService.db.query.organisations.findFirst.mockResolvedValue(
        org,
      );

      const result = await service.getOrganisationById(orgId);
      expect(result).toEqual(org);
      expect(mockCacheService.set).toHaveBeenCalled();
    });

    it('should throw NotFoundException if organisation not found', async () => {
      mockCacheService.get.mockResolvedValue(null);
      mockDrizzleService.db.query.organisations.findFirst.mockResolvedValue(
        null,
      );

      try {
        await service.getOrganisationById(orgId);
        // If we reach here, the test should fail
        fail('Expected NotFoundException was not thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        if (error instanceof NotFoundException) {
          expect(error.message).toBe(
            `Organisation with id ${orgId} not found.`,
          );
        }
      }
    });
  });
});
