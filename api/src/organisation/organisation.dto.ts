import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import {
  organisationKeys,
  organisations,
  selectOrganisation,
} from '@/db/schema/organisation';
import { createInsertSchema } from 'drizzle-zod';
import { querySchema } from '@/common/dto/query-params.dto';

const creatorSchema = z
  .object({
    state: z.enum(['active', 'disabled', 'inactive']),
  })
  .nullable();
const extendedSelectOrganisation = selectOrganisation.extend({
  creator: creatorSchema,
});

const baseInsertOrganisation = createInsertSchema(organisations);

const insertOrganisation = baseInsertOrganisation.extend({
  name: z.string().trim().min(3),
  email: z.string().trim().min(3),
  contact: z.string().trim().min(10),
  address: z.string().trim().min(3),
  is_active: z.boolean().optional(),
  creator: creatorSchema.optional(),
});

const updateOrganisationProfileSchema = insertOrganisation
  .omit({
    email: true,
    is_active: true,
    creator: true,
  })
  .extend({
    organization_profile_url: z.string().optional(),
    organization_banner_url: z.string().optional(),
  });

const organizationQueryParamsSchema = querySchema.extend({
  sort: z.enum(organisationKeys).optional().default('id'),
  all: z
    .enum(['true', 'false'])
    .optional()
    .default('true')
    .transform((v) => (v === 'true' ? true : false)),
});

export class CreateOrganisationDto extends createZodDto(insertOrganisation) {}
export type OrganisationParams = z.infer<typeof insertOrganisation>;
export class UpdateOrganisationParams extends createZodDto(
  insertOrganisation.partial(),
) {}
export type SelectOrganisation = z.infer<typeof extendedSelectOrganisation>;
const DeleteUserSchema = z.object({
  userId: z.string(),
});

export class DeleteOrganisationDto extends createZodDto(DeleteUserSchema) {}
export class organisationQueryParamsDto extends createZodDto(
  organizationQueryParamsSchema,
) {}
export class UpdateOrganisationProfileDto extends createZodDto(
  updateOrganisationProfileSchema,
) {}
