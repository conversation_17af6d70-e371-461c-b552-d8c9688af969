import { Logger } from '@nestjs/common';
import { CacheService } from '@app/shared/redis/cache.service';

const logger = new Logger('OrganisationCache');

/**
 * Generates a cache key for a specific organisation
 */
export const generateOrganisationKey = (
  cacheService: CacheService,
  id: string,
  prefix: string,
): string => {
  return cacheService.generateResourceKey(id, prefix);
};

/**
 * Invalidates the general organisation cache
 */
export const invalidateOrganisationCache = async (
  cacheService: CacheService,
  prefix: string,
): Promise<void> => {
  try {
    await cacheService.del(`${prefix}:all`);
    logger.debug('Organisation list cache invalidated');
  } catch (error) {
    logger.warn('Failed to invalidate organisation cache', error);
  }
};

/**
 * Invalidates multiple organisation-related caches
 */
export const invalidateOrganisationCaches = async (
  cacheService: CacheService,
  organisationId: string,
  prefix: string,
): Promise<void> => {
  try {
    await Promise.all([
      invalidateOrganisationCache(cacheService, prefix),
      cacheService.del(
        generateOrganisationKey(cacheService, organisationId, prefix),
      ),
    ]);
  } catch (error) {
    logger.warn('Failed to invalidate organisation caches', error);
  }
};
