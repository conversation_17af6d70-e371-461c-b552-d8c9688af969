import { Test, TestingModule } from '@nestjs/testing';
import { OrganisationController } from './organisation.controller';
import { OrganisationService } from './organisation.service';
import { RolesBuilder } from 'nest-access-control';
import { ROLES_BUILDER_TOKEN } from 'nest-access-control';
import { Reflector } from '@nestjs/core';
import { UploadService } from '@/upload/upload.service';

// Mock the CLIENT_TYPE decorator
jest.mock('@/guards/request-validation.decorator', () => ({
  CLIENT_TYPE: () => jest.fn(),
  CLIENT_TYPE_KEY: 'CLIENT_TYPE_KEY',
}));

// Mock the User decorator
jest.mock('@/guards/user.decorator', () => ({
  User: () => jest.fn(),
}));

describe('OrganisationController', () => {
  let controller: OrganisationController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrganisationController],
      providers: [
        {
          provide: OrganisationService,
          useValue: {
            createOrganisation: jest.fn(),
            getAllOrganisations: jest.fn(),
            getOrganisationById: jest.fn(),
            updateOrganisation: jest.fn(),
            deleteOrganisation: jest.fn(),
          },
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: ROLES_BUILDER_TOKEN,
          useValue: new RolesBuilder(),
        },
        {
          provide: UploadService,
          useValue: {
            uploadFile: jest.fn(),
            deleteFile: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<OrganisationController>(OrganisationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
