import {
  Controller,
  Body,
  Post,
  Get,
  Param,
  UseGuards,
  Delete,
  Logger,
  NotFoundException,
  Put,
  Query,
  UseInterceptors,
  UploadedFiles,
  MaxFileSizeValidator,
  FileTypeValidator,
} from '@nestjs/common';
import { OrganisationService } from './organisation.service';
import {
  CreateOrganisationDto,
  organisationQueryParamsDto,
  UpdateOrganisationParams,
  UpdateOrganisationProfileDto,
} from './organisation.dto';

import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiParam,
  ApiQuery,
  ApiHeader,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { AppClients } from '@app/shared/constants/auth.constants';
import { Public } from '@/guards/guard.decorator';
import { ZodSerializerDto } from 'nestjs-zod';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import type { Organisation, User as UserDecoratorType } from '@/db/schema';
import { OrganisationRoutes } from '@app/shared/constants/organisation.constant';
import { ParseFilesPipe } from '@/validators/custom-max-file-size.validator';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { UploadService } from '@/upload/upload.service';
import { Two_MB } from '@app/shared/constants/mcq.constants';
import { User } from '@/guards/user.decorator';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@Controller({ version: '1', path: 'organisation' })
@ApiTags('Organisation')
export class OrganisationController {
  constructor(
    private readonly organisationService: OrganisationService,
    private uploadService: UploadService,
  ) {}
  private readonly logger = new Logger(OrganisationController.name);

  @Post()
  @ApiOperation({
    summary: 'Create organization',
    description:
      'Create a new organization and associated admin user. Sends magic link to organization email for account activation.',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiBody({
    description: 'Organization data',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 3, example: 'Tech Solutions Inc.' },
        email: {
          type: 'string',
          format: 'email',
          minLength: 3,
          example: '<EMAIL>',
        },
        contact: { type: 'string', minLength: 10, example: '+**********' },
        address: {
          type: 'string',
          minLength: 3,
          example: '123 Business St, Tech City, TC 12345',
        },
        is_active: {
          type: 'boolean',
          example: true,
          description: 'Optional - defaults to true',
        },
      },
      required: ['name', 'email', 'contact', 'address'],
    },
  })
  @ApiCreatedResponse({
    description: 'Organization created successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string', example: 'Tech Solutions Inc.' },
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
        },
        contact: { type: 'string', example: '+**********' },
        address: {
          type: 'string',
          example: '123 Business St, Tech City, TC 12345',
        },
        user_id: { type: 'string', format: 'uuid' },
        organization_banner_url: { type: 'string', nullable: true },
        disabled: { type: 'boolean', example: false },
        deleted: { type: 'string', example: 'false' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
        deleted_at: { type: 'string', format: 'date-time', nullable: true },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Organization with email already exists',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        message: {
          type: 'string',
          example:
            'Organisation <NAME_EMAIL> already exists',
        },
        error: { type: 'string', example: 'Conflict' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid organization data',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'array',
          items: { type: 'string' },
          example: [
            'name must be at least 3 characters',
            'email must be a valid email',
          ],
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'organisation', action: 'create', possession: 'any' })
  @ZodSerializerDto(CreateOrganisationDto)
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async addOrganisation(@Body() organisationInput: CreateOrganisationDto) {
    try {
      return await this.organisationService.addOrganisation(organisationInput);
    } catch (error: any) {
      this.logger.error('Failed to add organisation', error.stack);
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all organizations',
    description:
      'Retrieve paginated list of organizations with optional filtering and sorting. Public endpoint accessible without authentication.',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Client type - web or mobile',
    enum: ['web', 'mobile'],
    required: true,
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term for organization name',
    required: false,
    type: String,
    example: 'Tech',
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field',
    required: false,
    type: String,
    example: 'created_at',
  })
  @ApiQuery({
    name: 'order',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
    example: 'desc',
  })
  @ApiQuery({
    name: 'all',
    description: 'Return all organizations without pagination',
    required: false,
    enum: ['true', 'false'],
    example: 'true',
  })
  @ApiOkResponse({
    description: 'Organizations retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              name: { type: 'string', example: 'Tech Solutions Inc.' },
              email: {
                type: 'string',
                format: 'email',
                example: '<EMAIL>',
              },
              contact: { type: 'string', example: '+**********' },
              address: {
                type: 'string',
                example: '123 Business St, Tech City, TC 12345',
              },
              user_id: { type: 'string', format: 'uuid' },
              organization_banner_url: { type: 'string', nullable: true },
              disabled: { type: 'boolean', example: false },
              deleted: { type: 'string', example: 'false' },
              created_at: { type: 'string', format: 'date-time' },
              updated_at: { type: 'string', format: 'date-time' },
              deleted_at: {
                type: 'string',
                format: 'date-time',
                nullable: true,
              },
              creator: {
                type: 'object',
                nullable: true,
                properties: {
                  state: {
                    type: 'string',
                    enum: ['active', 'disabled', 'inactive'],
                  },
                },
              },
            },
          },
        },
        total: {
          type: 'number',
          example: 25,
          description:
            'Total number of organizations (only when pagination is used)',
        },
      },
    },
  })
  @Public()
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getOrganisations(
    @Query() query: organisationQueryParamsDto,
  ): Promise<any> {
    try {
      return await this.organisationService.getOrganisations(
        query as organisationQueryParamsDto & {
          sort: keyof Organisation;
        },
      );
    } catch (error: any) {
      this.logger.error('Failed to retrieve organisations', error.stack);
      throw error;
    }
  }

  @Get(OrganisationRoutes.GET_BY_ID)
  @ApiOperation({
    summary: 'Get single organization',
    description:
      'Retrieve a specific organization by its ID. Public endpoint accessible without authentication.',
  })
  @ApiParam({
    name: 'id',
    description: 'Organization ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Client type - web or mobile',
    enum: ['web', 'mobile'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Organization retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string', example: 'Tech Solutions Inc.' },
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
        },
        contact: { type: 'string', example: '+**********' },
        address: {
          type: 'string',
          example: '123 Business St, Tech City, TC 12345',
        },
        user_id: { type: 'string', format: 'uuid' },
        organization_banner_url: { type: 'string', nullable: true },
        disabled: { type: 'boolean', example: false },
        deleted: { type: 'string', example: 'false' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
        deleted_at: { type: 'string', format: 'date-time', nullable: true },
        creator: {
          type: 'object',
          nullable: true,
          properties: {
            state: { type: 'string', enum: ['active', 'disabled', 'inactive'] },
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Organization not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example:
            'Organisation with id 123e4567-e89b-12d3-a456-************ not found.',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getOrganisationById(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<any> {
    try {
      const organisation =
        await this.organisationService.getOrganisationById(id);
      if (!organisation) {
        this.logger.warn(`Organisation with id ${id} not found.`);
        throw new NotFoundException(`Organisation with id ${id} not found.`);
      }
      this.logger.log(`Organisation with id ${id} retrieved successfully.`);
      return organisation;
    } catch (error: any) {
      this.logger.error(
        `Failed to retrieve organisation with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(OrganisationRoutes.DELETE_BY_ID)
  @ApiOperation({
    summary: 'Delete organization',
    description:
      'Delete an organization by its ID. This action is irreversible.',
  })
  @ApiParam({
    name: 'id',
    description: 'Organization ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Organization deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Organization deleted successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Organization not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Organization not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'organisation', action: 'delete', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async deleteOrganisation(@Param('id', new CustomParseUUIDPipe()) id: string) {
    try {
      this.logger.log(`Deleting organisation with id ${id}`);
      const result = await this.organisationService.deleteOrganisation(id);
      return result;
    } catch (error: any) {
      this.logger.error('error deleting organisation', error.stack);
      throw error;
    }
  }

  @Put(OrganisationRoutes.UPDATE_BY_ID)
  @ApiOperation({
    summary: 'Update organization',
    description: 'Update an existing organization by its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'Organization ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiBody({
    description: 'Updated organization data',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          minLength: 3,
          example: 'Updated Tech Solutions Inc.',
        },
        email: {
          type: 'string',
          format: 'email',
          minLength: 3,
          example: '<EMAIL>',
        },
        contact: { type: 'string', minLength: 10, example: '+************' },
        address: {
          type: 'string',
          minLength: 3,
          example: 'Ama Akroma Stream New Business Ave, Takoradi , TD 54321',
        },
        is_active: { type: 'boolean', example: true },
      },
    },
  })
  @ApiOkResponse({
    description: 'Organization updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string', example: 'Updated Tech Solutions Inc.' },
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
        },
        contact: { type: 'string', example: '+1987654321' },
        address: {
          type: 'string',
          example: '456 New Business Ave, Tech City, TC 54321',
        },
        user_id: { type: 'string', format: 'uuid' },
        organization_banner_url: { type: 'string', nullable: true },
        disabled: { type: 'boolean', example: false },
        deleted: { type: 'string', example: 'false' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
        deleted_at: { type: 'string', format: 'date-time', nullable: true },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid organization data',
  })
  @ApiNotFoundResponse({
    description: 'Organization not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'organisation', action: 'update', possession: 'any' })
  @ZodSerializerDto(UpdateOrganisationParams)
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async updateOrganissation(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() organisationInput: CreateOrganisationDto,
  ): Promise<any> {
    try {
      const updatedOrganisation =
        await this.organisationService.updateOrganisation(
          id,
          organisationInput,
        );
      this.logger.log(`Organisation with id ${id} updated successfully.`);
      return updatedOrganisation;
    } catch (error: any) {
      this.logger.error(
        `Failed to update organisation with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Put(OrganisationRoutes.DISABLE_ORGANISATION)
  @ApiOperation({
    summary: 'Disable organization',
    description:
      'Disable an organization by its ID. This prevents the organization from being active.',
  })
  @ApiParam({
    name: 'id',
    description: 'Organization ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Organization disabled successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Organization disabled successfully',
        },
        disabled: { type: 'boolean', example: true },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Organization not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'organisation', action: 'update', possession: 'any' })
  @ZodSerializerDto(UpdateOrganisationParams)
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async disableOrganisation(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<any> {
    try {
      const result = await this.organisationService.disabledOrganisation(id);
      this.logger.log(`Organisation with ID ${id} disabled successfully.`);
      return result;
    } catch (error: any) {
      this.logger.error(
        `Failed to disable organisation with ID ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Put(OrganisationRoutes.UPDATE_ORGANISATION_PROFILE)
  @ApiOperation({
    summary: 'Update organization profile',
    description:
      'Update organization profile information including profile and banner images.',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Organization profile data with optional image uploads',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          minLength: 3,
          example: 'Updated Organization Name',
        },
        contact: { type: 'string', minLength: 10, example: '+**********' },
        address: {
          type: 'string',
          minLength: 3,
          example: '123 Updated Address St',
        },
        organization_profile_url: {
          type: 'string',
          format: 'url',
          description: 'Existing profile image URL',
        },
        organization_banner_url: {
          type: 'string',
          format: 'url',
          description: 'Existing banner image URL',
        },
        organization_profile_image: {
          type: 'string',
          format: 'binary',
          description: 'New profile image file (max 2MB, jpg/jpeg/png/gif)',
        },
        organization_banner_image: {
          type: 'string',
          format: 'binary',
          description: 'New banner image file (max 2MB, jpg/jpeg/png/gif)',
        },
      },
    },
  })
  @ApiOkResponse({
    description: 'Organization profile updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string', example: 'Updated Organization Name' },
        contact: { type: 'string', example: '+**********' },
        address: { type: 'string', example: '123 Updated Address St' },
        organization_profile_url: {
          type: 'string',
          format: 'url',
          nullable: true,
        },
        organization_banner_url: {
          type: 'string',
          format: 'url',
          nullable: true,
        },
        updated_at: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid file type, size, or organization data',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'string',
          example: 'File size exceeds 2MB limit or invalid file type',
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'organisation',
    action: 'update',
    possession: 'any',
  })
  @ZodSerializerDto(UpdateOrganisationParams)
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'organization_profile_image', maxCount: 1 },
      { name: 'organization_banner_image', maxCount: 1 },
    ]),
  )
  async updateOrganisationProfile(
    @Body() organizationInput: UpdateOrganisationProfileDto,
    @UploadedFiles(
      new ParseFilesPipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: Two_MB }),
          new FileTypeValidator({ fileType: /(jpg|jpeg|png|gif)$/ }),
        ],
      }),
    )
    files: {
      organization_profile_image: Express.Multer.File[];
      organization_banner_image: Express.Multer.File[];
    },
    @User() user: UserDecoratorType,
  ): Promise<any> {
    try {
      if (
        files.organization_profile_image &&
        files.organization_profile_image[0]
      ) {
        organizationInput.organization_profile_url = (
          await this.uploadService.uploadFileToS3(
            files.organization_profile_image[0],
          )
        ).imageUrl;
      }
      if (
        files.organization_banner_image &&
        files.organization_banner_image[0]
      ) {
        organizationInput.organization_banner_url = (
          await this.uploadService.uploadFileToS3(
            files.organization_banner_image[0],
          )
        ).imageUrl;
      }
      const updatedOrganisation =
        await this.organisationService.updateOrganisationProfile(
          organizationInput,
          user,
        );
      return updatedOrganisation;
    } catch (error: any) {
      this.logger.error(`Failed to update organisation profile`, error.stack);
      throw error;
    }
  }
}
