import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import {
  countries,
  countryKeys,
  selectCountrySchema,
} from '@/db/schema/countries';
import { createInsertSchema } from 'drizzle-zod';
import { querySchema } from '@/common/dto/query-params.dto';

const baseInsertCountry = createInsertSchema(countries);
const insertCountrySchema = baseInsertCountry.extend({
  name: z.string().trim().min(3),
  code: z
    .string()
    .trim()
    .min(2)
    .regex(/^[A-Za-z]+$/)
    .transform((val) => val.toUpperCase()),
  is_active: z.boolean().optional(),
});
const updateCountrySchema = baseInsertCountry.extend({
  code: z.string().trim().min(2).optional(),
  name: z.string().trim().min(3).optional(),
  is_active: z.boolean().optional(),
});

const countryQueryParamsSchema = querySchema.extend({
  all: z
    .enum(['true', 'false'])
    .optional()
    .default('true')
    .transform((v) => (v === 'true' ? true : false)),
  sort: z.enum(countryKeys).optional().default('id'),
});

export class CountryDto extends createZodDto(insertCountrySchema) {}
export type CountryParam = z.infer<typeof selectCountrySchema>;
export type UpdateCountryParam = z.infer<typeof insertCountrySchema>;
export class UpdateCountryDto extends createZodDto(updateCountrySchema) {}
export class countryQueryParamsDto extends createZodDto(
  countryQueryParamsSchema,
) {}
