import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CountryDto } from './country.dto';
import type { UpdateCountryDto } from './country.dto';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { countries, Country } from '@/db/schema/countries';
import { and, asc, desc, eq, ilike, sql } from 'drizzle-orm';
import { CountryServiceMessages } from './country.message';
import { institutions, User, user_roles } from '@/db/schema';
import { queryParamsDto } from '@/common/dto/query-params.dto';
import { CacheService } from '@app/shared/redis/cache.service';
import {
  generateCountryKey,
  invalidateCountryCache,
  invalidateCountryCaches,
} from './utils/cache.utils';
import { formatCountryName } from './utils/string.utils';

@Injectable()
export class CountryService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
  ) {}

  private readonly logger = new Logger(CountryService.name);
  private readonly CACHE_PREFIX = 'country';
  private readonly CACHE_TTL = 60 * 60 * 24 * 30;

  async addCountry(createCountryDto: CountryDto) {
    const { code }: CountryDto = createCountryDto;
    const name = formatCountryName(createCountryDto.name);

    const existingCountry = await this.drizzle.db
      .select()
      .from(countries)
      .where(sql`UPPER(${countries.name}) = ${name.toUpperCase()}`)
      .limit(1);

    if (existingCountry.length > 0) {
      throw new ConflictException('Country already exists');
    }

    return await this.drizzle.db.transaction(async (tx: any) => {
      const [newCountry] = await tx
        .insert(countries)
        .values({
          name,
          code,
        })
        .returning();

      await invalidateCountryCache(this.cacheService, this.CACHE_PREFIX);

      try {
        await this.cacheService.set(
          generateCountryKey(
            this.cacheService,
            newCountry.id,
            this.CACHE_PREFIX,
          ),
          newCountry,
          this.CACHE_TTL,
        );
      } catch (error) {
        this.logger.warn('Failed to set country cache', error);
      }
      return newCountry;
    });
  }
  async getAllCountries(
    user: User,
    {
      sort,
      order,
      limit,
      all,
      page,
      search,
    }: queryParamsDto & {
      all?: boolean;
      sort: keyof Country;
    },
  ) {
    // Don't use cache for filtered/paginated/search requests
    if (search || !all) {
      return this.getCountriesFromDB(user, {
        sort,
        order,
        limit,
        all,
        page,
        search,
      });
    }

    const cacheKey = this.cacheService.generateKey(
      ['all', user.role],
      this.CACHE_PREFIX,
    );

    // Try to get from cache first
    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved countries from cache');
      return cachedData;
    }

    // If not in cache or cache failed, get from database
    const countries = await this.getCountriesFromDB(user, {
      sort,
      order,
      limit,
      all,
      page,
      search,
    });

    await this.cacheService.set(cacheKey, countries, this.CACHE_TTL);

    return countries;
  }

  private async getCountriesFromDB(
    user: User,
    {
      sort,
      order,
      limit,
      all,
      page,
      search,
    }: queryParamsDto & {
      all?: boolean;
      sort: keyof Country;
    },
  ) {
    const isUserStudent =
      user.role === user_roles.STUDENT ||
      user.role === user_roles.STUDENT_ADMIN;

    const whereCondition = [
      isUserStudent ? eq(countries.disabled, false) : undefined,
    ].filter(Boolean); // Remove undefined conditions

    if (search) {
      whereCondition.push(ilike(countries.name, `%${search}%`));
    }

    const query = this.drizzle.db
      .select()
      .from(countries)
      .where(and(...whereCondition))
      .orderBy(order === 'asc' ? asc(countries[sort]) : desc(countries[sort]));

    if (!all) {
      const data = await query.limit(limit).offset((page - 1) * limit);
      const total = await this.drizzle.db.$count(
        countries,
        and(...whereCondition),
      );

      return {
        data,
        total,
      };
    }

    return await query;
  }
  async getAllActiveCountries(): Promise<CountryDto[]> {
    return await this.drizzle.db
      .select()
      .from(countries)
      .where(eq(countries.disabled, false));
  }

  async getDisabledCountries(): Promise<CountryDto[]> {
    return await this.drizzle.db
      .select()
      .from(countries)
      .where(eq(countries.disabled, true));
  }
  async findCountryById(id: string) {
    if (!id) {
      throw new BadRequestException(CountryServiceMessages.CountryId);
    }
    const country = await this.drizzle.db.query.countries.findFirst({
      where: eq(countries.id, id),
    });
    if (!country) {
      throw new NotFoundException(CountryServiceMessages.CountryNotFound);
    }
    return country;
  }
  async deleteCountryById(id: string): Promise<CountryDto[]> {
    if (!id) {
      throw new BadRequestException(CountryServiceMessages.CountryId);
    }

    const existingCountry = await this.findCountryById(id);
    if (!existingCountry) {
      throw new NotFoundException(CountryServiceMessages.CountryNotFound);
    }

    try {
      const result = await this.drizzle.db.transaction(async (tx) => {
        const hasInstitutions = await tx.query.institutions.findFirst({
          where: eq(institutions.country_id, id),
        });

        if (hasInstitutions) {
          throw new BadRequestException(
            'Cannot delete country that has associated institutions',
          );
        }

        const deleted = await tx
          .delete(countries)
          .where(eq(countries.id, id))
          .returning();

        if (!deleted.length) {
          throw new NotFoundException(CountryServiceMessages.CountryNotFound);
        }

        return deleted;
      });

      // Invalidate all related caches using utility function
      await invalidateCountryCaches(this.cacheService, id, this.CACHE_PREFIX);

      return result;
    } catch (error) {
      this.logger.error(`Failed to delete country with ID ${id}`, error);
      throw error;
    }
  }
  async updateCountry(
    id: string,
    updateCountryDto: UpdateCountryDto,
  ): Promise<CountryDto[]> {
    if (!id) {
      throw new BadRequestException(CountryServiceMessages.CountryId);
    }

    const { code } = updateCountryDto;

    const name = updateCountryDto.name
      ? formatCountryName(updateCountryDto.name)
      : (await this.findCountryById(id)).name;
    const upperCaseCode = code?.toUpperCase().trim();

    return await this.drizzle.db.transaction(async (tx) => {
      if (name) {
        const existingCountry = await tx
          .select()
          .from(countries)
          .where(
            and(
              sql`LOWER(${countries.name}) = ${name.toLowerCase()}`,
              sql`UPPER(${countries.code}) = ${upperCaseCode}`,
              sql`${countries.id} != ${id}`,
            ),
          )
          .limit(1);

        if (existingCountry.length > 0) {
          throw new ConflictException(
            `Country with name ${name} and code ${code} already exists`,
          );
        }
      }

      const [updatedCountry] = await tx
        .update(countries)
        .set({
          ...(name && { name }),
          ...(upperCaseCode && { code: upperCaseCode }),
        })
        .where(eq(countries.id, id))
        .returning();

      if (!updatedCountry) {
        throw new NotFoundException(CountryServiceMessages.CountryNotFound);
      }

      try {
        await invalidateCountryCaches(this.cacheService, id, this.CACHE_PREFIX);

        const allCountries = await tx
          .select()
          .from(countries)
          .orderBy(asc(countries.name));

        // Update caches for all user roles
        const roles = [
          user_roles.ADMIN,
          user_roles.STUDENT,
          user_roles.STUDENT_ADMIN,
          user_roles.SUPER_ADMIN,
        ];

        await Promise.all(
          roles.map(async (role) => {
            const cacheKey = this.cacheService.generateKey(
              ['all', role],
              this.CACHE_PREFIX,
            );

            const filteredCountries =
              role === user_roles.STUDENT || role === user_roles.STUDENT_ADMIN
                ? allCountries.filter((country) => !country.disabled)
                : allCountries;

            await this.cacheService.set(
              cacheKey,
              filteredCountries,
              this.CACHE_TTL,
            );
          }),
        );

        await this.cacheService.set(
          generateCountryKey(this.cacheService, id, this.CACHE_PREFIX),
          updatedCountry,
          this.CACHE_TTL,
        );
      } catch (error) {
        this.logger.error('Failed to update country caches', error);
      }

      return [updatedCountry];
    });
  }
  async disableCountry(id: string): Promise<CountryDto[]> {
    if (!id) {
      throw new BadRequestException(CountryServiceMessages.CountryId);
    }
    const result = await this.drizzle.db
      .update(countries)
      .set({ disabled: true })
      .where(eq(countries.id, id))
      .returning();
    return result;
  }

  async enableCountry(id: string): Promise<CountryDto[]> {
    if (!id) {
      throw new BadRequestException(CountryServiceMessages.CountryId);
    }
    const result = await this.drizzle.db
      .update(countries)
      .set({ disabled: false })
      .where(eq(countries.id, id))
      .returning();
    return result;
  }
}
