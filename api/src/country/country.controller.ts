import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CountryService } from './country.service';

import { ZodSerializerDto } from 'nestjs-zod';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBadRequestResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { AppClients } from '@app/shared/constants/auth.constants';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import {
  CountryDto,
  countryQueryParamsDto,
  UpdateCountryDto,
} from './country.dto';
import { Country, type User as UserDecoratorType } from '@/db/schema';
import { User } from '@/guards/user.decorator';
import { queryParamsDto } from '@/common/dto/query-params.dto';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { CountryServiceMessages } from './country.message';
import { CountryRoutes } from '@app/shared/constants/country.constants';

@Controller({ version: '1', path: 'country' })
@ApiTags('Country')
export class CountryController {
  constructor(private readonly country: CountryService) {}
  private readonly logger = new Logger(CountryService.name);

  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'country', action: 'create', possession: 'any' })
  @ZodSerializerDto(CountryDto)
  @ApiOperation({
    summary: 'Create new country',
    description: 'Create a new country entry in the system',
  })
  @ApiBody({
    type: CountryDto,
    description: 'Country data to create',
    examples: {
      example1: {
        value: {
          name: 'Ghana',
          code: 'GH',
        },
      },
    },
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully created.',
    type: CountryDto,
  })
  @ApiConflictResponse({
    description: 'Country already exists',
  })
  @CLIENT_TYPE(AppClients.WEB)
  @ApiBearerAuth()
  async addCountry(@Body() countryInput: CountryDto) {
    try {
      return await this.country.addCountry(countryInput);
    } catch (error: any) {
      this.logger.error('Failed to add country', error.stack);
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all countries',
    description:
      'Retrieve a list of all countries with optional filtering and pagination',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter countries by name',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'all',
    required: false,
    description: 'Return all results without pagination',
    type: Boolean,
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    description: 'Field to sort by',
    type: String,
  })
  @ApiQuery({
    name: 'order',
    required: false,
    description: 'Sort order (asc or desc)',
    enum: ['asc', 'desc'],
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all countries.',
    type: [CountryDto],
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  @ApiBearerAuth()
  async getAllCountries(
    @User() user: UserDecoratorType,
    @Query() query: countryQueryParamsDto,
  ) {
    try {
      return await this.country.getAllCountries(
        user,
        query as queryParamsDto & {
          sort: keyof Country;
        },
      );
    } catch (error: any) {
      this.logger.error('Failed to fetch countries', error.stack);
      throw error;
    }
  }

  @Get(CountryRoutes.GET_ACTIVE_COUNTRIES)
  @ApiOperation({
    summary: 'Get active countries',
    description: 'Retrieve a list of all active (enabled) countries',
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all active countries.',
    type: [CountryDto],
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  @ApiBearerAuth()
  async getActiveCountries() {
    try {
      return await this.country.getAllActiveCountries();
    } catch (error: any) {
      this.logger.error('Failed to fetch active countries', error.stack);
      throw error;
    }
  }

  @Get(CountryRoutes.GET_INACTIVE_COUNTRIES)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'country', action: 'read', possession: 'any' })
  @ApiOperation({
    summary: 'Get disabled countries',
    description: 'Retrieve a list of all disabled countries',
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all disabled countries.',
    type: [CountryDto],
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getAllInActiveCountries(): Promise<CountryDto[]> {
    try {
      return await this.country.getDisabledCountries();
    } catch (error: any) {
      this.logger.error('Failed to fetch disabled countries', error.stack);
      throw error;
    }
  }

  @Get(CountryRoutes.GET_COUNTRY_BY_ID)
  @ApiOperation({
    summary: 'Get country by ID',
    description: 'Retrieve a specific country by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Country unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved the country.',
    type: CountryDto,
  })
  @ApiNotFoundResponse({
    description: CountryServiceMessages.CountryNotFound,
  })
  @ApiBadRequestResponse({
    description: CountryServiceMessages.CountryId,
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async findCountryById(@Param('id', new CustomParseUUIDPipe()) id: string) {
    try {
      return await this.country.findCountryById(id);
    } catch (error: any) {
      this.logger.error(`Failed to fetch country with ID ${id}`, error.stack);
      throw error;
    }
  }

  @Delete(CountryRoutes.DELETE_COUNTRY)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'country', action: 'delete', possession: 'any' })
  @ApiOperation({
    summary: 'Delete country',
    description: 'Delete a country by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Country unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiNoContentResponse({
    description: 'Successfully deleted the country.',
  })
  @ApiNotFoundResponse({
    description: CountryServiceMessages.CountryNotFound,
  })
  @ApiBadRequestResponse({
    description: 'Cannot delete country that has associated institutions',
  })
  @CLIENT_TYPE(AppClients.WEB)
  @ApiBearerAuth()
  async deleteCountryById(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<CountryDto[] | undefined> {
    try {
      return await this.country.deleteCountryById(id);
    } catch (error: any) {
      this.logger.error(`Failed to delete country with ID ${id}`, error.stack);
      throw error;
    }
  }

  @Put(CountryRoutes.UPDATE_COUNTRY)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'country', action: 'update', possession: 'any' })
  @ZodSerializerDto(CountryDto)
  @ApiOperation({
    summary: 'Update country',
    description: "Update a country's information by ID",
  })
  @ApiParam({
    name: 'id',
    description: 'Country unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiBody({
    type: UpdateCountryDto,
    description: 'Updated country data',
    examples: {
      example1: {
        value: {
          name: 'Updated Country Name',
          code: 'UC',
        },
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully updated the country.',
    type: CountryDto,
  })
  @ApiNotFoundResponse({
    description: CountryServiceMessages.CountryNotFound,
  })
  @ApiConflictResponse({
    description: 'Country with this name and code already exists',
  })
  @ApiBadRequestResponse({
    description: CountryServiceMessages.CountryId,
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async updateCountry(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() countryInput: UpdateCountryDto,
  ): Promise<CountryDto[] | undefined> {
    try {
      return await this.country.updateCountry(id, countryInput);
    } catch (error: any) {
      this.logger.error(`Failed to update country with ID ${id}`, error.stack);
      throw error;
    }
  }

  @Put(CountryRoutes.DISABLE_COUNTRY)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'country', action: 'update', possession: 'any' })
  @ZodSerializerDto(CountryDto)
  @ApiOperation({
    summary: 'Disable country',
    description: 'Mark a country as disabled',
  })
  @ApiParam({
    name: 'id',
    description: 'Country unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully disabled the country.',
    type: CountryDto,
  })
  @ApiNotFoundResponse({
    description: CountryServiceMessages.CountryNotFound,
  })
  @ApiBadRequestResponse({
    description: CountryServiceMessages.CountryId,
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async disableCountry(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<CountryDto[] | undefined> {
    try {
      return await this.country.disableCountry(id);
    } catch (error: any) {
      this.logger.error(`Failed to disable country with ID ${id}`, error.stack);
      throw error;
    }
  }

  @Put(CountryRoutes.ENABLE_COUNTRY)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'country', action: 'update', possession: 'any' })
  @ZodSerializerDto(CountryDto)
  @ApiOperation({
    summary: 'Enable country',
    description: 'Mark a country as enabled/active',
  })
  @ApiParam({
    name: 'id',
    description: 'Country unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully enabled the country.',
    type: CountryDto,
  })
  @ApiNotFoundResponse({
    description: CountryServiceMessages.CountryNotFound,
  })
  @ApiBadRequestResponse({
    description: CountryServiceMessages.CountryId,
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async enableCountry(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<CountryDto[] | undefined> {
    try {
      return await this.country.enableCountry(id);
    } catch (error: any) {
      this.logger.error(`Failed to enable country with ID ${id}`, error.stack);
      throw error;
    }
  }

  @Get(CountryRoutes.GET_DISABLED_COUNTRIES)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'country', action: 'read', possession: 'any' })
  @ApiOperation({
    summary: 'Get countries by disabled status',
    description:
      'Retrieve countries filtered by their disabled status (true only)',
  })
  @ApiParam({
    name: 'disabled',
    description: 'Disabled status (must be "true")',
    type: 'string',
    example: 'true',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all disabled countries.',
    type: [CountryDto],
  })
  @ApiBadRequestResponse({
    description: 'Invalid parameter. Expected "true" for disabled countries.',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getDisabledCountries(
    @Param('disabled') disabled: string,
  ): Promise<CountryDto[]> {
    try {
      if (disabled !== 'true') {
        throw new BadRequestException(
          'Invalid parameter. Expected "true" for disabled countries.',
        );
      }

      return await this.country.getDisabledCountries();
    } catch (error: any) {
      this.logger.error('Failed to fetch disabled countries', error.stack);
      throw error;
    }
  }
}
