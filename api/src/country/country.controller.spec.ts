import { Test, TestingModule } from '@nestjs/testing';
import { CountryController } from './country.controller';
import { CountryService } from './country.service';

import { Reflector } from '@nestjs/core';
import { RoleGuard } from '../guards/role.guard'; // Adjust the import path as  necessary
describe('CountryController', () => {
  let controller: CountryController;
  let service: CountryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CountryController],
      providers: [
        {
          provide: CountryService,
          useValue: {
            findCountryById: jest.fn(),
            addCountry: jest.fn(),
            updateCountry: jest.fn(),
            getAllCountries: jest.fn(),
            getAllActiveCountries: jest.fn(),
            getDisabledCountries: jest.fn(),
            deleteCountryById: jest.fn(),
            disableCountry: jest.fn(),
            enableCountry: jest.fn(),
          },
        },
        {
          provide: RoleGuard,
          useValue: {
            canActivate: jest.fn().mockReturnValue(true),
          },
        },
        {
          provide: '__roles_builder__',
          useValue: {},
        },
        Reflector,
      ],
    }).compile();

    controller = module.get<CountryController>(CountryController);
    service = module.get<CountryService>(CountryService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findCountryById', () => {
    it('should return a single country', async () => {
      const result: any = { id: '1', name: 'Test Country', code: 'TC' };
      jest.spyOn(service, 'findCountryById').mockResolvedValue(result);
      expect(await controller.findCountryById('1')).toBe(result);
    });
  });

  describe('addCountry', () => {
    it('should create and return a country', async () => {
      const countryDto: any = { name: 'Test Country', code: 'TC' };
      const createdCountry: any = { id: '1', ...countryDto };
      jest.spyOn(service, 'addCountry').mockResolvedValue(createdCountry);
      expect(await controller.addCountry(countryDto)).toEqual(createdCountry);
    });
  });

  describe('updateCountry', () => {
    it('should update and return a country', async () => {
      const countryDto: any = { name: 'Updated Country', code: 'UC' };
      const updatedCountry: any = { id: '1', ...countryDto };

      jest.spyOn(service, 'updateCountry').mockResolvedValue(updatedCountry);
      expect(await controller.updateCountry('1', countryDto)).toEqual(
        updatedCountry,
      );
    });
  });

  describe('getAllCountries', () => {
    it('should return all countries', async () => {
      const result: any = [{ id: '1', name: 'Test Country', code: 'TC' }];
      jest.spyOn(service, 'getAllCountries').mockResolvedValue(result);
      expect(await controller.getAllCountries({} as any, {} as any)).toBe(
        result,
      );
    });
  });

  describe('getActiveCountries', () => {
    it('should return all active countries', async () => {
      const result: any = [{ id: '1', name: 'Test Country', code: 'TC' }];
      jest.spyOn(service, 'getAllActiveCountries').mockResolvedValue(result);
      expect(await controller.getActiveCountries()).toBe(result);
    });
  });

  describe('getAllInActiveCountries', () => {
    it('should return all inactive countries', async () => {
      const result: any = [{ id: '1', name: 'Test Country', code: 'TC' }];
      jest.spyOn(service, 'getDisabledCountries').mockResolvedValue(result);
      expect(await controller.getAllInActiveCountries()).toBe(result);
    });
  });

  describe('deleteCountryById', () => {
    it('should delete a country by ID', async () => {
      const result: any = [{ id: '1', name: 'Test Country', code: 'TC' }];
      jest.spyOn(service, 'deleteCountryById').mockResolvedValue(result);
      expect(await controller.deleteCountryById('1')).toBe(result);
    });
  });

  describe('disableCountry', () => {
    it('should disable a country', async () => {
      const result: any = [
        { id: '1', name: 'Test Country', code: 'TC', disabled: true },
      ];
      jest.spyOn(service, 'disableCountry').mockResolvedValue(result);
      expect(await controller.disableCountry('1')).toBe(result);
    });
  });

  describe('enableCountry', () => {
    it('should enable a country', async () => {
      const result: any = [
        { id: '1', name: 'Test Country', code: 'TC', disabled: false },
      ];
      jest.spyOn(service, 'enableCountry').mockResolvedValue(result);
      expect(await controller.enableCountry('1')).toBe(result);
    });
  });

  describe('getDisabledCountries', () => {
    it('should return all disabled countries', async () => {
      const result: any = [{ id: '1', name: 'Test Country', code: 'TC' }];
      jest.spyOn(service, 'getDisabledCountries').mockResolvedValue(result);
      expect(await controller.getDisabledCountries('true')).toBe(result);
    });
  });
});
