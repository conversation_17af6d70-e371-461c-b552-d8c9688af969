import { Test, TestingModule } from '@nestjs/testing';
import { CountryService } from './country.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CountryDto } from './country.dto';
import { CacheService } from '@app/shared/redis/cache.service';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { User, user_roles } from '@/db/schema';
import { Country } from '@/db/schema/countries';
import { queryParamsDto } from '@/common/dto/query-params.dto';

describe('CountryService', () => {
  let service: CountryService;
  let drizzleService: DrizzleService;

  beforeEach(async () => {
    // Create a more realistic mock of the DrizzleService
    const mockDrizzleService = {
      db: {
        transaction: jest.fn((callback) => callback(mockDrizzleService.db)),
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        $count: jest.fn().mockResolvedValue(2),
        execute: jest.fn(),
        query: {
          countries: {
            findFirst: jest.fn(),
          },
          institutions: {
            findFirst: jest.fn(),
          },
        },
      },
    } as any;

    const mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      generateKey: jest
        .fn()
        .mockImplementation(
          (keys, prefix) =>
            `${prefix}:${Array.isArray(keys) ? keys.join(':') : keys}`,
        ),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CountryService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<CountryService>(CountryService);
    drizzleService = module.get<DrizzleService>(DrizzleService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addCountry', () => {
    it('should successfully add a new country', async () => {
      const countryDto: CountryDto = {
        name: 'Test Country',
        code: 'TC',
      };

      const mockNewCountry = { id: '1', ...countryDto };

      (drizzleService.db.select as jest.Mock).mockImplementation(
        () =>
          ({
            from: () => ({
              where: () => ({
                limit: () => [],
              }),
            }),
          }) as any,
      );

      (drizzleService.db.insert as jest.Mock).mockImplementation(
        () =>
          ({
            values: () => ({
              returning: () => [mockNewCountry],
            }),
          }) as any,
      );

      const result = await service.addCountry(countryDto);
      expect(result).toEqual(mockNewCountry);
    });

    it('should throw ConflictException if country already exists', async () => {
      const countryDto: CountryDto = {
        name: 'Test Country',
        code: 'TC',
      };

      (drizzleService.db.select as jest.Mock).mockImplementation(
        () =>
          ({
            from: () => ({
              where: () => ({
                limit: () => [{ id: '1', ...countryDto }],
              }),
            }),
          }) as any,
      );

      await expect(service.addCountry(countryDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('getAllCountries', () => {
    it('should return all countries with pagination', async () => {
      const user: User = {
        id: '1',
        role: user_roles.ADMIN,
      } as User;

      const queryParams: queryParamsDto & {
        all?: boolean;
        sort: keyof Country;
      } = {
        sort: 'name',
        order: 'asc',
        limit: 10,
        page: 1,
        search: '',
        all: false,
      };

      const mockCountries = [
        { id: '1', name: 'Country A', code: 'CA' },
        { id: '2', name: 'Country B', code: 'CB' },
      ];

      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => ({
                orderBy: () => ({
                  limit: () => ({
                    offset: () => mockCountries,
                  }),
                }),
              }),
            }),
          }) as any,
      );

      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => [{ count: '2' }],
            }),
          }) as any,
      );

      const result = await service.getAllCountries(user, queryParams);
      expect(result).toEqual({
        data: mockCountries,
        total: 2,
      });
    });
  });

  describe('findCountryById', () => {
    it('should return a country when found', async () => {
      const mockCountry = {
        id: '1',
        name: 'Test Country',
        code: 'TC',
      };

      (
        drizzleService.db.query.countries.findFirst as jest.Mock
      ).mockResolvedValue(mockCountry);

      const result = await service.findCountryById('1');
      expect(result).toEqual(mockCountry);
    });

    it('should throw NotFoundException when country not found', async () => {
      (
        drizzleService.db.query.countries.findFirst as jest.Mock
      ).mockResolvedValue(null);

      await expect(service.findCountryById('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
