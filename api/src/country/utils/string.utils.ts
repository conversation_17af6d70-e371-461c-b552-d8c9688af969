/**
 * Formats a country name by:
 * 1. Trimming whitespace
 * 2. Capitalizing first letter of each word
 * 3. Handling special cases (articles, prepositions, etc.)
 * 4. Handling specific country name conventions
 */
export const formatCountryName = (name: string): string => {
  if (!name) return '';

  // Trim and convert to lowercase
  let formattedName = name.trim().toLowerCase();

  // Special cases for country names
  const specialCases: { [key: string]: string } = {
    usa: 'United States of America',
    uk: 'United Kingdom',
    uae: 'United Arab Emirates',
    drc: 'Democratic Republic of the Congo',
    car: 'Central African Republic',
  };

  // Check for special cases first
  const lowercaseName = formattedName.toLowerCase();
  if (specialCases[lowercaseName]) {
    return specialCases[lowercaseName];
  }

  // Words that should remain lowercase unless at the start
  const lowercaseWords = new Set([
    'and',
    'of',
    'the',
    'in',
    'on',
    'at',
    'for',
    'to',
    'with',
  ]);

  // Split into words and capitalize
  const words = formattedName.split(' ');

  const capitalizedWords = words.map((word, index) => {
    // Always capitalize first word
    if (index === 0) {
      return word.charAt(0).toUpperCase() + word.slice(1);
    }

    // Check if word should remain lowercase
    if (lowercaseWords.has(word)) {
      return word;
    }

    // Capitalize word
    return word.charAt(0).toUpperCase() + word.slice(1);
  });

  // Join words back together
  formattedName = capitalizedWords.join(' ');

  // Handle hyphenated names
  formattedName = formattedName.replace(/(?:^|\s|-)\w/g, (letter) =>
    letter.toUpperCase(),
  );

  return formattedName;
};
