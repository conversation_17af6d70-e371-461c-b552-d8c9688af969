import { Logger } from '@nestjs/common';
import { CacheService } from '@app/shared/redis/cache.service';
import { user_roles } from '@/db/schema';

const logger = new Logger('CountryCache');

/**
 * Generates a cache key for a specific country
 */
export const generateCountryKey = (
  cacheService: CacheService,
  id: string,
  prefix: string,
): string => {
  return cacheService.generateResourceKey(id, prefix);
};

/**
 * Invalidates the general country cache
 */
export const invalidateCountryCache = async (
  cacheService: CacheService,
  prefix: string,
): Promise<void> => {
  try {
    await cacheService.del(`${prefix}:all`);
    logger.debug('Country list cache invalidated');
  } catch (error) {
    logger.warn('Failed to invalidate country cache', error);
  }
};

/**
 * Invalidates all country-related caches including role-specific and status-specific caches
 */
export const invalidateCountryCaches = async (
  cacheService: CacheService,
  id: string,
  prefix: string,
): Promise<void> => {
  const keysToInvalidate = [
    // Role-specific list caches
    `all:${user_roles.ADMIN}`,
    `all:${user_roles.STUDENT}`,
    `all:${user_roles.STUDENT_ADMIN}`,
    // Status-specific caches
    'active',
    'disabled',
    // Specific country cache
    id,
  ];

  await cacheService.invalidateMany(keysToInvalidate, prefix);
};
