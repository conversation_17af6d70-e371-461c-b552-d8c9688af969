import {
  DynamicModule,
  Global,
  Module,
  Logger,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  SSMClient,
  GetParametersByPathCommand,
  Parameter,
  GetParametersByPathCommandOutput,
} from '@aws-sdk/client-ssm';
import { setTimeout } from 'timers/promises';

interface ParameterStoreModuleOptions {
  prefix: string;
  cacheTTL: number;
  retryAttempts: number;
  retryDelay: number;
  enabled: boolean;
}

@Global()
@Module({})
export class ParameterStoreModule {
  private static logger = new Logger('ParameterStoreModule');

  static forRootAsync(): DynamicModule {
    return {
      module: ParameterStoreModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: 'PARAMETER_STORE_OPTIONS',
          inject: [ConfigService],
          useFactory: (
            configService: ConfigService,
          ): ParameterStoreModuleOptions => {
            const enabled =
              configService.get<boolean>('AWS_PARAMETER_STORE_ENABLED') ??
              false;
            const prefix =
              configService.get<string>('AWS_PARAMETER_STORE_PREFIX') ?? '';
            const cacheTTL =
              configService.get<number>('AWS_PARAMETER_STORE_CACHE_TTL') ?? 300;
            const retryAttempts =
              configService.get<number>('AWS_PARAMETER_STORE_RETRY_ATTEMPTS') ??
              3;
            const retryDelay =
              configService.get<number>('AWS_PARAMETER_STORE_RETRY_DELAY') ??
              1000;

            return {
              prefix,
              cacheTTL,
              retryAttempts,
              retryDelay,
              enabled,
            };
          },
        },
        {
          provide: SSMClient,
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            return new SSMClient({
              region: configService.get('AWS_REGION') ?? 'us-east-1',
            });
          },
        },
        ParameterStoreService,
      ],
      exports: [ParameterStoreService],
    };
  }
}

@Injectable()
export class ParameterStoreService implements OnModuleInit {
  private readonly logger = new Logger(ParameterStoreService.name);
  private cache: Map<string, string> = new Map();
  private cacheExpiry = 0;

  constructor(
    private readonly ssmClient: SSMClient,
    private readonly configService: ConfigService,
    @Inject('PARAMETER_STORE_OPTIONS')
    private readonly options: ParameterStoreModuleOptions,
  ) {}

  async onModuleInit() {
    if (
      this.options.enabled &&
      this.configService.get('NODE_ENV') === 'production'
    ) {
      this.logger.log('Loading parameters from AWS Parameter Store...');
      try {
        await this.loadParametersWithRetry();
      } catch (error: unknown) {
        this.logger.error(
          'Failed to load parameters from Parameter Store',
          error as Error,
        );
      }
    } else {
      this.logger.log(
        'Parameter Store loading disabled or not in production environment',
      );
    }
  }

  private async loadParametersWithRetry() {
    let attempts = 0;
    while (attempts < this.options.retryAttempts) {
      try {
        await this.loadParameters();
        return;
      } catch (error: unknown) {
        attempts++;
        const message = error instanceof Error ? error.message : String(error);
        this.logger.warn(
          `Attempt ${attempts} to load parameters failed: ${message}`,
        );
        if (attempts < this.options.retryAttempts) {
          await setTimeout(this.options.retryDelay);
        } else {
          throw error;
        }
      }
    }
  }

  private async loadParameters() {
    const prefix = this.options.prefix;
    const parameters: Parameter[] = [];
    let nextToken: string | undefined = undefined;

    do {
      const command = new GetParametersByPathCommand({
        Path: prefix,
        Recursive: true,
        WithDecryption: true,
        NextToken: nextToken,
      });

      const response: GetParametersByPathCommandOutput =
        await this.ssmClient.send(command);

      if (response.Parameters) {
        parameters.push(...response.Parameters);
      }

      nextToken = response.NextToken;
    } while (nextToken);

    this.cache.clear();
    parameters.forEach((param: Parameter) => {
      if (param.Name && param.Value) {
        const key = param.Name.replace(`${prefix}/`, '');
        this.cache.set(key, param.Value);
      }
    });

    this.cacheExpiry = Date.now() + this.options.cacheTTL * 1000;
    this.logger.log(
      `Loaded ${parameters.length} parameters from Parameter Store`,
    );
  }

  get(key: string): string | undefined {
    if (!this.options.enabled) {
      return undefined;
    }

    if (Date.now() > this.cacheExpiry) {
      this.logger.warn('Parameter cache expired, consider reloading');
      // Optionally trigger reload asynchronously
    }

    return this.cache.get(key);
  }
}
