import { Logger } from '@nestjs/common';
import { ParameterStoreConfigProvider } from './parameter-store-config.provider';
import * as dotenv from 'dotenv';

type Environment = 'production' | 'staging' | 'development' | 'local';

interface ConfigurationResult {
  source: 'parameter-store' | 'env-file';
  parametersLoaded: number;
  environment: string;
}

interface ParameterStoreError extends Error {
  code?: string;
  Code?: string;
}

export async function bootstrapConfig(): Promise<ConfigurationResult> {
  const logger = new Logger('BootstrapConfig');

  // Load .env file to get NODE_ENV if not already set
  if (!process.env.NODE_ENV) {
    dotenv.config();
  }

  const nodeEnv = (process.env.NODE_ENV || 'local') as Environment;
  const isLocal = nodeEnv === 'local';

  logger.log(`Environment: ${nodeEnv}`);

  if (isLocal) {
    return loadFromEnvFile(nodeEnv, logger);
  }

  return loadFromParameterStore(nodeEnv, logger);
}

/**
 * Load configuration from .env file for local environment
 */
async function loadFromEnvFile(
  environment: Environment,
  logger: Logger,
): Promise<ConfigurationResult> {
  dotenv.config();
  validateEnvironmentVariables(environment);

  logger.log('Configuration loaded from .env file');

  return {
    source: 'env-file',
    parametersLoaded: 0,
    environment,
  };
}

/**
 * Load configuration from AWS Parameter Store for production, staging, and development
 */
async function loadFromParameterStore(
  environment: Environment,
  logger: Logger,
): Promise<ConfigurationResult> {
  try {
    const parameterStoreConfig =
      await ParameterStoreConfigProvider.loadParametersAndMergeWithEnv();
    Object.assign(process.env, parameterStoreConfig);

    const parametersCount = Object.keys(parameterStoreConfig).length;
    validateEnvironmentVariables(environment);

    logger.log(
      `Configuration loaded from Parameter Store (${parametersCount} parameters)`,
    );

    return {
      source: 'parameter-store',
      parametersLoaded: parametersCount,
      environment,
    };
  } catch (error: unknown) {
    const parameterStoreError = error as ParameterStoreError;

    logger.error('Parameter Store loading failed', {
      environment,
      errorType: parameterStoreError?.name || 'Unknown',
      errorMessage: parameterStoreError?.message || 'No message available',
      errorCode:
        parameterStoreError?.code ||
        parameterStoreError?.Code ||
        'No code available',
    });

    // Parameter Store is mandatory for all non-local environments
    logger.error(
      `Application startup ABORTED - Parameter Store is mandatory for ${environment} environment`,
    );
    logger.error('No fallback to .env files allowed for deployed environments');
    process.exit(1);
  }
}

/**
 * Validate that critical environment variables are set
 */
function validateEnvironmentVariables(environment: string): void {
  const requiredVars = ['PORT', 'NODE_ENV'];

  // Always validate DATABASE_URL regardless of environment
  requiredVars.push('DATABASE_URL');

  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      throw new Error(
        `${varName} environment variable is required but not set (Environment: ${environment})`,
      );
    }
  }
}
