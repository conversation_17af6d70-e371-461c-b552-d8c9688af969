import { z } from 'zod';

/**
 * Helper function to create a boolean schema that properly handles string values from query parameters
 * Specifically handles the case where the string "false" should be converted to boolean false
 *
 * @param defaultValue - Optional default value if the input is undefined (if not provided, field will be optional)
 * @returns A Zod schema that correctly processes string values to booleans
 */
export const createQueryBooleanSchema = (defaultValue?: boolean) => {
  const processor = z.preprocess((val) => {
    // Handle already boolean values directly
    if (typeof val === 'boolean') return val;

    // Handle string values
    if (typeof val === 'string') {
      if (val === 'true') return true;
      if (val === 'false') return false;
    }

    // Handle undefined/null for optional fields
    if (val === undefined || val === null) {
      return defaultValue !== undefined ? defaultValue : undefined;
    }

    return Boolean(val);
  }, z.boolean());

  return defaultValue !== undefined
    ? processor.default(defaultValue)
    : processor.optional();
};
