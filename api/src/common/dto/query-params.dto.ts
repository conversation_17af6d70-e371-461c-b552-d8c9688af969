import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

export const querySchema = z.object({
  page: z.coerce.number().positive().optional().default(1),
  limit: z.coerce.number().positive().optional().default(10),
  sort: z.string().optional().default('id'),
  order: z.enum(['asc', 'desc']).optional().default('asc'),
  search: z.string().optional().default(''),
  all: z
    .union([z.boolean(), z.enum(['true', 'false'])])
    .optional()
    .default('true')
    .transform((v) => v === 'true'),
});

export const periodEnum = z.enum([
  'quarterly',
  'daily',
  'weekly',
  'monthly',
  'yearly',
  'all-time',
  'first-quarter',
  'second-quarter',
  'third-quarter',
  'fourth-quarter',
  'last-week',
  'last-month',
  'current-quarter',
]);

export const periodSchema = z.object({
  period: periodEnum.default('weekly'),
});

export class periodQueryDto extends createZodDto(periodSchema) {}
export class queryParamsDto extends createZodDto(querySchema) {}
