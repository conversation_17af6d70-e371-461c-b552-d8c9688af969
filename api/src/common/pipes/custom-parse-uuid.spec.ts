import { CustomParseUUIDPipe } from './custom-parse-uuid';
import { ArgumentMetadata, BadRequestException } from '@nestjs/common';

describe('CustomParseUUIDPipe', () => {
  let pipe: CustomParseUUIDPipe;
  const metadata: ArgumentMetadata = {
    type: 'param',
    metatype: String,
    data: 'id',
  };

  beforeEach(() => {
    pipe = new CustomParseUUIDPipe();
  });

  it('should validate a valid UUID', async () => {
    const validUuid = '12d8595f-0490-42df-ad26-2a6b279d0c20';
    const result = await pipe.transform(validUuid, metadata);
    expect(result).toBe(validUuid);
  });

  it('should throw an error for an invalid UUID', async () => {
    const invalidUuid = 'not-a-uuid';
    await expect(pipe.transform(invalidUuid, metadata)).rejects.toThrow(
      BadRequestException,
    );
  });

  it('should throw an error for an empty string when not optional', async () => {
    const emptyValue = '';
    await expect(pipe.transform(emptyValue, metadata)).rejects.toThrow(
      BadRequestException,
    );
  });

  describe('with optional flag', () => {
    let optionalPipe: CustomParseUUIDPipe;

    beforeEach(() => {
      optionalPipe = new CustomParseUUIDPipe({ optional: true });
    });

    it('should handle empty string when optional', async () => {
      const emptyValue = '';
      const result = await optionalPipe.transform(emptyValue, metadata);
      expect(result).toBe(emptyValue);
    });

    it('should handle null values when optional', async () => {
      const nullValue = null;
      const result = await optionalPipe.transform(nullValue as any, metadata);
      expect(result).toBe(nullValue);
    });

    it('should handle undefined values when optional', async () => {
      const undefinedValue = undefined;
      const result = await optionalPipe.transform(
        undefinedValue as any,
        metadata,
      );
      expect(result).toBe(undefinedValue);
    });

    it('should still validate valid UUIDs', async () => {
      const validUuid = '12d8595f-0490-42df-ad26-2a6b279d0c20';
      const result = await optionalPipe.transform(validUuid, metadata);
      expect(result).toBe(validUuid);
    });

    it('should still reject invalid UUIDs', async () => {
      const invalidUuid = 'not-a-uuid';
      await expect(
        optionalPipe.transform(invalidUuid, metadata),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('with version option', () => {
    it('should accept version 4 UUIDs with version 4 specified', async () => {
      const versionPipe = new CustomParseUUIDPipe({ version: '4' });
      const validV4Uuid = '12d8595f-0490-42df-ad26-2a6b279d0c20';
      const result = await versionPipe.transform(validV4Uuid, metadata);
      expect(result).toBe(validV4Uuid);
    });

    it('should reject non-version 4 UUIDs with version 4 specified', async () => {
      // This is a version 3 UUID
      const versionPipe = new CustomParseUUIDPipe({ version: '4' });
      const v3Uuid = '6fa459ea-ee8a-3ca4-894e-db77e160355e';
      await expect(versionPipe.transform(v3Uuid, metadata)).rejects.toThrow(
        BadRequestException,
      );
    });
  });
});
