import {
  allowedMimeTypes,
  fileSignatures,
} from '@app/shared/constants/mimetypes.constants';
import type { PipeTransform } from '@nestjs/common';
import { BadRequestException, Injectable } from '@nestjs/common';

@Injectable()
export class ValidateFilePipe implements PipeTransform {
  transform(file: Express.Multer.File) {
    if (!file || !file.mimetype || !file.buffer) {
      throw new BadRequestException('Invalid file');
    }

    const mimetype = file.mimetype;
    if (!this.isAllowedMimeType(mimetype)) {
      throw new BadRequestException('Invalid file extension');
    }

    if (!this.hasValidContent(file.buffer, mimetype)) {
      throw new BadRequestException('Invalid file content');
    }

    return file;
  }

  private isAllowedMimeType(mimetype: string): boolean {
    return allowedMimeTypes.includes(mimetype);
  }

  private hasValidContent(buffer: Buffer, mimetype: string): boolean {
    const fileSignature = buffer
      .toString('hex', 0, 8)
      .toUpperCase()
      .substring(0, 8);

    const validSignatures =
      fileSignatures[mimetype as keyof typeof fileSignatures];
    return validSignatures?.includes(fileSignature);
  }
}
