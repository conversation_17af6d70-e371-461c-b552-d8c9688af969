import { Logger } from '@nestjs/common';
import { CacheService } from '@app/shared/redis/cache.service';
import { user_roles } from '@/db/schema';

const logger = new Logger('InstitutionCache');

/**
 * Generates a cache key for a specific institution
 */
export const generateInstitutionKey = (
  cacheService: CacheService,
  id: string,
  prefix: string,
): string => {
  return cacheService.generateResourceKey(id, prefix);
};

/**
 * Invalidates the general institution cache
 */
export const invalidateInstitutionCache = async (
  cacheService: CacheService,
  prefix: string,
): Promise<void> => {
  try {
    await cacheService.del(`${prefix}:all`);
    logger.debug('Institution list cache invalidated');
  } catch (error) {
    logger.warn('Failed to invalidate institution cache', error);
  }
};

/**
 * Invalidates all institution-related caches including role-specific and status-specific caches
 */
export const invalidateInstitutionCaches = async (
  cacheService: CacheService,
  id: string,
  prefix: string,
): Promise<void> => {
  const keysToInvalidate = [
    // Role-specific list caches
    `all:${user_roles.ADMIN}`,
    `all:${user_roles.STUDENT}`,
    `all:${user_roles.STUDENT_ADMIN}`,
    `all:${user_roles.SUPER_ADMIN}`,
    // Status-specific caches
    'status:true',
    'status:false',
    // Specific institution cache
    id,
  ];

  await cacheService.invalidateMany(keysToInvalidate, prefix);
};
