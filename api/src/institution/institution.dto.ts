import { z } from 'nestjs-zod/z';
import { institutionKeys, institutions } from '@/db/schema/institution';
import { createZodDto } from 'nestjs-zod';
import { createInsertSchema } from 'drizzle-zod';
import { querySchema } from '@/common/dto/query-params.dto';

const domainRegex =
  /^(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.(?!-)[A-Za-z0-9-]{1,63}(?<!-))+$/;

const baseInsertInstitution = createInsertSchema(institutions);
const insertInstitution = baseInsertInstitution.extend({
  name: z.string().trim().min(3),
  country_id: z.string().uuid('Invalid country id'),
  location: z.string().trim().min(3),
  address: z.string().trim().min(1),
  city: z.string().trim().min(3),
  state: z.string().trim().min(3),
  domain: z
    .string()
    .regex(domainRegex, {
      message: 'Invalid domain',
    })
    .optional(),
});

const updateInstitution = baseInsertInstitution.extend({
  name: z.string().trim().min(3).optional(),
  country_id: z.string().uuid('Invalid country ID').optional(),
  location: z.string().trim().min(3).optional(),
  address: z.string().trim().min(1).optional(),
  city: z.string().trim().min(3).optional(),
  state: z.string().trim().min(3).optional(),
  domain: z
    .string()
    .regex(domainRegex, {
      message: 'Invalid domain',
    })
    .optional(),
});

const institutionQueryParamsSchema = querySchema.extend({
  all: z
    .enum(['true', 'false'])
    .optional()
    .default('true')
    .transform((v) => (v === 'true' ? true : false)),
  sort: z.enum(institutionKeys).optional().default('id'),
});

export class InstitutionDto extends createZodDto(insertInstitution) {}
export class UpdateInstitutionDto extends createZodDto(updateInstitution) {}
export type InstitutionParams = z.infer<typeof insertInstitution>;
export class institutionQueryParamsDto extends createZodDto(
  institutionQueryParamsSchema,
) {}
