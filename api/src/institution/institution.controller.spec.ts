import { Test, TestingModule } from '@nestjs/testing';
import { InstitutionController } from './institution.controller';
import { InstitutionService } from './institution.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { DATABASE_OPTIONS } from '@app/shared/drizzle/drizzle.module-definition';
import { ROLES_BUILDER_TOKEN } from 'nest-access-control';
import { user_states, user_roles } from '@/db/schema';

describe('InstitutionController', () => {
  let institutionController: InstitutionController;
  let institutionService: InstitutionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InstitutionController],
      providers: [
        {
          provide: 'EnvConfig',
          useValue: {
            DATABASE_URL: 'postgres://admin:pass@localhost:5432/db',
            NODE_ENV: 'test',
          },
        },
        {
          provide: InstitutionService,
          useValue: {
            getInstitutionById: jest.fn(),
            removeInstitution: jest.fn(),
            addInstitution: jest.fn(),
            getAllInstitutions: jest.fn(),
            updateInstitution: jest.fn(),
          },
        },
        {
          provide: DATABASE_OPTIONS,
          useValue: {
            connectionString: 'mockConnectionString',
            max: 10,
            debug: true,
          },
        },
        { provide: ROLES_BUILDER_TOKEN, useValue: {} },
        {
          provide: DrizzleService,
          useValue: {
            db: {}, // Mock the db property
            close: jest.fn(), // Mock the close method
          },
        },
      ],
    }).compile();

    institutionController = module.get<InstitutionController>(
      InstitutionController,
    );
    institutionService = module.get<InstitutionService>(InstitutionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(institutionController).toBeDefined();
  });

  it('should be defined', () => {
    expect(institutionService).toBeDefined();
  });

  describe('addInstitution', () => {
    it('should add a new institution', async () => {
      const institutionDto = {
        name: 'Test Institution',
        location: 'Test Location',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
        country_id: '1',
        domain: undefined,
      };

      const expectedResult = {
        name: 'Test Institution',
        id: '1',
        location: 'Test Location',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
        domain: null,
        country_id: '1',
        disabled: false,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
        country: null,
      };

      jest
        .spyOn(institutionService, 'addInstitution')
        .mockResolvedValue(expectedResult);

      const result = await institutionController.addInstitution(institutionDto);
      expect(result).toEqual(expectedResult);
      expect(institutionService.addInstitution).toHaveBeenCalledWith(
        institutionDto,
      );
    });
  });

  describe('getAllInstitutions', () => {
    it('should return all institutions', async () => {
      const expectedResult = [
        {
          name: 'Institution 1',
          id: '1',
          location: 'Location 1',
          address: 'Address 1',
          city: 'City 1',
          state: 'State 1',
          country_id: '1',
          disabled: false,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-02T00:00:00Z',
          country: null,
        },
        {
          name: 'Institution 2',
          id: '2',
          location: 'Location 2',
          address: 'Address 2',
          city: 'City 2',
          state: 'State 2',
          country_id: '2',
          disabled: false,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-02T00:00:00Z',
          country: null,
        },
      ];

      jest
        .spyOn(institutionService, 'getAllInstitutions')
        .mockResolvedValue(expectedResult);

      // Create a mock user for the @User() decorator
      const mockUser = {
        id: 'user-123',
        state: user_states.ACTIVE,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
        email: '<EMAIL>',
        role: user_roles.ADMIN,
        profile_pic_url: null,
        deleted: false,
        deleted_at: null,
      };

      const result = await institutionController.getAllInstitutions(mockUser, {
        search: '',
        sort: 'name',
        limit: 10,
        page: 1,
        all: true,
        order: 'asc',
      });
      expect(result).toEqual(expectedResult);
      expect(institutionService.getAllInstitutions).toHaveBeenCalled();
    });
  });

  describe('getInstitutionById', () => {
    it('should return an institution by ID', async () => {
      const institutionId = '1';
      const expectedResult = {
        name: 'Test Institution',
        id: '1',
        location: 'Test Location',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
        country_id: '1',
        disabled: false,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
        country: null,
      };

      jest
        .spyOn(institutionService, 'getInstitutionById')
        .mockResolvedValue(expectedResult);

      const result =
        await institutionController.getInstitutionById(institutionId);
      expect(result).toEqual(expectedResult);
      expect(institutionService.getInstitutionById).toHaveBeenCalledWith(
        institutionId,
      );
    });
  });

  describe('updateInstitution', () => {
    it('should update an institution by ID', async () => {
      const institutionId = '1';
      const institutionDto = {
        name: 'Updated Institution',
        location: 'Updated Location',
        address: 'Updated Address',
        city: 'Updated City',
        state: 'Updated State',
        country_id: '1',
      };
      const expectedResult = {
        name: 'Updated Institution',
        id: '1',
        location: 'Updated Location',
        address: 'Updated Address',
        city: 'Updated City',
        state: 'Updated State',
        domain: null,
        country_id: '1',
        disabled: false,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
        country: null,
      };

      jest
        .spyOn(institutionService, 'updateInstitution')
        .mockResolvedValue(expectedResult);

      const result = await institutionController.updateInstitution(
        institutionId,
        institutionDto,
      );
      expect(result).toEqual(expectedResult);
      expect(institutionService.updateInstitution).toHaveBeenCalledWith(
        institutionId,
        institutionDto,
      );
    });
  });

  describe('removeInstitution', () => {
    it('should remove an institution by ID', async () => {
      const institutionId = '1';

      // The service returns an Institution object, but the controller doesn't return anything
      const deletedInstitution = {
        id: '1',
        name: 'Test Institution',
        location: 'Test Location',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
        domain: null,
        country_id: '1',
        disabled: false,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
      };

      jest
        .spyOn(institutionService, 'removeInstitution')
        .mockResolvedValue(deletedInstitution);

      await institutionController.removeInstitution(institutionId);
      expect(institutionService.removeInstitution).toHaveBeenCalledWith(
        institutionId,
      );
    });
  });
});
