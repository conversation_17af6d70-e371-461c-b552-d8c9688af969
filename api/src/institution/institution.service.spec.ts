import { Test, TestingModule } from '@nestjs/testing';
import { InstitutionService } from './institution.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CacheService } from '@app/shared/redis/cache.service';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { InstitutionParams } from './institution.dto';
import { User, user_roles } from '@/db/schema';
import { Institution } from '@/db/schema/institution';

describe('InstitutionService', () => {
  let service: InstitutionService;
  let drizzleService: DrizzleService;

  beforeEach(async () => {
    // Create a more realistic mock of the DrizzleService
    const mockDrizzleService = {
      db: {
        transaction: jest.fn((callback) => callback(mockDrizzleService.db)),
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        $count: jest.fn().mockResolvedValue(2),
        execute: jest.fn(),
        query: {
          countries: {
            findFirst: jest.fn(),
          },
          institutions: {
            findFirst: jest.fn(),
          },
        },
      },
    } as any;

    const mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      invalidateMany: jest.fn(),
      generateKey: jest
        .fn()
        .mockImplementation(
          (keys, prefix) =>
            `${prefix}:${Array.isArray(keys) ? keys.join(':') : keys}`,
        ),
      generateResourceKey: jest
        .fn()
        .mockImplementation((id, prefix) => `${prefix}:${id}`),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InstitutionService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<InstitutionService>(InstitutionService);
    drizzleService = module.get<DrizzleService>(DrizzleService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addInstitution', () => {
    it('should successfully add a new institution', async () => {
      const institutionDto: InstitutionParams = {
        name: 'Test University',
        country_id: '1',
        location: 'Test Location',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
      };

      const mockNewInstitution = { id: '1', ...institutionDto };
      const mockInstitutionWithCountry = {
        ...mockNewInstitution,
        country: 'Test Country',
      };

      // Mock country validation
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => ({
                limit: () => [{ id: '1', name: 'Test Country' }],
              }),
            }),
          }) as any,
      );

      // Mock institution name check
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => ({
                limit: () => [],
              }),
            }),
          }) as any,
      );

      // Mock insert
      (drizzleService.db.insert as jest.Mock).mockImplementation(
        () =>
          ({
            values: () => ({
              returning: () => [mockNewInstitution],
            }),
          }) as any,
      );

      // Mock select with join for institution with country
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              leftJoin: () => ({
                where: () => ({
                  limit: () => [mockInstitutionWithCountry],
                }),
              }),
            }),
          }) as any,
      );

      // Mock select all institutions
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              leftJoin: () => ({
                orderBy: () => [mockInstitutionWithCountry],
              }),
            }),
          }) as any,
      );

      const result = await service.addInstitution(institutionDto);
      expect(result).toEqual(mockInstitutionWithCountry);
    });

    it('should throw ConflictException if institution name already exists', async () => {
      const institutionDto: InstitutionParams = {
        name: 'Test University',
        country_id: '1',
        location: 'Test Location',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
      };

      // Mock country validation
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => ({
                limit: () => [{ id: '1', name: 'Test Country' }],
              }),
            }),
          }) as any,
      );

      // Mock institution name check - return existing institution
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => ({
                limit: () => [{ id: '2', name: 'Test University' }],
              }),
            }),
          }) as any,
      );

      await expect(service.addInstitution(institutionDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('getInstitutionById', () => {
    it('should return an institution when found', async () => {
      const mockInstitution = {
        id: '1',
        name: 'Test University',
        country_id: '1',
        country: 'Test Country',
        location: 'Test Location',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
      };

      // Mock cache miss
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              leftJoin: () => ({
                where: () => ({
                  limit: () => [mockInstitution],
                }),
              }),
            }),
          }) as any,
      );

      const result = await service.getInstitutionById('1');
      expect(result).toEqual(mockInstitution);
    });

    it('should throw NotFoundException when institution not found', async () => {
      // Mock cache miss
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              leftJoin: () => ({
                where: () => ({
                  limit: () => [],
                }),
              }),
            }),
          }) as any,
      );

      await expect(service.getInstitutionById('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('getAllInstitutions', () => {
    it('should return all institutions with pagination', async () => {
      const user: User = {
        id: '1',
        role: user_roles.ADMIN,
      } as User;

      const queryParams = {
        sort: 'name' as keyof Institution,
        order: 'asc' as 'asc' | 'desc',
        limit: 10,
        page: 1,
        search: '',
        all: false,
      };

      const mockInstitutions = [
        { id: '1', name: 'University A', country: 'Country A' },
        { id: '2', name: 'University B', country: 'Country B' },
      ];

      // Mock select with pagination
      (drizzleService.db.select as jest.Mock).mockImplementationOnce(
        () =>
          ({
            from: () => ({
              where: () => ({
                leftJoin: () => ({
                  orderBy: () => ({
                    limit: () => ({
                      offset: () => mockInstitutions,
                    }),
                  }),
                }),
              }),
            }),
          }) as any,
      );

      const result = await service.getAllInstitutions(user, queryParams);
      expect(result).toEqual({
        data: mockInstitutions,
        total: 2,
      });
    });
  });
});
