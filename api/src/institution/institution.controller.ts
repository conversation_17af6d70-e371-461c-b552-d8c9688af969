import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  InstitutionDto,
  institutionQueryParamsDto,
  UpdateInstitutionDto,
} from './institution.dto';
import { InstitutionService } from './institution.service';

import { ZodSerializerDto } from 'nestjs-zod';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { UseRoles } from 'nest-access-control';
import { RoleGuard } from '@/guards/role.guard';
import { AppClients } from '@app/shared/constants/auth.constants';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { User } from '@/guards/user.decorator';
import { Institution, type User as UserDecoratorType } from '@/db/schema';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { InstitutionRoutes } from '@app/shared/constants/institution.constants';

@ApiTags('Institutions')
@Controller({ version: '1', path: 'institution' })
export class InstitutionController {
  constructor(private readonly institutionService: InstitutionService) {}
  private readonly logger = new Logger(InstitutionController.name);

  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'institution', action: 'create', possession: 'any' })
  @ApiBearerAuth()
  @ZodSerializerDto(InstitutionDto)
  @ApiOperation({
    summary: 'Create new institution',
    description: 'Create a new institution entry in the system',
  })
  @ApiBody({
    type: InstitutionDto,
    description: 'Institution data to create',
    examples: {
      example1: {
        value: {
          name: 'University of Ghana',
          country_id: '123e4567-e89b-12d3-a456-************',
          location: 'Accra',
          address: 'Legon Campus',
          city: 'Accra',
          state: 'Greater Accra',
          domain: 'ug.edu.gh',
        },
      },
    },
  })
  @ApiCreatedResponse({
    description: 'The institution has been successfully created.',
    type: InstitutionDto,
  })
  @ApiConflictResponse({
    description: 'Institution with this name already exists',
  })
  @ApiBadRequestResponse({
    description: 'Country with provided ID does not exist',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async addInstitution(
    @Body()
    institutionDto: InstitutionDto,
  ) {
    try {
      const newInstitution =
        await this.institutionService.addInstitution(institutionDto);
      return newInstitution;
    } catch (error: any) {
      this.logger.error('Error adding institution', error.stack);
      throw error;
    }
  }

  @Get()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'institution', action: 'read', possession: 'any' })
  @ApiOperation({
    summary: 'Get all institutions',
    description:
      'Retrieve a list of all institutions with optional filtering and pagination',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description:
      'Filter institutions by name, address, city, state, or location',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'all',
    required: false,
    description: 'Return all results without pagination',
    type: Boolean,
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    description: 'Field to sort by',
    type: String,
  })
  @ApiQuery({
    name: 'order',
    required: false,
    description: 'Sort order (asc or desc)',
    enum: ['asc', 'desc'],
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all institutions.',
    type: [InstitutionDto],
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getAllInstitutions(
    @User() user: UserDecoratorType,
    @Query() query: institutionQueryParamsDto,
  ) {
    try {
      const institutions = await this.institutionService.getAllInstitutions(
        user,
        query as institutionQueryParamsDto & {
          all: boolean;
          sort: keyof Institution;
        },
      );
      return institutions;
    } catch (error: any) {
      this.logger.error('Error retrieving institutions', error.stack);
      throw error;
    }
  }

  @Get(InstitutionRoutes.GET_BY_ID)
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  @ZodSerializerDto(InstitutionDto)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'institution', action: 'read', possession: 'any' })
  @ZodSerializerDto(InstitutionDto)
  @ApiOperation({
    summary: 'Get institution by ID',
    description: 'Retrieve a specific institution by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Institution unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved the institution.',
    type: InstitutionDto,
  })
  @ApiNotFoundResponse({
    description: 'Institution with ID not found',
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getInstitutionById(
    @Param('id', new CustomParseUUIDPipe()) institutionId: string,
  ) {
    try {
      const institution =
        await this.institutionService.getInstitutionById(institutionId);
      return institution;
    } catch (error: any) {
      this.logger.error('Error retrieving institution', error.stack);
      throw error;
    }
  }

  @Delete(InstitutionRoutes.DELETE_BY_ID)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'institution', action: 'delete', possession: 'any' })
  @ApiOperation({
    summary: 'Delete institution',
    description: 'Delete an institution by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Institution unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiNoContentResponse({
    description: 'Successfully deleted the institution.',
  })
  @ApiNotFoundResponse({
    description: 'Institution with ID not found',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async removeInstitution(
    @Param('id', new CustomParseUUIDPipe()) institutionId: string,
  ) {
    try {
      await this.institutionService.removeInstitution(institutionId);
    } catch (error: any) {
      this.logger.error('Error removing institution', error.stack);
      throw error;
    }
  }
  //Updating institution
  @Put(InstitutionRoutes.UPDATE_BY_ID)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'institution', action: 'update', possession: 'any' })
  @ZodSerializerDto(InstitutionDto)
  @ApiOperation({
    summary: 'Update institution',
    description: "Update an institution's information by ID",
  })
  @ApiParam({
    name: 'id',
    description: 'Institution unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiBody({
    type: UpdateInstitutionDto,
    description: 'Updated institution data',
    examples: {
      example1: {
        value: {
          name: 'Updated University Name',
          location: 'Updated Location',
          address: 'Updated Address',
        },
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully updated the institution.',
    type: InstitutionDto,
  })
  @ApiBearerAuth()
  @ApiNotFoundResponse({
    description: 'Institution with ID not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid institution data',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async updateInstitution(
    @Param('id', new CustomParseUUIDPipe()) institutionId: string,
    @Body() institutionDto: UpdateInstitutionDto,
  ) {
    try {
      const updatedInstitution =
        await this.institutionService.updateInstitution(
          institutionId,
          institutionDto,
        );
      return updatedInstitution;
    } catch (error: any) {
      this.logger.error(`Error updating institution ${error.message}`);
      throw error;
    }
  }
  // Update Institution Status Active and inactive
  @Put(InstitutionRoutes.UPDATE_STATUS)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'institution', action: 'update', possession: 'any' })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update institution status',
    description: 'Enable or disable an institution by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Institution unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        disabled: {
          type: 'boolean',
          description: 'Status to set (true for disabled, false for enabled)',
          example: true,
        },
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully updated institution status.',
    type: InstitutionDto,
  })
  @ApiNotFoundResponse({
    description: 'Institution with ID not found',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async updateInstitutionStatus(
    @Param('id', new CustomParseUUIDPipe()) institutionId: string,
    @Body('disabled') status: boolean,
  ) {
    try {
      return await this.institutionService.updateInstitutionStatus(
        institutionId,
        status,
      );
    } catch (error: any) {
      this.logger.error(`Error disabling institution ${error.message}`);
      throw error;
    }
  }

  //Get disabled institutions
  @Get(InstitutionRoutes.GET_DISABLED_INSTITUTIONS)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'institution', action: 'read', possession: 'any' })
  @ApiOperation({
    summary: 'Get institutions by status',
    description: 'Retrieve institutions filtered by their disabled status',
  })
  @ApiBearerAuth()
  @ApiParam({
    name: 'disabled',
    description: 'Disabled status (true or false)',
    type: 'boolean',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved institutions by status.',
    type: [InstitutionDto],
  })
  @CLIENT_TYPE(AppClients.WEB)
  async getInstitutionsByStatus(@Param('disabled') disabled: boolean) {
    try {
      const institutions =
        await this.institutionService.getInstitutionsByStatus(disabled);
      return institutions;
    } catch (error: any) {
      this.logger.error('Error retrieving disabled institutions', error.stack);
      throw error;
    }
  }
}
