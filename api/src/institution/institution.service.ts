import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import type {
  InstitutionParams,
  institutionQueryParamsDto,
  UpdateInstitutionDto,
} from './institution.dto';
import { Institution, institutions } from '@/db/schema/institution';
import {
  eq,
  and,
  desc,
  asc,
  ilike,
  or,
  getTableColumns,
  sql,
} from 'drizzle-orm';
import { countries, User, user_roles } from '@/db/schema';
import { CacheService } from '@app/shared/redis/cache.service';
import {
  generateInstitutionKey,
  invalidateInstitutionCaches,
} from './utils/cache.utils';

@Injectable()
export class InstitutionService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
  ) {}

  private readonly logger = new Logger(InstitutionService.name);
  private readonly CACHE_PREFIX = 'institution';
  private readonly CACHE_TTL = 60 * 60 * 24 * 7; // 7 days

  async addInstitution(institutionInput: InstitutionParams) {
    const { name, country_id, location, address, city, state, domain } =
      institutionInput;

    return await this.drizzle.db.transaction(async (tx) => {
      await this.validateCountryExists(country_id);
      await this.checkInstitutionNameExists(name);

      const [newInstitution] = await tx
        .insert(institutions)
        .values({
          name: name.trim(),
          country_id,
          location: location.trim(),
          address: address.trim(),
          city: city.trim(),
          state: state.trim(),
          domain: domain?.trim(),
        })
        .returning();

      // Fetch the complete institution data with country name
      const [institutionWithCountry] = await tx
        .select({
          ...getTableColumns(institutions),
          country: countries.name,
        })
        .from(institutions)
        .leftJoin(countries, eq(countries.id, institutions.country_id))
        .where(eq(institutions.id, newInstitution!.id))
        .limit(1);

      try {
        // Invalidate all existing caches first
        await invalidateInstitutionCaches(
          this.cacheService,
          newInstitution!.id,
          this.CACHE_PREFIX,
        );

        // Fetch all institutions with fresh data
        const allInstitutions = await tx
          .select({
            ...getTableColumns(institutions),
            country: countries.name,
          })
          .from(institutions)
          .leftJoin(countries, eq(countries.id, institutions.country_id))
          .orderBy(asc(institutions.name));

        // Update caches for all user roles
        const roles = Object.values(user_roles);

        await Promise.all(
          roles.map(async (role) => {
            const cacheKey = this.cacheService.generateKey(
              ['all', role],
              this.CACHE_PREFIX,
            );

            const filteredInstitutions =
              role === user_roles.STUDENT || role === user_roles.STUDENT_ADMIN
                ? allInstitutions.filter((institution) => !institution.disabled)
                : allInstitutions;

            await this.cacheService.set(
              cacheKey,
              filteredInstitutions,
              this.CACHE_TTL,
            );
          }),
        );

        // Update the individual institution cache
        await this.cacheService.set(
          generateInstitutionKey(
            this.cacheService,
            newInstitution!.id,
            this.CACHE_PREFIX,
          ),
          institutionWithCountry,
          this.CACHE_TTL,
        );
      } catch (error) {
        this.logger.error('Failed to update institution caches', error);
        throw error;
      }

      return institutionWithCountry;
    });
  }

  async getAllInstitutions(
    user: User,
    params: institutionQueryParamsDto & { sort: keyof Institution },
  ) {
    const { all, search } = params;

    // Don't use cache for filtered/paginated/search requests
    if (search || !all) {
      return this.getInstitutionsFromDB(user, params);
    }

    const cacheKey = this.cacheService.generateKey(
      ['all', user.role],
      this.CACHE_PREFIX,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved institutions from cache');
      return cachedData;
    }

    const institutions = await this.getInstitutionsFromDB(user, params);
    await this.cacheService.set(cacheKey, institutions, this.CACHE_TTL);

    return institutions;
  }

  private async getInstitutionsFromDB(
    user: User,
    params: institutionQueryParamsDto & { sort: keyof Institution },
  ) {
    const { all, sort, order, limit, page, search } = params;
    const isStudent = [user_roles.STUDENT, user_roles.STUDENT_ADMIN].includes(
      user.role as 'student' | 'student_admin',
    );

    const whereCondition = [
      isStudent ? eq(institutions.disabled, false) : undefined,
    ].filter(Boolean);

    if (search) {
      whereCondition.push(
        or(
          ilike(institutions.name, `%${search}%`),
          ilike(institutions.address, `%${search}%`),
          ilike(institutions.city, `%${search}%`),
          ilike(institutions.state, `%${search}%`),
          ilike(institutions.location, `%${search}%`),
        ),
      );
    }

    const query = this.drizzle.db
      .select({
        ...getTableColumns(institutions),
        country: countries.name,
      })
      .from(institutions)
      .where(and(...whereCondition))
      .leftJoin(countries, eq(countries.id, institutions.country_id))
      .orderBy(
        order === 'asc' ? asc(institutions[sort]) : desc(institutions[sort]),
      );

    if (!all) {
      const data = await query.limit(limit).offset((page - 1) * limit);
      const total = await this.drizzle.db.$count(
        institutions,
        and(...whereCondition),
      );
      return { data, total };
    }

    return await query;
  }

  async updateInstitution(
    institutionId: string,
    institutionInput: UpdateInstitutionDto,
  ) {
    return await this.drizzle.db.transaction(async (tx) => {
      const [updatedInstitution] = await tx
        .update(institutions)
        .set({
          ...(institutionInput.name && { name: institutionInput.name.trim() }),
          ...(institutionInput.country_id && {
            country_id: institutionInput.country_id,
          }),
          ...(institutionInput.location && {
            location: institutionInput.location.trim(),
          }),
          ...(institutionInput.address && {
            address: institutionInput.address.trim(),
          }),
          ...(institutionInput.city && { city: institutionInput.city.trim() }),
          ...(institutionInput.state && {
            state: institutionInput.state.trim(),
          }),
          ...(institutionInput.domain && {
            domain: institutionInput.domain.trim(),
          }),
        })
        .where(eq(institutions.id, institutionId))
        .returning();

      if (!updatedInstitution) {
        throw new NotFoundException(
          `Institution with ID ${institutionId} not found`,
        );
      }

      try {
        // Invalidate all existing caches first
        await invalidateInstitutionCaches(
          this.cacheService,
          institutionId,
          this.CACHE_PREFIX,
        );

        // Fetch all institutions with fresh data
        const allInstitutions = await tx
          .select()
          .from(institutions)
          .orderBy(asc(institutions.name));

        // Update caches for all user roles
        const roles = Object.values(user_roles);

        await Promise.all(
          roles.map(async (role) => {
            const cacheKey = this.cacheService.generateKey(
              ['all', role],
              this.CACHE_PREFIX,
            );

            const filteredInstitutions =
              role === user_roles.STUDENT || role === user_roles.STUDENT_ADMIN
                ? allInstitutions.filter((institution) => !institution.disabled)
                : allInstitutions;

            await this.cacheService.set(
              cacheKey,
              filteredInstitutions,
              this.CACHE_TTL,
            );
          }),
        );

        // Update the individual institution cache
        await this.cacheService.set(
          generateInstitutionKey(
            this.cacheService,
            institutionId,
            this.CACHE_PREFIX,
          ),
          updatedInstitution,
          this.CACHE_TTL,
        );
      } catch (error) {
        this.logger.error('Failed to update institution caches', error);
        throw error;
      }

      return updatedInstitution;
    });
  }

  async updateInstitutionStatus(institutionId: string, status: boolean) {
    return await this.drizzle.db.transaction(async (tx) => {
      const [updatedInstitution] = await tx
        .update(institutions)
        .set({ disabled: status })
        .where(eq(institutions.id, institutionId))
        .returning();

      if (!updatedInstitution) {
        throw new NotFoundException(
          `Institution with ID ${institutionId} not found`,
        );
      }

      await invalidateInstitutionCaches(
        this.cacheService,
        institutionId,
        this.CACHE_PREFIX,
      );

      return updatedInstitution;
    });
  }

  async removeInstitution(institutionId: string) {
    return await this.drizzle.db.transaction(async (tx) => {
      const [deletedInstitution] = await tx
        .delete(institutions)
        .where(eq(institutions.id, institutionId))
        .returning();

      if (!deletedInstitution) {
        throw new NotFoundException(
          `Institution with ID ${institutionId} not found`,
        );
      }

      await invalidateInstitutionCaches(
        this.cacheService,
        institutionId,
        this.CACHE_PREFIX,
      );

      return deletedInstitution;
    });
  }

  async getInstitutionById(institutionId: string) {
    const cacheKey = generateInstitutionKey(
      this.cacheService,
      institutionId,
      this.CACHE_PREFIX,
    );

    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const result = await this.drizzle.db
      .select({
        ...getTableColumns(institutions),
        country: countries.name,
      })
      .from(institutions)
      .leftJoin(countries, eq(countries.id, institutions.country_id))
      .where(eq(institutions.id, institutionId))
      .limit(1);

    if (!result.length) {
      throw new NotFoundException(
        `Institution with ID ${institutionId} not found`,
      );
    }

    await this.cacheService.set(cacheKey, result[0], this.CACHE_TTL);
    return result[0];
  }

  async getInstitutionsByStatus(disabled: boolean) {
    const cacheKey = this.cacheService.generateKey(
      ['status', String(disabled)],
      this.CACHE_PREFIX,
    );

    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const result = await this.drizzle.db
      .select()
      .from(institutions)
      .where(eq(institutions.disabled, disabled));

    await this.cacheService.set(cacheKey, result, this.CACHE_TTL);
    return result;
  }

  private async validateCountryExists(countryId: string): Promise<void> {
    const country = await this.drizzle.db
      .select()
      .from(countries)
      .where(eq(countries.id, countryId))
      .limit(1);

    if (!country.length) {
      throw new BadRequestException(
        `Country with ID ${countryId} does not exist`,
      );
    }
  }

  private async checkInstitutionNameExists(name: string): Promise<void> {
    const [existingInstitution] = await this.drizzle.db
      .select()
      .from(institutions)
      .where(sql`LOWER(${institutions.name}) = ${name.toLowerCase()}`)
      .limit(1);

    if (existingInstitution) {
      throw new ConflictException(
        `Institution with name ${name} already exists`,
      );
    }
  }
}
