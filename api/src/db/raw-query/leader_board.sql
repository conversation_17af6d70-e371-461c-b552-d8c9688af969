-- Indexes for quiz_score
CREATE INDEX IF NOT EXISTS idx_quiz_score_user_id_created_at ON quiz_score (user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_quiz_score_created_at ON quiz_score (created_at);

CREATE INDEX IF NOT EXISTS idx_quiz_score_updated_at ON quiz_score (updated_at);

-- Indexes for points_logs
CREATE INDEX IF NOT EXISTS idx_points_logs_student_id_created_at ON points_logs (student_id, created_at);

CREATE INDEX IF NOT EXISTS idx_points_logs_created_at ON points_logs (created_at);

CREATE INDEX IF NOT EXISTS idx_points_logs_updated_at ON points_logs (updated_at);

-- Function to avoid code duplication
CREATE OR REPLACE FUNCTION create_leaderboard_view(view_name TEXT, quiz_filter TEXT, points_filter TEXT)
RETURNS VOID AS $$
DECLARE
    sql_query TEXT;
BEGIN
    -- Construct the SQL query
    sql_query := format(
        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
        WITH
            quiz_scores_aggregated AS (
                SELECT
                    quiz_score.user_id AS student_id,
                    SUM(quiz_score.score) AS total_quiz_score,
                    MIN(quiz_score.created_at) AS first_quiz_date,
                    MAX(quiz_score.updated_at) AS last_quiz_date
                FROM quiz_score
                WHERE %s
                GROUP BY
                    quiz_score.user_id
            ),
            points_logs_aggregated AS (
                SELECT
                    points_logs.student_id,
                    SUM(points_logs.points) AS total_points,
                    MIN(points_logs.created_at) AS first_points_date,
                    MAX(points_logs.updated_at) AS last_points_date
                FROM points_logs
                WHERE %s
                GROUP BY
                    points_logs.student_id
            )
        SELECT
            sp.id AS student_id,
            sp.first_name,
            sp.last_name,
            u.profile_pic_url,
            sp.graduation_date,
            sp.enrollment_date,
            sp.institution_id,
            i.name AS institution_name,
            sp.degree,
            sp.programme,
            COALESCE(q.total_quiz_score, 0) + COALESCE(p.total_points, 0) AS total_score,
            ROW_NUMBER() OVER (
                ORDER BY COALESCE(q.total_quiz_score, 0) + COALESCE(p.total_points, 0) DESC,
                LEAST(COALESCE(q.first_quiz_date, ''2000-01-01''::timestamp), COALESCE(p.first_points_date, ''2000-01-01''::timestamp)) ASC,
                sp.id ASC
            ) AS rank,
            LEAST(COALESCE(q.first_quiz_date, NULL), COALESCE(p.first_points_date, NULL)) AS first_quiz_date,
            GREATEST(COALESCE(q.last_quiz_date, NULL), COALESCE(p.last_points_date, NULL)) AS last_quiz_date
        FROM
            student_profiles sp
            JOIN users u ON sp.user_id = u.id
            JOIN institutions i ON sp.institution_id = i.id
            LEFT JOIN quiz_scores_aggregated q ON sp.id = q.student_id
            LEFT JOIN points_logs_aggregated p ON sp.id = p.student_id
        WHERE
            -- Only include students with a score greater than zero
            (COALESCE(q.total_quiz_score, 0) + COALESCE(p.total_points, 0)) > 0
        GROUP BY
            sp.id,
            sp.first_name,
            sp.last_name,
            u.profile_pic_url,
            sp.enrollment_date,
            sp.graduation_date,
            sp.institution_id,
            i.name,
            sp.degree,
            sp.programme,
            q.total_quiz_score,
            p.total_points,
            q.first_quiz_date,
            q.last_quiz_date,
            p.first_points_date,
            p.last_points_date;',
        view_name,
        quiz_filter,
        points_filter
    );

    -- Execute the query
    EXECUTE sql_query;
END;
$$ LANGUAGE plpgsql;

-- Daily leaderboard
SELECT create_leaderboard_view (
        'leaderboard_day', 'quiz_score.created_at >= date_trunc(''day'', CURRENT_DATE) AND quiz_score.created_at < date_trunc(''day'', CURRENT_DATE + INTERVAL ''1 day'')', 'points_logs.created_at >= date_trunc(''day'', CURRENT_DATE) AND points_logs.created_at < date_trunc(''day'', CURRENT_DATE + INTERVAL ''1 day'')'
    );

-- Weekly leaderboard (current week)
SELECT create_leaderboard_view (
        'leaderboard_week',
        'quiz_score.created_at >= date_trunc(''week'', CURRENT_DATE) AND quiz_score.created_at < date_trunc(''week'', CURRENT_DATE + INTERVAL ''1 week'')',
        'points_logs.created_at >= date_trunc(''week'', CURRENT_DATE) AND points_logs.created_at < date_trunc(''week'', CURRENT_DATE + INTERVAL ''1 week'')'
    );

-- Last week leaderboard
SELECT create_leaderboard_view (
        'leaderboard_last_week',
        'quiz_score.created_at >= date_trunc(''week'', CURRENT_DATE - INTERVAL ''1 week'') AND quiz_score.created_at < date_trunc(''week'', CURRENT_DATE)',
        'points_logs.created_at >= date_trunc(''week'', CURRENT_DATE - INTERVAL ''1 week'') AND points_logs.created_at < date_trunc(''week'', CURRENT_DATE)'
    );

-- Monthly leaderboard (current month)
SELECT create_leaderboard_view (
        'leaderboard_month',
        'quiz_score.created_at >= date_trunc(''month'', CURRENT_DATE) AND quiz_score.created_at < date_trunc(''month'', CURRENT_DATE + INTERVAL ''1 month'')',
        'points_logs.created_at >= date_trunc(''month'', CURRENT_DATE) AND points_logs.created_at < date_trunc(''month'', CURRENT_DATE + INTERVAL ''1 month'')'
    );

-- Last month leaderboard
SELECT create_leaderboard_view (
        'leaderboard_last_month',
        'quiz_score.created_at >= date_trunc(''month'', CURRENT_DATE - INTERVAL ''1 month'') AND quiz_score.created_at < date_trunc(''month'', CURRENT_DATE)',
        'points_logs.created_at >= date_trunc(''month'', CURRENT_DATE - INTERVAL ''1 month'') AND points_logs.created_at < date_trunc(''month'', CURRENT_DATE)'
    );

-- Current quarter leaderboard
SELECT create_leaderboard_view (
        'leaderboard_current_quarter',
        'quiz_score.created_at >= date_trunc(''quarter'', CURRENT_DATE) AND quiz_score.created_at < date_trunc(''quarter'', CURRENT_DATE + INTERVAL ''3 months'')',
        'points_logs.created_at >= date_trunc(''quarter'', CURRENT_DATE) AND points_logs.created_at < date_trunc(''quarter'', CURRENT_DATE + INTERVAL ''3 months'')'
    );

-- Yearly leaderboard
SELECT create_leaderboard_view (
        'leaderboard_year', 'quiz_score.created_at >= date_trunc(''year'', CURRENT_DATE) AND quiz_score.created_at < date_trunc(''year'', CURRENT_DATE + INTERVAL ''1 year'')', 'points_logs.created_at >= date_trunc(''year'', CURRENT_DATE) AND points_logs.created_at < date_trunc(''year'', CURRENT_DATE + INTERVAL ''1 year'')'
    );

-- All-time leaderboard
SELECT create_leaderboard_view (
        'leaderboard_all_time', 'TRUE', 'TRUE'
    );

-- First quarter leaderboard
SELECT create_leaderboard_view (
        'leaderboard_first_quarter', 'EXTRACT(QUARTER FROM quiz_score.created_at) = 1 AND EXTRACT(YEAR FROM quiz_score.created_at) = EXTRACT(YEAR FROM CURRENT_DATE)', 'EXTRACT(QUARTER FROM points_logs.created_at) = 1 AND EXTRACT(YEAR FROM points_logs.created_at) = EXTRACT(YEAR FROM CURRENT_DATE)'
    );

-- Second quarter leaderboard
SELECT create_leaderboard_view (
        'leaderboard_second_quarter', 'EXTRACT(QUARTER FROM quiz_score.created_at) = 2 AND EXTRACT(YEAR FROM quiz_score.created_at) = EXTRACT(YEAR FROM CURRENT_DATE)', 'EXTRACT(QUARTER FROM points_logs.created_at) = 2 AND EXTRACT(YEAR FROM points_logs.created_at) = EXTRACT(YEAR FROM CURRENT_DATE)'
    );

-- Third quarter leaderboard
SELECT create_leaderboard_view (
        'leaderboard_third_quarter', 'EXTRACT(QUARTER FROM quiz_score.created_at) = 3 AND EXTRACT(YEAR FROM quiz_score.created_at) = EXTRACT(YEAR FROM CURRENT_DATE)', 'EXTRACT(QUARTER FROM points_logs.created_at) = 3 AND EXTRACT(YEAR FROM points_logs.created_at) = EXTRACT(YEAR FROM CURRENT_DATE)'
    );

-- Fourth quarter leaderboard
SELECT create_leaderboard_view (
        'leaderboard_fourth_quarter', 'EXTRACT(QUARTER FROM quiz_score.created_at) = 4 AND EXTRACT(YEAR FROM quiz_score.created_at) = EXTRACT(YEAR FROM CURRENT_DATE)', 'EXTRACT(QUARTER FROM points_logs.created_at) = 4 AND EXTRACT(YEAR FROM points_logs.created_at) = EXTRACT(YEAR FROM CURRENT_DATE)'
    );
