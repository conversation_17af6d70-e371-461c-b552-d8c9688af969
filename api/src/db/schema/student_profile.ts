import {
  text,
  date,
  pgTable,
  integer,
  timestamp,
  uuid,
  boolean,
} from 'drizzle-orm/pg-core';

import { users } from './users';
import { student_club_memberships, student_clubs } from './clubs';
import { countries } from './countries';
import { institutions } from './institution';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { postEngagementsSchema } from './post_engagements';
import { studentSkills } from './skills';

export const studentProgrammes = ['ICT', 'Non-STEM', 'Other STEM'] as const;
export const studentDegrees = ['Bachelors', 'Masters', 'HND'] as const;

export const student_profiles = pgTable('student_profiles', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  user_id: uuid('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
  first_name: text('first_name').notNull(),
  last_name: text('last_name').notNull(),
  other_name: text('other_name'),
  username: text('username'),
  country_id: uuid('country_id')
    .references(() => countries.id, { onDelete: 'cascade' })
    .notNull(),
  date_of_birth: date('date_of_birth'),
  phone_number: text('phone_number'),
  institution_id: uuid('institution_id')
    .references(() => institutions.id, { onDelete: 'cascade' })
    .notNull(),
  enrollment_date: integer('enrollment_date').notNull(),
  graduation_date: integer('graduation_date').notNull(),
  degree: text({ enum: studentDegrees }).notNull().default('Bachelors'),
  programme: text({ enum: studentProgrammes }).notNull().default('ICT'),
  github_profile: text('github_profile'),
  linkedin_profile: text('linkedin_profile'),
  about: text('about'),
  club_id: uuid('club_id').references(() => student_clubs.id, {
    onDelete: 'cascade',
  }),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
  username_last_updated: timestamp('username_last_updated', { mode: 'string' })
    .notNull()
    .default(sql`now()`),
  deleted: boolean('deleted').default(false).notNull(),
  deleted_at: timestamp('deleted_at', { mode: 'string' }),
});

export const studentProfileRelations = relations(
  student_profiles,
  ({ many, one }) => ({
    club_memberships: many(student_club_memberships),
    country: one(countries, {
      fields: [student_profiles.country_id],
      references: [countries.id],
    }),
    institution: one(institutions, {
      fields: [student_profiles.institution_id],
      references: [institutions.id],
    }),
    user: one(users, {
      fields: [student_profiles.user_id],
      references: [users.id],
    }),
    postEngagements: many(postEngagementsSchema),
    skills: many(studentSkills),
  }),
);

export const selectStudentProfileSchema = createSelectSchema(student_profiles);
export const insertStudentProfileSchema = createInsertSchema(
  student_profiles,
).omit({ user_id: true });

export type StudentProfile = typeof student_profiles.$inferSelect;
export type StudentProfileInput = typeof student_profiles.$inferInsert;
export const studentProfileKeys = Object.keys(student_profiles) as [
  string,
  ...string[],
];
