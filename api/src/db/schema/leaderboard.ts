import {
  pgMaterializedView,
  text,
  timestamp,
  uuid,
  integer,
} from 'drizzle-orm/pg-core';
import { student_profiles } from './student_profile';
import { sql } from 'drizzle-orm';

// Materialized view for daily leaderboard
export const leaderBoardDay = pgMaterializedView('leaderboard_day', {
  student_id: uuid('student_id').references(() => student_profiles.id, {
    onDelete: 'cascade',
  }),
  first_name: text('first_name'),
  last_name: text('last_name'),
  profile_pic_url: text('profile_pic_url'),
  graduation_date: timestamp('graduation_date'),
  enrollment_date: timestamp('enrollment_date'),
  institution_id: uuid('institution_id'),
  institution_name: text('institution_name'),
  degree: text('degree'),
  programme: text('programme'),
  total_score: integer('total_score').notNull().default(0),
  rank: integer('rank').notNull(),
  first_quiz_date: timestamp('first_quiz_date'),
  last_quiz_date: timestamp('last_quiz_date'),
}).as(sql`
  SELECT student_profiles.id AS student_id,
  student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    COALESCE(SUM(quiz_score.score), 0) + COALESCE((
      SELECT SUM(points_logs.points)
      FROM points_logs
      WHERE points_logs.student_id = student_profiles.id
      AND date_trunc('day', points_logs.created_at) = date_trunc('day', CURRENT_DATE)
    ), 0) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY COALESCE(SUM(quiz_score.score), 0) + COALESCE((
          SELECT SUM(points_logs.points)
          FROM points_logs
          WHERE points_logs.student_id = student_profiles.id
          AND date_trunc('day', points_logs.created_at) = date_trunc('day', CURRENT_DATE)
        ), 0) DESC
    ) AS rank,
    MIN(COALESCE(quiz_score.created_at, (
      SELECT MIN(points_logs.created_at)
      FROM points_logs
      WHERE points_logs.student_id = student_profiles.id
    ))) AS first_quiz_date,
    MAX(COALESCE(quiz_score.updated_at, (
      SELECT MAX(points_logs.updated_at)
      FROM points_logs
      WHERE points_logs.student_id = student_profiles.id
    ))) AS last_quiz_date
    FROM student_profiles
    LEFT JOIN quiz_score ON quiz_score.user_id = student_profiles.id
    JOIN users ON student_profiles.user_id = users.id
    JOIN institutions ON student_profiles.institution_id = institutions.id
  WHERE
  (date_trunc('day', quiz_score.created_at) = date_trunc('day', CURRENT_DATE)
    OR student_profiles.id IN (
      SELECT student_id FROM points_logs
      WHERE date_trunc('day', points_logs.created_at) = date_trunc('day', CURRENT_DATE)
    ))
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
  `);

// Materialized view for weekly leaderboard
export const leaderBoardWeek = pgMaterializedView('leaderboard_week', {
  student_id: uuid('student_id').references(() => student_profiles.id, {
    onDelete: 'cascade',
  }),
  first_name: text('first_name'),
  last_name: text('last_name'),
  profile_pic_url: text('profile_pic_url'),
  graduation_date: timestamp('graduation_date'),
  enrollment_date: timestamp('enrollment_date'),
  institution_id: uuid('institution_id'),
  institution_name: text('institution_name'),
  degree: text('degree'),
  programme: text('programme'),
  total_score: integer('total_score').notNull().default(0),
  rank: integer('rank').notNull(),
  first_quiz_date: timestamp('first_quiz_date'),
  last_quiz_date: timestamp('last_quiz_date'),
}).as(sql`
  SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('week', quiz_score.created_at) = date_trunc('week', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
  `);

// Materialized view for monthly leaderboard
export const leaderBoardMonth = pgMaterializedView('leaderboard_month', {
  student_id: uuid('student_id').references(() => student_profiles.id, {
    onDelete: 'cascade',
  }),
  first_name: text('first_name'),
  last_name: text('last_name'),
  profile_pic_url: text('profile_pic_url'),
  graduation_date: timestamp('graduation_date'),
  enrollment_date: timestamp('enrollment_date'),
  institution_id: uuid('institution_id'),
  institution_name: text('institution_name'),
  degree: text('degree'),
  programme: text('programme'),
  total_score: integer('total_score').notNull().default(0),
  rank: integer('rank').notNull(),
  first_quiz_date: timestamp('first_quiz_date'),
  last_quiz_date: timestamp('last_quiz_date'),
}).as(sql`
   SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('month', quiz_score.created_at) = date_trunc('month', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
  `);

// Materialized view for first quarter leaderboard
export const leaderBoardFirstQuarter = pgMaterializedView(
  'leaderboard_first_quarter',
  {
    student_id: uuid('student_id').references(() => student_profiles.id, {
      onDelete: 'cascade',
    }),
    first_name: text('first_name'),
    last_name: text('last_name'),
    profile_pic_url: text('profile_pic_url'),
    graduation_date: timestamp('graduation_date'),
    enrollment_date: timestamp('enrollment_date'),
    institution_id: uuid('institution_id'),
    institution_name: text('institution_name'),
    degree: text('degree'),
    programme: text('programme'),
    total_score: integer('total_score').notNull().default(0),
    rank: integer('rank').notNull(),
    first_quiz_date: timestamp('first_quiz_date'),
    last_quiz_date: timestamp('last_quiz_date'),
  },
).as(sql`SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    EXTRACT(QUARTER FROM quiz_score.created_at) = 1
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme`);

// Materialized view for second quarter leaderboard
export const leaderBoardSecondQuarter = pgMaterializedView(
  'leaderboard_second_quarter',
  {
    student_id: uuid('student_id').references(() => student_profiles.id, {
      onDelete: 'cascade',
    }),
    first_name: text('first_name'),
    last_name: text('last_name'),
    profile_pic_url: text('profile_pic_url'),
    graduation_date: timestamp('graduation_date'),
    enrollment_date: timestamp('enrollment_date'),
    institution_id: uuid('institution_id'),
    institution_name: text('institution_name'),
    degree: text('degree'),
    programme: text('programme'),
    total_score: integer('total_score').notNull().default(0),
    rank: integer('rank').notNull(),
    first_quiz_date: timestamp('first_quiz_date'),
    last_quiz_date: timestamp('last_quiz_date'),
  },
).as(sql`SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    EXTRACT(QUARTER FROM quiz_score.created_at) = 2
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme`);

// Materialized view for third quarter leaderboard
export const leaderBoardThirdQuarter = pgMaterializedView(
  'leaderboard_third_quarter',
  {
    student_id: uuid('student_id').references(() => student_profiles.id, {
      onDelete: 'cascade',
    }),
    first_name: text('first_name'),
    last_name: text('last_name'),
    profile_pic_url: text('profile_pic_url'),
    graduation_date: timestamp('graduation_date'),
    enrollment_date: timestamp('enrollment_date'),
    institution_id: uuid('institution_id'),
    institution_name: text('institution_name'),
    degree: text('degree'),
    programme: text('programme'),
    total_score: integer('total_score').notNull().default(0),
    rank: integer('rank').notNull(),
    first_quiz_date: timestamp('first_quiz_date'),
    last_quiz_date: timestamp('last_quiz_date'),
  },
).as(sql`SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    EXTRACT(QUARTER FROM quiz_score.created_at) = 3
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme`);

// Materialized view for fourth quarter leaderboard
export const leaderBoardFourthQuarter = pgMaterializedView(
  'leaderboard_fourth_quarter',
  {
    student_id: uuid('student_id').references(() => student_profiles.id, {
      onDelete: 'cascade',
    }),
    first_name: text('first_name'),
    last_name: text('last_name'),
    profile_pic_url: text('profile_pic_url'),
    graduation_date: timestamp('graduation_date'),
    enrollment_date: timestamp('enrollment_date'),
    institution_id: uuid('institution_id'),
    institution_name: text('institution_name'),
    degree: text('degree'),
    programme: text('programme'),
    total_score: integer('total_score').notNull().default(0),
    rank: integer('rank').notNull(),
    first_quiz_date: timestamp('first_quiz_date'),
    last_quiz_date: timestamp('last_quiz_date'),
  },
).as(sql`SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    EXTRACT(QUARTER FROM quiz_score.created_at) = 4
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme`);

// Materialized view for current quarter leaderboard
export const leaderBoardCurrentQuarter = pgMaterializedView(
  'leaderboard_current_quarter',
  {
    student_id: uuid('student_id').references(() => student_profiles.id, {
      onDelete: 'cascade',
    }),
    first_name: text('first_name'),
    last_name: text('last_name'),
    profile_pic_url: text('profile_pic_url'),
    graduation_date: timestamp('graduation_date'),
    enrollment_date: timestamp('enrollment_date'),
    institution_id: uuid('institution_id'),
    institution_name: text('institution_name'),
    degree: text('degree'),
    programme: text('programme'),
    total_score: integer('total_score').notNull().default(0),
    rank: integer('rank').notNull(),
    first_quiz_date: timestamp('first_quiz_date'),
    last_quiz_date: timestamp('last_quiz_date'),
  },
).as(sql`  SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('quarter', quiz_score.created_at) = date_trunc('quarter', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
`);

// Materialized view for yearly leaderboard
export const leaderBoardYear = pgMaterializedView('leaderboard_year', {
  student_id: uuid('student_id').references(() => student_profiles.id, {
    onDelete: 'cascade',
  }),
  first_name: text('first_name'),
  last_name: text('last_name'),
  profile_pic_url: text('profile_pic_url'),
  graduation_date: timestamp('graduation_date'),
  enrollment_date: timestamp('enrollment_date'),
  institution_id: uuid('institution_id'),
  institution_name: text('institution_name'),
  degree: text('degree'),
  programme: text('programme'),
  total_score: integer('total_score').notNull().default(0),
  rank: integer('rank').notNull(),
  first_quiz_date: timestamp('first_quiz_date'),
  last_quiz_date: timestamp('last_quiz_date'),
}).as(sql`
  SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    date_trunc('year', quiz_score.created_at) = date_trunc('year', CURRENT_DATE)
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme
  `);

// Materialized view for all time leaderboard
export const leaderBoardAllTime = pgMaterializedView('leaderboard_all_time', {
  student_id: uuid('student_id').references(() => student_profiles.id, {
    onDelete: 'cascade',
  }),
  first_name: text('first_name'),
  last_name: text('last_name'),
  profile_pic_url: text('profile_pic_url'),
  graduation_date: timestamp('graduation_date'),
  enrollment_date: timestamp('enrollment_date'),
  institution_id: uuid('institution_id'),
  institution_name: text('institution_name'),
  degree: text('degree'),
  programme: text('programme'),
  total_score: integer('total_score').notNull().default(0),
  rank: integer('rank').notNull(),
  first_quiz_date: timestamp('first_quiz_date'),
  last_quiz_date: timestamp('last_quiz_date'),
}).as(sql`
  SELECT
    student_profiles.id AS student_id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.graduation_date,
    student_profiles.enrollment_date,
    student_profiles.institution_id,
    institutions.name AS institution_name,
    student_profiles.degree,
    student_profiles.programme,
    SUM(quiz_score.score) AS total_score,
    DENSE_RANK() OVER (
        ORDER BY SUM(quiz_score.score) DESC
    ) AS rank,
    MIN(quiz_score.created_at) AS first_quiz_date,
    MAX(quiz_score.updated_at) AS last_quiz_date
  FROM quiz_score
    JOIN public.student_profiles ON quiz_score.user_id = student_profiles.id
    JOIN public.users ON student_profiles.user_id = users.id
    JOIN public.institutions ON student_profiles.institution_id = institutions.id
  WHERE
    quiz_score.created_at >= '2024-01-01'
  GROUP BY
    student_profiles.id,
    student_profiles.first_name,
    student_profiles.last_name,
    users.profile_pic_url,
    student_profiles.enrollment_date,
    student_profiles.graduation_date,
    student_profiles.institution_id,
    institutions.name,
    student_profiles.degree,
    student_profiles.programme`);
