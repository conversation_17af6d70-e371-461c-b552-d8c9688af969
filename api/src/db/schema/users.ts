import { pgTable, uniqueIndex } from 'drizzle-orm/pg-core';

import { relations, sql } from 'drizzle-orm';
import { organisations } from './organisation';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { posts } from './posts';
import { student_profiles } from './student_profile';

export const StateEnum = [
  'active',
  'disabled',
  'inactive',
  'verified',
  'pending',
] as const;
export const users = pgTable(
  'users',
  (table) => ({
    id: table
      .uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    email: table.text('email').notNull(),
    role: table
      .text({ enum: ['student', 'student_admin', 'super_admin', 'admin'] })
      .default('student')
      .notNull(),
    state: table.text({ enum: StateEnum }).notNull().default('inactive'),
    profile_pic_url: table.text('profile_pic_url'),
    created_at: table
      .timestamp('created_at', { mode: 'string' })
      .notNull()
      .defaultNow(),
    updated_at: table
      .timestamp('updated_at', { mode: 'string' })
      .notNull()
      .default(sql`now()`)
      .$onUpdate(() => sql`now()`),
    deleted: table.boolean('deleted').default(false).notNull(),
    deleted_at: table.timestamp('deleted_at', { mode: 'string' }),
  }),
  (table) => ({
    emailUnique: uniqueIndex('email_unique_when_not_deleted')
      .on(table.email)
      .where(sql`deleted = false`),
  }),
);
export const usersRelations = relations(users, ({ one, many }) => ({
  // one-to-one relation
  profile: one(organisations, {
    fields: [users.id],
    references: [organisations.user_id],
  }),
  posts: many(posts),
  student_profile: one(student_profiles, {
    fields: [users.id],
    references: [student_profiles.user_id],
  }),
}));

export type User = typeof users.$inferSelect & {
  student_profile?: typeof student_profiles.$inferSelect;
};
export const insertUserSchema = createInsertSchema(users);
export const selectUserSchema = createSelectSchema(users);

export const user_roles = {
  SUPER_ADMIN: 'super_admin',
  STUDENT: 'student',
  STUDENT_ADMIN: 'student_admin',
  ADMIN: 'admin',
} as const;

export const user_states = {
  ACTIVE: 'active',
  DISABLED: 'disabled',
  INACTIVE: 'inactive',
  VERIFIED: 'verified',
  PENDING: 'pending',
} as const;

export type UserRole = (typeof user_roles)[keyof typeof user_roles];
export type UserState = (typeof user_states)[keyof typeof user_states];
