import { pgTable, uuid, boolean, jsonb, timestamp } from 'drizzle-orm/pg-core';
import { users } from './users';
import { events } from './posts';
import { sql } from 'drizzle-orm';

export const event_notification_preferences = pgTable(
  'event_notification_preferences',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id),
    eventId: uuid('event_id')
      .notNull()
      .references(() => events.id),
    optedIn: boolean('opted_in').notNull().default(true),
    reminderTimes: jsonb('reminder_times').$type<number[]>(),
    channels: jsonb('channels').$type<string[]>().default(['push', 'email']),
    created_at: timestamp('created_at').notNull().defaultNow(),
    updated_at: timestamp('updated_at', { mode: 'string' })
      .notNull()
      .default(sql`now()`)
      .$onUpdate(() => sql`now()`),
  },
);
