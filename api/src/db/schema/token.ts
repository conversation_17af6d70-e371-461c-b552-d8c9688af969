import { relations, sql } from 'drizzle-orm';
import { text, pgTable, timestamp, varchar, uuid } from 'drizzle-orm/pg-core';
import { users } from './users';

export const TokenTypes = ['otp', 'magic-link'] as const;

export const token = pgTable('token', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),

  user_id: uuid('user_id')
    .notNull()
    .unique()
    .references(() => users.id, { onDelete: 'cascade' }),
  token: varchar('token').notNull().unique(),
  type: text({ enum: TokenTypes }).notNull(),
  expiresAt: timestamp('expires_at', {
    mode: 'date',
  }).notNull(),
});

export const userTokenRelation = relations(token, ({ one }) => ({
  user: one(users, {
    fields: [token.user_id],
    references: [users.id],
  }),
}));

export const otpTypes = {
  OTP: 'otp',
  MAGIC_LINK: 'magic-link',
} as const;
export type OTPType = (typeof otpTypes)[keyof typeof otpTypes];
export type Token = typeof token.$inferSelect;
export type CreateTokenInput = typeof token.$inferInsert;
