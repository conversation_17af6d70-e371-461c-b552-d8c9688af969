import { sql } from 'drizzle-orm';
import {
  boolean,
  integer,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { student_profiles } from './student_profile';
import { createInsertSchema } from 'drizzle-zod';
import { users } from './users';
import { relations } from 'drizzle-orm';

/*
  Points Configuration Schema
  - Defines the details of each point configuration.
*/
export const pointsConfigSchema = pgTable('points_config', {
  id: uuid('id').primaryKey().defaultRandom(),
  point_name: varchar('point_name', { length: 50 }).notNull().unique(),
  point_value: integer('point_value').notNull(),
  description: text('description').notNull(),
  created_by: uuid('created_by').references(() => users.id, {
    onDelete: 'set null',
  }),
  deleted: boolean('deleted').notNull().default(false),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

/*
  Point Rules Schema
  - Associates modules and actions with specific point configurations.
  - Eliminates duplication by referencing pointsConfigSchema.
*/
export const pointRulesSchema = pgTable('point_rules', {
  id: uuid('id').primaryKey().defaultRandom(),
  module: varchar('module', { length: 50 }).notNull(),
  action: varchar('action', { length: 50 }).notNull(),
  points_config_id: uuid('points_config_id')
    .references(() => pointsConfigSchema.id)
    .notNull(),
  frequency: varchar('frequency', { length: 50 }).notNull(),
  disabled: boolean('deleted').notNull().default(false),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

/*
  Points Log Schema
  - Logs the points assigned to each student.
  - References pointsConfigSchema for point details to avoid duplication.
*/
export const pointsLogSchema = pgTable('points_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  student_id: uuid('student_id')
    .references(() => student_profiles.id, { onDelete: 'cascade' })
    .notNull(),
  point_rule_id: uuid('point_rule_id')
    .references(() => pointRulesSchema.id)
    .notNull(),
  points: integer('points').notNull(),
  description: text('description').notNull(),
  deleted: boolean('deleted').notNull().default(false),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

/*
  Type Definitions
*/
export type PointsConfig = typeof pointsConfigSchema.$inferInsert;
export type PointsLog = typeof pointsLogSchema.$inferInsert;
export type PointRule = typeof pointRulesSchema.$inferInsert;

/*
  Insert Schemas
*/
export const insertPointsConfig = createInsertSchema(pointsConfigSchema).omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export const insertPointsLogs = createInsertSchema(pointsLogSchema).omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export const insertPointRules = createInsertSchema(pointRulesSchema).omit({
  id: true,
  created_at: true,
  updated_at: true,
});

/*
  Relations
*/
export const pointsConfigRelations = relations(
  pointsConfigSchema,
  ({ many }) => ({
    pointRules: many(pointRulesSchema),
    pointsLogs: many(pointsLogSchema),
  }),
);

export const pointRulesRelations = relations(pointRulesSchema, ({ one }) => ({
  pointsConfig: one(pointsConfigSchema, {
    fields: [pointRulesSchema.points_config_id],
    references: [pointsConfigSchema.id],
  }),
}));

export const pointsLogRelations = relations(pointsLogSchema, ({ one }) => ({
  pointsConfig: one(pointsConfigSchema, {
    fields: [pointsLogSchema.point_rule_id],
    references: [pointsConfigSchema.id],
  }),
  studentProfile: one(student_profiles, {
    fields: [pointsLogSchema.student_id],
    references: [student_profiles.id],
  }),
}));
