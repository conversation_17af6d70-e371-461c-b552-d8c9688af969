import {
  boolean,
  text,
  timestamp,
  uuid,
  pgTable,
  unique,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { institutions } from './institution';
import { users } from './users';

import { countries } from './countries';
import { sql } from 'drizzle-orm';
import { student_profiles } from './student_profile';
import { relations } from 'drizzle-orm';
import { posts } from './posts';
export const clubRoles = ['member', 'club_admin'] as const;
export const student_clubs = pgTable(
  'clubs',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text('name').notNull().unique(),
    description: text('description').notNull(),
    club_admin: uuid('club_admin').references(() => users.id, {
      onDelete: 'set null',
    }),
    institution_id: uuid('institution_id')
      .notNull()
      .references(() => institutions.id),
    country_id: uuid('country_id')
      .notNull()
      .references(() => countries.id),
    is_active: boolean('is_active').default(false),
    club_logo_url: text('club_logo_url'),
    club_banner_url: text('club_banner_url'),
    created_by: uuid('created_by').references(() => users.id, {
      onDelete: 'set null',
    }),
    created_at: timestamp('created_at', { mode: 'string' })
      .notNull()
      .defaultNow(),
    updated_at: timestamp('updated_at', { mode: 'string' })
      .notNull()
      .default(sql`now()`)
      .$onUpdate(() => sql`now()`),
  },
  (table) => ({
    unq: unique().on(table.name, table.institution_id),
  }),
);

// Define the student_club_memberships table
export const student_club_memberships = pgTable(
  'student_club_memberships',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    student_id: uuid('student_id')
      .notNull()
      .references(() => student_profiles.id, { onDelete: 'cascade' }),
    club_id: uuid('club_id')
      .notNull()
      .references(() => student_clubs.id, { onDelete: 'cascade' }),
    role: text({ enum: clubRoles }).default('member'),
    joined_at: timestamp('joined_at', { mode: 'string' })
      .notNull()
      .defaultNow(),
    is_active: boolean('is_active').default(true),
    created_at: timestamp('created_at', { mode: 'string' })
      .notNull()
      .defaultNow(),
    updated_at: timestamp('updated_at', { mode: 'string' })
      .notNull()
      .default(sql`now()`)
      .$onUpdate(() => sql`now()`),
  },
  (table) => ({
    unq: unique().on(table.student_id, table.club_id),
  }),
);

export const studentClubRelations = relations(
  student_clubs,
  ({ many, one }) => ({
    members: many(student_club_memberships),
    posts: many(posts),
    admin: one(users, {
      fields: [student_clubs.club_admin],
      references: [users.id],
    }),
    country: one(countries, {
      fields: [student_clubs.country_id],
      references: [countries.id],
    }),
    institution: one(institutions, {
      fields: [student_clubs.institution_id],
      references: [institutions.id],
    }),
  }),
);

export const studentClubMembershipRelations = relations(
  student_club_memberships,
  ({ one }) => ({
    club: one(student_clubs, {
      fields: [student_club_memberships.club_id],
      references: [student_clubs.id],
    }),
    student: one(student_profiles, {
      fields: [student_club_memberships.student_id],
      references: [student_profiles.id],
    }),
  }),
);

export type Club = typeof student_clubs.$inferSelect;
export const student_club_roles = {
  MEMBER: 'member',
  ADMIN: 'club_admin',
} as const;
export type StudentClubRole =
  (typeof student_club_roles)[keyof typeof student_club_roles];

export const insertClubSchema = createInsertSchema(student_clubs);
export const selectClubSchema = createSelectSchema(student_clubs);

export const insertMembershipSchema = createInsertSchema(
  student_club_memberships,
);
export const selectMembershipSchema = createSelectSchema(
  student_club_memberships,
);
