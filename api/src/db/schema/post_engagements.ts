import { sql, relations } from 'drizzle-orm';
import { uuid, timestamp, pgTable, text } from 'drizzle-orm/pg-core';
import { posts } from './posts';
import { student_profiles } from './student_profile';

export const postEngagementsType = ['like', 'share', 'rsvp'] as const;

export const postEngagementsSchema = pgTable('post_engagements', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  student_profile_id: uuid('student_profile_id')
    .notNull()
    .references(() => student_profiles.id, { onDelete: 'cascade' }),
  postId: uuid('postId')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  post_engagement_type: text({ enum: postEngagementsType }).notNull(),
  created_at: timestamp('created_at').notNull().defaultNow(),
});

export const postEngagementsRelations = relations(
  postEngagementsSchema,
  ({ one }) => ({
    student_profile: one(student_profiles, {
      fields: [postEngagementsSchema.student_profile_id],
      references: [student_profiles.id],
    }),
    post: one(posts, {
      fields: [postEngagementsSchema.postId],
      references: [posts.id],
    }),
  }),
);

export type PostEngagement = typeof postEngagementsSchema.$inferSelect;
export type PostEngagementInput = typeof postEngagementsSchema.$inferInsert;
export type PostEngagementType =
  (typeof postEngagementsType)[keyof typeof postEngagementsType];
export const post_engagement_types = {
  like: 'like',
  share: 'share',
  rsvp: 'rsvp',
} as const;
