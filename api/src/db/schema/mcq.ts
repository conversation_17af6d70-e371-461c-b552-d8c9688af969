import { sql } from 'drizzle-orm';
import { varchar, uuid, timestamp, pgTable } from 'drizzle-orm/pg-core';
import { users } from './users';
import { relations } from 'drizzle-orm';
import { createInsertSchema } from 'drizzle-zod';
import { unique } from 'drizzle-orm/pg-core';

export const questionBank = pgTable(
  'question_bank',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: varchar('name').notNull().unique(),
    stack: varchar('stack').notNull(),
    framework: varchar('framework').notNull(),
    imageUrl: varchar('imageUrl').notNull(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    created_at: timestamp('created_at').notNull().defaultNow(),
    updated_at: timestamp('updated_at', { mode: 'string' })
      .notNull()
      .default(sql`now()`)
      .$onUpdate(() => sql`now()`),
  },
  (table) => ({
    unq: unique().on(table.name, table.framework),
  }),
);

export const questionBankRelations = relations(questionBank, ({ one }) => ({
  createdBy: one(users, {
    fields: [questionBank.createdBy],
    references: [users.id],
  }),
}));

export const insertQuestionBankSchema = createInsertSchema(questionBank).omit({
  imageUrl: true,
  id: true,
});

export type QuestionBank = typeof questionBank.$inferSelect;
export type QuestionBankInput = typeof questionBank.$inferInsert;
