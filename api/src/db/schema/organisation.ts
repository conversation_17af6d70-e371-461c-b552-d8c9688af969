import { pgTable, text, boolean, timestamp, uuid } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';
import { users } from './users';
import { createSelectSchema, createInsertSchema } from 'drizzle-zod';

export const organisations = pgTable('organisations', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),

  name: text('name').notNull(),
  user_id: uuid('user_id')
    .unique()
    .references(() => users.id, { onDelete: 'set null' }),
  email: text('email').notNull().unique(),
  contact: text('contact'),
  address: text('address'),
  organization_banner_url: text('organization_banner_url'),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
  disabled: boolean('disabled').default(false),
  deleted_at: timestamp('deleted_at', { mode: 'string' }),
  deleted: text('deleted').default('false'),
});

export const organisationRelations = relations(organisations, ({ one }) => ({
  creator: one(users, {
    fields: [organisations.user_id],
    references: [users.id],
  }),
}));

export type Organisation = typeof organisations.$inferSelect;
export const organisationKeys = Object.keys(organisations) as [
  string,
  ...string[],
];
export const selectOrganisation = createSelectSchema(organisations);
export const insertOrganisation = createInsertSchema(organisations);
