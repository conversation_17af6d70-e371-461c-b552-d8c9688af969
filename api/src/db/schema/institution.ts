import { boolean, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { createInsertSchema } from 'drizzle-zod';
import { countries } from './countries';
import { relations, sql } from 'drizzle-orm';
import { student_profiles } from './student_profile';
import { uuid } from 'drizzle-orm/pg-core';

export const institutions = pgTable('institutions', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  name: text('name').notNull().unique(),
  location: text('location').notNull(),
  address: text('address').notNull(),
  city: text('city').notNull(),
  state: text('state'),
  domain: text('domain'),
  country_id: uuid('country_id')
    .references(() => countries.id)
    .notNull(),
  disabled: boolean('disabled').default(false),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});
export const institutionsRelations = relations(
  institutions,
  ({ one, many }) => ({
    country: one(countries, {
      fields: [institutions.country_id],
      references: [countries.id],
    }),
    students: many(student_profiles),
  }),
);
export const insertInstitution = createInsertSchema(institutions);
export const institutionKeys = Object.keys(institutions) as [
  string,
  ...string[],
];
export type Institution = typeof institutions.$inferInsert;
