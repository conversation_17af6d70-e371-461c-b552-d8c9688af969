import { relations, sql } from 'drizzle-orm';
import { boolean, pgTable, uuid, text, timestamp } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { institutions } from './institution';
import { postCountries } from './posts';
import { z } from 'nestjs-zod/z';

export const countries = pgTable('countries', {
  id: uuid('id')
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  name: text('name').notNull().unique(),
  code: text('code').notNull(),
  disabled: boolean('disabled').default(false),
  created_at: timestamp('created_at', { mode: 'string' })
    .notNull()
    .defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

export const countriesRelations = relations(countries, ({ many }) => ({
  institutions: many(institutions),
  posts: many(postCountries),
}));

export const insertCountrySchema = createInsertSchema(countries).extend({
  code: z.string().min(2).max(3).trim(),
  name: z.string().min(2).max(100).trim(),
});

export const selectCountrySchema = createSelectSchema(countries);
export type Country = typeof countries.$inferSelect;

export const countryKeys = Object.keys(countries) as [string, ...string[]];
