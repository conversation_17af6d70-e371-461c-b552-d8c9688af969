import {
  pgTable,
  text,
  timestamp,
  uuid,
  json,
  integer,
  boolean,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm/sql';
import { createInsertSchema } from 'drizzle-zod';
import { student_profiles } from './student_profile';
import { institutions } from './institution';
import { users } from './users';

export const statusType = ['active', 'draft', 'expired'] as const;
// Define the Raffles table
export const raffleSchema = pgTable('raffles', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  description: text('description'),
  startDate: timestamp('start_date', { mode: 'string' }).notNull(),
  endDate: timestamp('end_date', { mode: 'string' }).notNull(),
  maxParticipants: integer('max_participants').notNull(),
  totalWinners: integer('total_winners').notNull(),
  institutionId: uuid('institution_id').references(() => institutions.id),
  degree: jsonb('degree'),
  scheduled: boolean('scheduled').notNull().default(false),
  programme: jsonb('programme'),
  level: jsonb('level'),
  createdBy: uuid('created_by').references(() => users.id, {
    onDelete: 'set null',
  }),
  status: text({ enum: statusType }).notNull().default('active'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

export const raffleInstitutionsSchema = pgTable('raffle_institutions', {
  id: uuid('id').primaryKey().defaultRandom(),
  raffleId: uuid('raffle_id').references(() => raffleSchema.id),
  institutionId: uuid('institution_id').references(() => institutions.id),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// Define the Prizes table
export const prizeSchema = pgTable('raffle_prizes', {
  id: uuid('id').primaryKey().defaultRandom(),
  raffleId: uuid('raffle_id').references(() => raffleSchema.id),
  type: text('type').notNull(),
  details: json('details').notNull(),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// Define the Participants table
export const participantSchema = pgTable('raffle_participants', {
  id: uuid('id').primaryKey().defaultRandom(),
  raffleId: uuid('raffle_id').references(() => raffleSchema.id),
  studentId: uuid('student_id')
    .references(() => student_profiles.id, { onDelete: 'cascade' })
    .notNull(),
  entryDate: timestamp('entry_date').notNull().defaultNow(),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// Define the Winners table
export const winnersSchema = pgTable('raffle_winners', {
  id: uuid('id').primaryKey().defaultRandom(),
  raffleId: uuid('raffle_id').references(() => raffleSchema.id),
  participantId: uuid('student_id').references(() => student_profiles.id, {
    onDelete: 'cascade',
  }),
  selectionDate: timestamp('selection_date').notNull().defaultNow(),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at', { mode: 'string' })
    .notNull()
    .default(sql`now()`)
    .$onUpdate(() => sql`now()`),
});

// Infer the model types using $inferSelect
export type Raffle = typeof raffleSchema.$inferSelect;
export type Prize = typeof prizeSchema.$inferSelect;
export type Participant = typeof participantSchema.$inferSelect;
export type Winner = typeof winnersSchema.$inferSelect;

// Create the insert schema for the Raffles table
export const insertRaffleSchema = createInsertSchema(raffleSchema).omit({
  id: true,
  created_at: true,
  updated_at: true,
});
