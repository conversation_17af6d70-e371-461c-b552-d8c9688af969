import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Injectable, Logger } from '@nestjs/common';
import {
  notification_templates,
  notification_types,
} from '@/db/schema/notification_system';
import { NotificationModule } from '@app/shared/constants/notification.constant';

@Injectable()
export class EventNotificationSeed {
  private readonly logger = new Logger(EventNotificationSeed.name);

  constructor(private readonly drizzle: DrizzleService) {}

  async seed() {
    this.logger.log('Seeding event notification templates and types...');

    try {
      // Create event reminder template
      const [eventReminderTemplate] = await this.drizzle.db
        .insert(notification_templates)
        .values({
          name: 'Event Reminder',
          description: 'Template for event reminder notifications',
          title_template: '{{minutesUntilStart}} minute reminder: {{title}}',
          body_template:
            'Your event "{{title}}" starts in {{minutesUntilStart}} minutes at {{formattedTime}} on {{formattedDate}}.',
          email_subject_template: 'Reminder: {{title}} starts soon',
          email_body_template: `Event Reminder

Hello,
This is a reminder that the event "{{title}}" is starting in {{minutesUntilStart}} minutes.

Date: {{formattedDate}}
Time: {{formattedTime}}
Description: {{description}}

We look forward to seeing you there!`,
        })
        .returning();

      if (!eventReminderTemplate) {
        this.logger.error('Failed to create event reminder template');
        return;
      }

      // Create event reminder notification type
      await this.drizzle.db.insert(notification_types).values({
        code: 'event_reminder',
        name: 'Event Reminder',
        description: 'Notification for upcoming events',
        module: NotificationModule.EVENT,
        template_id: eventReminderTemplate.id,
        default_channels: ['push', 'email'],
      });

      this.logger.log(
        'Event notification templates and types seeded successfully',
      );
    } catch (error) {
      this.logger.error(
        'Error seeding event notification templates and types',
        error,
      );
    }
  }
}
