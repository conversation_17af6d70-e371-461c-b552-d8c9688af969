import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EventNotificationSeed } from '@/db/seeds/event-notification.seed';

@Injectable()
export class EventNotificationSeedService {
  private readonly logger = new Logger(EventNotificationSeedService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly eventNotificationSeed: EventNotificationSeed,
  ) {}

  async seed() {
    this.logger.log('Seeding event notification templates and types...');
    try {
      await this.eventNotificationSeed.seed();
      this.logger.log(
        'Event notification templates and types seeded successfully',
      );
    } catch (error) {
      this.logger.error(
        'Error seeding event notification templates and types',
        error,
      );
      throw error;
    }
  }
}
