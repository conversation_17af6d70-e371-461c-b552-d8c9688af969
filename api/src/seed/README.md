# Notification Seeding Architecture

This directory contains the services and commands for seeding notification templates and types in the database.

## Architecture

The notification seeding system follows a modular architecture:

1. **Base Notification Seed Service** (`BaseNotificationSeedService`)
   - Abstract base class that provides common functionality for all notification seed services
   - Provides methods for creating templates and notification types
   - Defines the `seed()` method that subclasses must implement

2. **Specific Notification Seed Services**
   - `NotificationSeedService`: Seeds standard notification templates and types
   - `EventNotificationSeedService`: Seeds event-related notification templates and types
   - `SelectionNotificationSeedService`: Seeds selection-related notification templates and types (raffle winners, opportunity selections)
   - Each service extends the base service and implements the `seed()` method

3. **Seed Commands**
   - `NotificationSeedCommand`: Command for seeding standard notifications
   - `EventNotificationSeedCommand`: Command for seeding event notifications
   - `SelectionNotificationSeedCommand`: Command for seeding selection notifications
   - `AllNotificationsSeedCommand`: Command for seeding all notification types

## Adding a New Notification Type

To add a new notification type:

1. Create a new service that extends `BaseNotificationSeedService`
2. Implement the `seed()` method to create the templates and types
3. Add the service to the `SeedModule` providers and exports
4. Create a command for the new notification type (optional)
5. Update the `AllNotificationsSeedCommand` to include the new service

## Running Seeds

You can run the seeds using the following commands:

```bash
# Using the CLI commands
npx nest start seed seed:notifications
npx nest start seed seed:event-notifications
npx nest start seed seed:selection-notifications
npx nest start seed seed:all-notifications

# Using npm scripts
npm run seed:notifications
npm run seed:event-notifications
npm run seed:selection-notifications
npm run seed:all-notifications
npm run seed:notifications:unified
```

See the README in the `scripts` directory for more information on the unified seeding approach.
