import { SeedModule } from './seed.module';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { users } from 'src/db/schema/users';
import { INestApplicationContext, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Logger as PinoLogger } from 'nestjs-pino';
import { sql, SQL } from 'drizzle-orm';

async function seed() {
  // Connect to the PostgreSQL database
  let app: INestApplicationContext | undefined;
  let drizzleService: DrizzleService | undefined;
  let seedLogger: Logger | undefined;

  try {
    app = await NestFactory.createApplicationContext(SeedModule, {
      bufferLogs: true,
    });
    seedLogger = new Logger('Seed');
    app.useLogger(app.get(PinoLogger));
    drizzleService = app.get<DrizzleService>(DrizzleService);

    const userEmail = process.env?.SUPER_ADMIN_EMAIL;
    const userRole = process.env?.SUPER_ADMIN_ROLE;
    const userState = process.env?.SUPER_ADMIN_STATE;

    // Ensure the uuid-ossp extension is created
    await drizzleService.db.execute(
      sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
    );

    //Checking if the user already exists
    await drizzleService?.db
      .insert(users)
      .values([
        {
          email: userEmail! as never,
          role: userRole! as never,
          state: userState! as unknown as SQL<unknown>,
          profile_pic_url: sql`null`,
        },
      ])
      .execute();
    seedLogger.log('Seed completed successfully.');
  } catch (error) {
    seedLogger?.error(`Error seeding database: ${error}`);
  } finally {
    await drizzleService?.close();
    await app?.close();
  }
}
// Run the seed function
seed();
