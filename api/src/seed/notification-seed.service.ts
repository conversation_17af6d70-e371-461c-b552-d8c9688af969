import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  notification_templates,
  notification_types,
} from '@/db/schema/notification_system';
import { eq } from 'drizzle-orm';
import { NotificationTemplateService } from '@app/shared/notification/notification-template.service';
import { NotificationTypeService } from '@app/shared/notification/notification-type.service';

@Injectable()
export class NotificationSeedService {
  private readonly logger = new Logger(NotificationSeedService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly templateService: NotificationTemplateService,
    private readonly typeService: NotificationTypeService,
  ) {}

  async seed() {
    this.logger.log('Seeding notification templates and types...');

    // Define template-type pairs for seeding
    const templateTypePairs = [
      {
        template: {
          name: 'Quiz Notification',
          description: 'Template for quiz-related notifications',
          title_template: '{{title}}',
          body_template: '{{body}}',
          email_subject_template: '{{title}}',
          email_body_template:
            '{{body}}\n\nLog in to the app to attempt the quiz.',
        },
        types: [
          {
            code: 'new_quiz',
            name: 'New Quiz',
            description: 'Notification for when a new quiz is created',
            module: 'quiz',
            default_channels: ['email', 'push'],
          },
          {
            code: 'active_quiz',
            name: 'Active Quiz',
            description: 'Notification for when a quiz becomes active',
            module: 'quiz',
            default_channels: ['push'],
          },
        ],
      },
      {
        template: {
          name: 'Post Notification',
          description: 'Template for post-related notifications',
          title_template: '{{title}}',
          body_template: '{{body}}',
          email_subject_template: '{{title}}',
          email_body_template:
            '{{body}}\n\nLog in to the app to view the post.',
        },
        types: [
          {
            code: 'new_post',
            name: 'New Post',
            description: 'Notification for when a new post is created',
            module: 'post',
            default_channels: ['push'],
          },
          {
            code: 'new_event',
            name: 'New Event',
            description: 'Notification for when a new event is created',
            module: 'event',
            default_channels: ['email', 'push'],
          },
          {
            code: 'new_opportunity',
            name: 'New Opportunity',
            description: 'Notification for when a new opportunity is created',
            module: 'opportunity',
            default_channels: ['email', 'push'],
          },
        ],
      },
      {
        template: {
          name: 'Announcement Notification',
          description: 'Template for announcements',
          title_template: '{{title}}',
          body_template: '{{body}}',
          email_subject_template: '{{title}}',
          email_body_template: '{{body}}',
        },
        types: [
          {
            code: 'announcement',
            name: 'Announcement',
            description: 'General announcement notification',
            module: 'announcement',
            default_channels: ['email', 'push'],
          },
        ],
      },
    ];

    // Process each template-type pair
    for (const pair of templateTypePairs) {
      // Check if template exists
      const existingTemplate = await this.drizzle.db
        .select()
        .from(notification_templates)
        .where(eq(notification_templates.name, pair.template.name))
        .then((results) => results[0]);

      if (existingTemplate) {
        this.logger.log(`Template already exists: ${pair.template.name}`);

        // Process types for existing template
        for (const type of pair.types) {
          const existingType = await this.drizzle.db
            .select()
            .from(notification_types)
            .where(eq(notification_types.code, type.code))
            .then((results) => results[0]);

          if (existingType) {
            this.logger.log(`Notification type already exists: ${type.name}`);

            // Update the type if needed
            await this.typeService.updateNotificationType(existingType.id, {
              name: type.name,
              description: type.description,
              module: type.module,
              default_channels: type.default_channels,
            });
          } else {
            // Create new type with existing template
            await this.typeService.createNotificationType(
              {
                ...type,
                template_id: existingTemplate.id,
              },
              true,
            ); // true indicates this is a seed operation
            this.logger.log(`Created notification type: ${type.name}`);
          }
        }
      } else {
        // Create new template
        this.logger.log(`Creating template: ${pair.template.name}`);

        // Create the first type along with the template
        const firstType = pair.types[0];
        if (firstType) {
          await this.templateService.createNotificationTemplate(pair.template, {
            ...firstType,
            isSeedOperation: true,
          });
          this.logger.log(
            `Created template with type: ${pair.template.name} - ${firstType.name}`,
          );

          // Create the rest of the types for this template
          if (pair.types.length > 1) {
            // Get the newly created template
            const newTemplate = await this.drizzle.db
              .select()
              .from(notification_templates)
              .where(eq(notification_templates.name, pair.template.name))
              .then((results) => results[0]);

            // Create the remaining types
            for (let i = 1; i < pair.types.length; i++) {
              if (newTemplate && pair.types[i]) {
                const currentType = pair.types[i];

                // Ensure all required properties are present
                const typeData = {
                  code: currentType?.code || `type_${i}_${Date.now()}`,
                  name: currentType?.name || `Type ${i}`,
                  description:
                    currentType?.description ||
                    'Auto-generated notification type',
                  module: currentType?.module || 'general',
                  template_id: newTemplate.id,
                  default_channels: currentType?.default_channels || ['push'],
                };

                await this.typeService.createNotificationType(typeData, true);
                this.logger.log(
                  `Created additional notification type: ${typeData.name}`,
                );
              } else {
                this.logger.warn(
                  `Skipping notification type creation due to missing template or type data at index ${i}`,
                );
              }
            }
          }
        } else {
          // Just create the template without a type (should be rare)
          await this.templateService.createNotificationTemplate(pair.template);
          this.logger.log(
            `Created template without type: ${pair.template.name}`,
          );
        }
      }
    }

    this.logger.log('Notification templates and types seeding completed');
  }
}
