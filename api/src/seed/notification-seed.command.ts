import { Command, CommandRunner } from 'nest-commander';
import { NotificationSeedService } from './notification-seed.service';
import { Logger } from '@nestjs/common';

@Command({
  name: 'seed:notifications',
  description: 'Seed notification templates and types',
})
export class NotificationSeedCommand extends CommandRunner {
  private readonly logger = new Logger(NotificationSeedCommand.name);

  constructor(
    private readonly notificationSeedService: NotificationSeedService,
  ) {
    super();
  }

  async run(): Promise<void> {
    try {
      this.logger.log('Starting notification seed...');
      await this.notificationSeedService.seed();
      this.logger.log('Notification seed completed successfully');
      process.exit(0);
    } catch (error) {
      this.logger.error('Failed to seed notifications', error);
      process.exit(1);
    }
  }
}
