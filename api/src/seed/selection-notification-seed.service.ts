import { Injectable } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { NotificationModule } from '@app/shared/constants/notification.constant';
import { BaseNotificationSeedService } from './base-notification-seed.service';

@Injectable()
export class SelectionNotificationSeedService extends BaseNotificationSeedService {
  constructor(protected override readonly drizzle: DrizzleService) {
    super(drizzle);
  }

  async seed() {
    this.logger.log('Seeding selection notification templates and types...');

    try {
      // Create raffle winner template
      const [raffleWinnerTemplate] = await this.createTemplateIfNotExists({
        name: 'Raffle Winner',
        description: 'Template for raffle winner notifications',
        title_template: '🎉 Congratulations, You Won!',
        body_template:
          "You've been selected as a winner in the {{raffleName}} raffle!",
        email_subject_template: '🎉 Congratulations, You Won!',
        email_body_template: `Congratulations!

We're excited to inform you that you've been selected as a winner in the {{raffleName}} raffle!

Raffle: {{raffleName}}
{{#if raffleDescription}}
Description: {{raffleDescription}}
{{/if}}
{{#if prize}}
Prize: {{prize}}
{{/if}}

To claim your prize, please log in to your account.
{{#if claimDeadline}}
Important: Please claim your prize by {{claimDeadline}}.
{{/if}}`,
      });

      // Create opportunity selection template
      const [opportunitySelectionTemplate] =
        await this.createTemplateIfNotExists({
          name: 'Opportunity Selection',
          description: 'Template for opportunity selection notifications',
          title_template: "🎉 You've Been Selected!",
          body_template:
            "You've been selected for the {{opportunityTitle}} opportunity!",
          email_subject_template: "🎉 You've Been Selected!",
          email_body_template: `Congratulations!

We're pleased to inform you that you've been selected for the {{opportunityTitle}} opportunity!

Opportunity: {{opportunityTitle}}
{{#if opportunityDescription}}
Description: {{opportunityDescription}}
{{/if}}
{{#if startDate}}
Start Date: {{startDate}}
{{/if}}
{{#if endDate}}
End Date: {{endDate}}
{{/if}}

To confirm your participation, please log in to your account.
{{#if confirmDeadline}}
Important: Please confirm your participation by {{confirmDeadline}}.
{{/if}}`,
        });

      // Create notification types
      await this.createNotificationTypeIfNotExists({
        code: 'raffle_winner',
        name: 'Raffle Winner',
        description: 'Notification for raffle winners',
        module: NotificationModule.RAFFLE,
        template_id: raffleWinnerTemplate!.id,
        default_channels: ['email', 'push', 'in_app'],
      });

      await this.createNotificationTypeIfNotExists({
        code: 'opportunity_selection',
        name: 'Opportunity Selection',
        description: 'Notification for opportunity selections',
        module: NotificationModule.OPPORTUNITY,
        template_id: opportunitySelectionTemplate!.id,
        default_channels: ['email', 'push', 'in_app'],
      });

      this.logger.log(
        'Selection notification templates and types seeded successfully',
      );
    } catch (error) {
      this.logger.error(
        'Error seeding selection notification templates and types',
        error,
      );
      throw error;
    }
  }
}
