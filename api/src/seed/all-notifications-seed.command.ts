import { Command, CommandRunner } from 'nest-commander';
import { NotificationSeedService } from './notification-seed.service';
import { EventNotificationSeedService } from './event-notification-seed.service';
import { SelectionNotificationSeedService } from './selection-notification-seed.service';
import { Logger } from '@nestjs/common';

@Command({
  name: 'seed:all-notifications',
  description:
    'Seed all notification templates and types (standard, event, selection, and any future types)',
})
export class AllNotificationsSeedCommand extends CommandRunner {
  private readonly logger = new Logger(AllNotificationsSeedCommand.name);

  constructor(
    private readonly notificationSeedService: NotificationSeedService,
    private readonly eventNotificationSeedService: EventNotificationSeedService,
    private readonly selectionNotificationSeedService: SelectionNotificationSeedService,
  ) {
    super();
  }

  async run(): Promise<void> {
    try {
      this.logger.log('Starting all notifications seed...');

      // Get all seed services from the constructor
      const seedServices = [
        { name: 'standard', service: this.notificationSeedService },
        { name: 'event', service: this.eventNotificationSeedService },
        { name: 'selection', service: this.selectionNotificationSeedService },
      ];

      // Seed each notification type
      for (const { name, service } of seedServices) {
        this.logger.log(`Seeding ${name} notifications...`);
        await service.seed();
        this.logger.log(
          `${name.charAt(0).toUpperCase() + name.slice(1)} notifications seeded successfully`,
        );
      }

      this.logger.log('All notifications seed completed successfully');
      process.exit(0);
    } catch (error) {
      this.logger.error('Failed to seed all notifications', error);
      process.exit(1);
    }
  }
}
