import { Command, CommandRunner } from 'nest-commander';
import { SelectionNotificationSeedService } from './selection-notification-seed.service';
import { Logger } from '@nestjs/common';

@Command({
  name: 'seed:selection-notifications',
  description:
    'Seed selection notification templates and types (raffle winners and opportunity selections)',
})
export class SelectionNotificationSeedCommand extends CommandRunner {
  private readonly logger = new Logger(SelectionNotificationSeedCommand.name);

  constructor(
    private readonly selectionNotificationSeedService: SelectionNotificationSeedService,
  ) {
    super();
  }

  async run(): Promise<void> {
    try {
      this.logger.log('Starting selection notifications seed...');
      await this.selectionNotificationSeedService.seed();
      this.logger.log('Selection notifications seed completed successfully');
      process.exit(0);
    } catch (error) {
      this.logger.error('Failed to seed selection notifications', error);
      process.exit(1);
    }
  }
}
