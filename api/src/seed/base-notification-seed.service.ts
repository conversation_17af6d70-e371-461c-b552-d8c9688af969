import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  notification_templates,
  notification_types,
} from '@/db/schema/notification_system';
import { eq } from 'drizzle-orm';

@Injectable()
export abstract class BaseNotificationSeedService {
  protected readonly logger: Logger;

  constructor(protected readonly drizzle: DrizzleService) {
    this.logger = new Logger(this.constructor.name);
  }

  /**
   * Seed notification templates and types
   * This method should be implemented by subclasses
   */
  abstract seed(): Promise<void>;

  /**
   * Create a notification template if it doesn't exist
   * @param template The template to create
   * @returns The created or existing template
   */
  protected async createTemplateIfNotExists(template: {
    name: string;
    description: string;
    title_template: string;
    body_template: string;
    email_subject_template?: string;
    email_body_template?: string;
  }) {
    const existingTemplate = await this.drizzle.db
      .select()
      .from(notification_templates)
      .where(eq(notification_templates.name, template.name));

    if (existingTemplate.length === 0) {
      const result = await this.drizzle.db
        .insert(notification_templates)
        .values(template)
        .returning();
      this.logger.log(`Created template: ${template.name}`);
      return result;
    } else {
      this.logger.log(`Template already exists: ${template.name}`);
      return existingTemplate;
    }
  }

  /**
   * Create a notification type if it doesn't exist
   * @param type The notification type to create
   */
  protected async createNotificationTypeIfNotExists(type: {
    code: string;
    name: string;
    description: string;
    module: string;
    template_id: string;
    default_channels: string[];
  }) {
    const existingType = await this.drizzle.db
      .select()
      .from(notification_types)
      .where(eq(notification_types.code, type.code));

    if (existingType.length === 0) {
      await this.drizzle.db.insert(notification_types).values(type);
      this.logger.log(`Created notification type: ${type.name}`);
    } else {
      this.logger.log(`Notification type already exists: ${type.name}`);
    }
  }
}
