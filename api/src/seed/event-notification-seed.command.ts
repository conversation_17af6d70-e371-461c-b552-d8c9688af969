import { Command, CommandRunner } from 'nest-commander';
import { EventNotificationSeedService } from './event-notification-seed.service';
import { Logger } from '@nestjs/common';

@Command({
  name: 'seed:event-notifications',
  description: 'Seed event notification templates and types',
})
export class EventNotificationSeedCommand extends CommandRunner {
  private readonly logger = new Logger(EventNotificationSeedCommand.name);

  constructor(
    private readonly eventNotificationSeedService: EventNotificationSeedService,
  ) {
    super();
  }

  async run(): Promise<void> {
    try {
      this.logger.log('Starting event notification seed...');
      await this.eventNotificationSeedService.seed();
      this.logger.log('Event notification seed completed successfully');
      process.exit(0);
    } catch (error) {
      this.logger.error('Failed to seed event notifications', error);
      process.exit(1);
    }
  }
}
