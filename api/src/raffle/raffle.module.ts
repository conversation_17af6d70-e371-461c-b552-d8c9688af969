import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON><PERSON>Controller } from './raffle.controller';
import { RaffleService } from './raffle.service';
import { RaffleRepository } from './repositories/raffle.repository';
import { RaffleScheduler } from './raffle.scheduler';
import { StudentProfileService } from '@/student_profile/student_profile.service';
import { NotificationModule } from '@app/shared/notification/notification.module';
import { PointSystemModule } from '@/point-system/point_system.module';
import { SelectionNotificationModule } from '@/notification/selection-notification.module';
import { RepositoriesModule } from '@/repositories/repositories.module';
import { EmailModule } from '@/mail/email.module';
import { McqModule } from '@/mcq/mcq.module';

@Module({
  imports: [
    PointSystemModule,
    SelectionNotificationModule,
    RepositoriesModule,
    EmailModule,
    NotificationModule,
    McqModule,
  ],
  controllers: [RaffleController],
  providers: [
    RaffleService,
    RaffleRepository,
    RaffleScheduler,
    StudentProfileService,
  ],
})
export class RaffleModule {}
