import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { RaffleData, RaffleDto, RaffleFilter } from './dto/index.dto';
import { RaffleRepository } from './repositories/raffle.repository';
import { StudentProfileService } from '@/student_profile/student_profile.service';
import { EmailService } from '@/mail/email.service';
import { UserRepository } from '@/repositories/user.repository';
import { SelectionNotificationService } from '@/notification/selection-notification.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import * as crypto from 'crypto';

@Injectable()
export class RaffleService {
  constructor(
    private readonly raffleRepository: RaffleRepository,
    private studentProfileService: StudentProfileService,
    private selectionNotificationService: SelectionNotificationService,
    private emailService: EmailService,
    private userRepository: UserRepository,
    private readonly drizzle: DrizzleService,
  ) {}
  private readonly logger = new Logger(RaffleService.name);
  /**
   * Validate raffle data and ensure eligibility
   * @param data RaffleDto containing programme, level, degree, institutionId, maxParticipants, totalWinners
   * @returns An object with eligible students and total count
   */
  private async validateRaffleData(data: RaffleDto) {
    const {
      programme,
      level,
      degree,
      institutionId,
      maxParticipants,
      totalWinners,
    } = data;
    const { students, total } = await this.studentProfileService.getAllStudents(
      institutionId,
      programme,
      level,
      degree,
    );

    if (total !== undefined && total < maxParticipants) {
      throw new BadRequestException(
        `Not enough students to meet the max participants requirement. Eligible students: ${total}`,
      );
    }
    if (totalWinners && totalWinners > maxParticipants) {
      throw new BadRequestException(
        `Total winners cannot be more than the max participants`,
      );
    }

    return { students, total };
  }

  /**
   * Add participants to a raffle
   * @param students Array of student objects
   * @param raffleId ID of the raffle
   * @param maxParticipants Maximum number of participants to add
   * @returns Array of selected participants
   */
  private async addParticipantsToRaffle(
    students: any[],
    raffleId: string,
    maxParticipants: number,
  ) {
    if (!students || !Array.isArray(students) || students.length === 0) {
      this.logger.warn(`No students provided for raffle ${raffleId}`);
      return [];
    }

    // Ensure maxParticipants is valid
    const participantCount = Math.max(
      1,
      Math.min(maxParticipants || 1, students.length),
    );
    this.logger.log(
      `Selecting ${participantCount} participants from ${students.length} eligible students`,
    );

    // Select random participants
    const selectedParticipants = await this.getRandomElements(
      students,
      participantCount,
      false,
    );

    // Add each participant to the raffle
    const addedParticipants = [];
    for (const participant of selectedParticipants) {
      try {
        if (!participant || !participant.id) {
          this.logger.warn(
            `Invalid participant data for raffle ${raffleId}, skipping`,
          );
          continue;
        }

        // Check if participant already exists
        const existingEntry =
          await this.raffleRepository.getParticipantByUserIdAndRaffleId(
            participant.id,
            raffleId,
          );

        if (existingEntry) {
          this.logger.warn(
            `Participant ${participant.id} already exists in raffle ${raffleId}, skipping`,
          );
          addedParticipants.push(participant); // Still count as added since they're in the raffle
          continue;
        }

        // Add participant to raffle
        await this.raffleRepository.addParticipant({
          studentId: participant.id,
          raffleId,
          entryDate: new Date(),
        });

        addedParticipants.push(participant);
      } catch (error: any) {
        this.logger.error(
          `Error adding participant ${participant?.id} to raffle ${raffleId}: ${error.message}`,
        );
        // Continue with other participants
      }
    }

    this.logger.log(
      `Successfully added ${addedParticipants.length} participants to raffle ${raffleId}`,
    );
    return addedParticipants;
  }

  /**
   * Select winners for a raffle and queue notifications
   * @param raffleId ID of the raffle
   * @param totalWinners Number of winners to select
   * @param raffleData Optional parameter to pass raffle data directly
   * @param useWeightedSelection Whether to use weighted selection based on previous wins
   * @param cooldownDays Number of days to consider for cooldown period
   * @returns Array of winner objects
   */
  private async selectAndAddWinners(
    raffleId: string,
    totalWinners: number,
    raffleData?: any,
    useWeightedSelection = false,
    cooldownDays = 30,
  ) {
    const participants =
      await this.raffleRepository.getParticipantsByRaffleId(raffleId);
    if (!participants || participants.length === 0) {
      throw new BadRequestException(
        `No participants found for raffle ${raffleId}`,
      );
    }

    if (participants.length < totalWinners) {
      this.logger.warn(
        `Not enough participants (${participants.length}) for requested winners (${totalWinners}). Adjusting winner count.`,
      );
      totalWinners = participants.length;
    }

    // Use provided raffle data or fetch it
    let raffle = raffleData;
    if (!raffle) {
      try {
        // Try to get raffle details using the simple query first
        raffle = await this.raffleRepository.getRaffleByIdSimple(raffleId);
      } catch (error: any) {
        this.logger.error(
          `Error getting raffle by simple query: ${error.message || 'Unknown error'}`,
        );

        // Fall back to the complex query
        const raffleDetails =
          await this.raffleRepository.getRaffleById(raffleId);
        if (!raffleDetails) {
          throw new BadRequestException(`Raffle with id ${raffleId} not found`);
        }

        raffle = raffleDetails;
      }
    }

    if (!raffle) {
      throw new BadRequestException(
        `Raffle with id ${raffleId} not found or has invalid data`,
      );
    }

    // Use weighted selection for winners if specified
    const winners = await this.getRandomElements(
      participants,
      totalWinners,
      useWeightedSelection,
      raffleId,
      cooldownDays,
    );

    for (const winner of winners) {
      // Add winner to database
      await this.raffleRepository.addWinner({
        raffleId,
        userId: winner.studentId,
      });
    }
    // Send notifications to winners
    winners.forEach((winner: any) => {
      try {
        if (!winner || !winner.studentId) {
          this.logger.error(`Invalid winner data for raffle ${raffleId}`);
          return;
        }

        this.selectionNotificationService
          .sendRaffleWinnerNotification(
            winner.studentId,
            raffleId,
            raffle.name || 'Raffle',
            raffle.description || 'You have won a raffle!',
          )
          .then(() => {
            this.logger.log(
              `Queued winner notification to user ${winner.studentId} for raffle ${raffleId}`,
            );
          })
          .catch((error: any) => {
            this.logger.error(
              `Failed to queue winner notification to user ${winner.studentId}: ${error.message}`,
              error.stack,
            );
          });
      } catch (error: any) {
        this.logger.error(
          `Error processing notification for winner ${winner?.studentId}: ${error.message}`,
          error.stack,
        );
      }
    });

    // Send notification to raffle creator
    //TODO WHEN STORY IS WRITTEN FOR
    // await this.sendRaffleCreatorNotification(raffleId, winners.length);

    return winners;
  }

  /**
   * Create a new raffle and, if instant, select winners and send notifications
   * @param data RaffleDto containing raffle details
   * @returns Raffle summary or created raffle object
   */
  async createRaffle(data: RaffleDto) {
    try {
      const { students } = await this.validateRaffleData(data);

      // Prepare raffle data with default dates if not provided
      const raffleData = {
        ...data,
        startDate: data.startDate ? new Date(data.startDate) : new Date(),
        endDate: data.endDate ? new Date(data.endDate) : new Date(),
      };

      // Create the raffle
      const raffle = await this.raffleRepository.createRaffle(raffleData);

      if (!raffle) {
        throw new BadRequestException('Failed to create raffle');
      }

      this.logger.log(`Raffle created with ID: ${raffle.id}`);

      // For instant raffles, add participants and select winners
      if (!data.scheduled) {
        this.logger.log('Instant Raffle - Adding participants');

        // Add participants to the raffle
        const selectedParticipants = await this.addParticipantsToRaffle(
          students,
          raffle.id,
          data.maxParticipants,
        );

        this.logger.log(
          `Added ${selectedParticipants.length} participants to raffle ${raffle.id}`,
        );

        // Small delay to ensure database consistency
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Select winners
        const totalWinners = data.totalWinners || 1;
        this.logger.log(
          `Selecting ${totalWinners} winners for raffle ${raffle.id}`,
        );

        try {
          const winners = await this.selectAndAddWinners(
            raffle.id,
            totalWinners,
            raffle,
            data.useWeightedSelection,
            data.cooldownDays || 30,
          );

          // Create summary
          const summary = {
            raffleId: raffle.id,
            title: raffle.name,
            numberOfParticipants: selectedParticipants.length,
            numberOfWinners: winners.length,
            universities: data.institutionId
              ? await this.raffleRepository.getUniversitiesByIds(
                  data.institutionId.map(String),
                )
              : [],
            degree: data.degree ?? 'N/A',
            level: raffle.level ?? 'N/A',
          };

          return {
            status: 'success',
            message:
              'Raffle executed instantly and winners selected successfully.',
            summary,
          };
        } catch (winnerError: any) {
          this.logger.error(
            `Error selecting winners: ${winnerError.message}`,
            winnerError.stack,
          );

          // Return partial success even if winner selection failed
          return {
            status: 'partial_success',
            message:
              'Raffle created and participants added, but winner selection failed. You can select winners manually.',
            raffleId: raffle.id,
            error: winnerError.message,
          };
        }
      } else {
        this.logger.log('Raffle scheduled. Saved without executing');
      }

      return raffle;
    } catch (error: any) {
      this.logger.error(`Error creating raffle: ${error.message}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create raffle: ${error.message}`,
      );
    }
  }

  /**
   * Get all raffles with optional filter parameters
   * @param param RaffleFilter object (status, limit, page)
   * @returns Object with data array and total count
   */
  async getAllRafflesWithParams(
    param: RaffleFilter = { status: 'active', limit: 10, page: 1 },
  ): Promise<{ data: RaffleData[]; total: number }> {
    return this.raffleRepository.getAllRafflesWithParams(param);
  }
  /**
   * Enter a student into a raffle
   * @param studentId ID of the student
   * @param raffleId ID of the raffle
   * @returns The participant entry
   */
  async enterRaffle(studentId: string, raffleId: string) {
    const raffle = await this.raffleRepository.getRaffleById(raffleId);
    const currentDate = new Date();

    if (!raffle || raffle.length === 0) {
      throw new BadRequestException(`Raffle with id ${raffleId} not found`);
    }
    if (raffle && raffle[0]) {
      const { endDate } = raffle[0];
      if (new Date(endDate) < currentDate) {
        this.logger.warn('Raffle has already ended');
      }
    }

    const existingEntry =
      await this.raffleRepository.getParticipantByUserIdAndRaffleId(
        studentId,
        raffleId,
      );
    if (existingEntry) {
      this.logger.warn('User has already entered the raffle');
    }

    const participantData = {
      studentId,
      raffleId,
      entryDate: currentDate,
    };
    return await this.raffleRepository.addParticipant(participantData);
  }

  /**
   * Select winners for a specific raffle
   * @param raffleId ID of the raffle
   * @param useWeightedSelection Whether to use weighted selection based on previous wins
   * @param cooldownDays Number of days to consider for cooldown period
   * @returns Array of winner objects
   */
  async selectWinners(
    raffleId: string,
    useWeightedSelection = false,
    cooldownDays = 30,
  ) {
    const raffle = await this.raffleRepository.getRaffleById(raffleId);
    if (!raffle || raffle.length === 0) {
      throw new BadRequestException(`Raffle with id ${raffleId} not found`);
    }

    const { totalWinners = 10 } = raffle[0] || {};
    return await this.selectAndAddWinners(
      raffleId,
      totalWinners,
      raffle[0],
      useWeightedSelection,
      cooldownDays,
    );
  }

  /**
   * Run all active raffles, add participants, and select winners if raffle ends
   * Enhanced with proper transaction management and error handling
   */
  async runRaffle() {
    try {
      const activeRaffles = await this.getAllRafflesWithParams();
      const { data } = activeRaffles;
      const currentDate = new Date();

      if (!data || data.length === 0) {
        this.logger.debug('No active raffles found to process');
        return;
      }

      this.logger.log(`Processing ${data.length} active raffles`);

      // Process each raffle in a separate transaction to avoid deadlocks
      for (const raffle of data) {
        await this.processRaffleWithTransaction(raffle, currentDate);
      }

      this.logger.log('Successfully completed raffle processing');
    } catch (error: any) {
      this.logger.error('Failed to run raffle job:', {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Process a single raffle within a transaction
   */
  private async processRaffleWithTransaction(raffle: any, currentDate: Date) {
    const { id, endDate, maxParticipants } = raffle;

    try {
      await this.drizzle.db.transaction(async (tx) => {
        const daysRemaining = Math.ceil(
          (new Date(endDate).getTime() - currentDate.getTime()) /
            (1000 * 60 * 60 * 24),
        );

        if (daysRemaining <= 0) {
          this.logger.debug(
            `Raffle ${id} has ended, skipping participant addition`,
          );
          return;
        }

        const participantsPerDay = Math.ceil(maxParticipants / daysRemaining);

        // Get students with timeout protection
        const { students } = (await Promise.race([
          this.studentProfileService.getAllStudents(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Student fetch timeout')), 10000),
          ),
        ])) as any;

        const selectedParticipants = await this.getRandomElements(
          students,
          participantsPerDay,
          false,
        );

        // Batch insert participants to reduce transaction time
        if (selectedParticipants.length > 0) {
          const participantData = selectedParticipants.map((participant) => ({
            studentId: participant.id,
            raffleId: id,
            entryDate: new Date(),
          }));

          await this.raffleRepository.batchAddParticipants(participantData, tx);
          this.logger.debug(
            `Added ${selectedParticipants.length} participants to raffle ${id}`,
          );
        }

        // Check if raffle ends today and select winners
        if (new Date(endDate).toDateString() === currentDate.toDateString()) {
          await this.selectWinners(id);
          this.logger.log(`Selected winners for completed raffle ${id}`);
        }
      });
    } catch (error: any) {
      this.logger.error(`Failed to process raffle ${id}:`, {
        message: error.message,
        stack: error.stack,
        raffleId: id,
      });
      // Continue with other raffles instead of failing completely
    }
  }

  /**
   * Update an existing raffle
   * @param data RaffleDto with updated data
   * @param id ID of the raffle to update
   * @returns The updated raffle object
   */
  async updateRaffle(data: RaffleDto, id: string) {
    return this.raffleRepository.updateRaffle(data, id);
  }

  /**
   * Get a random subset of elements from an array
   * @param array Source array
   * @param count Number of elements to select
   * @param useWeightedSelection Whether to use weighted selection based on previous wins
   * @param raffleId Current raffle ID (for excluding from previous wins count)
   * @param cooldownDays Number of days to consider for cooldown period
   * @returns Array of randomly selected elements
   */
  private async getRandomElements(
    array: any[],
    count: number,
    useWeightedSelection = false,
    raffleId?: string,
    cooldownDays = 30,
  ) {
    // If not using weighted selection, use the simple shuffle method
    if (!useWeightedSelection) {
      const shuffled = this.raffleRepository.shuffle(array);
      return shuffled.slice(0, count);
    }

    // For weighted selection, we need to calculate weights based on previous wins
    try {
      // Create a copy to avoid modifying the original array
      const arrayWithWeights = [...array];

      // Calculate weights for each participant
      for (let i = 0; i < arrayWithWeights.length; i++) {
        const item = arrayWithWeights[i];
        if (!item || !item.id) {
          // Skip invalid items
          arrayWithWeights[i] = { ...item, weight: 0 };
          continue;
        }

        // Get previous wins count
        const previousWins = await this.raffleRepository.getPreviousWins(
          item.id,
          raffleId,
          cooldownDays,
        );

        // Calculate weight - higher weight for fewer wins
        // Base weight of 10, subtract 2 for each previous win (minimum weight of 1)
        const weight = Math.max(1, 10 - previousWins * 2);

        // Store the weight with the item
        arrayWithWeights[i] = { ...item, weight };
      }

      // Filter out items with zero weight
      const validItems = arrayWithWeights.filter((item) => item.weight > 0);

      // If no valid items remain, fall back to random selection
      if (validItems.length === 0) {
        this.logger.warn(
          'No valid weighted items found, falling back to random selection',
        );
        const shuffled = this.raffleRepository.shuffle(array);
        return shuffled.slice(0, count);
      }

      // Calculate total weight
      const totalWeight = validItems.reduce(
        (sum, item) => sum + item.weight,
        0,
      );

      // Select items based on weight
      const selected = [];
      const remainingItems = [...validItems];

      while (selected.length < count && remainingItems.length > 0) {
        // Generate a random value between 0 and the total weight
        const randomValue = Math.random() * totalWeight;

        let cumulativeWeight = 0;
        let selectedIndex = -1;

        // Find the item that corresponds to the random value
        for (let i = 0; i < remainingItems.length; i++) {
          cumulativeWeight += remainingItems[i].weight;
          if (randomValue <= cumulativeWeight) {
            selectedIndex = i;
            break;
          }
        }

        // If no item was selected (shouldn't happen), select the last one
        if (selectedIndex === -1) {
          selectedIndex = remainingItems.length - 1;
        }

        // Add the selected item to the result
        const selectedItem = remainingItems[selectedIndex];
        selected.push(selectedItem);

        // Remove the selected item from the remaining items
        remainingItems.splice(selectedIndex, 1);
      }

      // Return the selected items without the weight property
      return selected.map((item) => {
        // Create a new object without the weight property
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { weight, ...rest } = item;
        return rest;
      });
    } catch (error: any) {
      this.logger.error(
        `Error in weighted selection: ${error.message}`,
        error.stack,
      );
      // Fall back to random selection on error
      const shuffled = this.raffleRepository.shuffle(array);
      return shuffled.slice(0, count);
    }
  }

  /**
   * Get a random subset of elements with verification
   * @param array Source array
   * @param count Number of elements to select
   * @param raffleId ID of the raffle for storing the verification seed
   * @returns Array of randomly selected elements with verification seed
   */
  /**
   * Get a random subset of elements with verification
   * This method is currently not used but is available for future implementation
   * of a more transparent selection process
   * @param array Source array
   * @param count Number of elements to select
   * @param raffleId ID of the raffle for storing the verification seed
   * @returns Array of randomly selected elements with verification seed
   */
  private async getVerifiableRandomElements(
    array: any[],
    count: number,
    raffleId: string,
  ) {
    try {
      // Generate a public seed for verification
      const publicSeed = crypto.randomBytes(16).toString('hex');

      // Store the seed for verification
      await this.raffleRepository.storeRandomSeed(raffleId, publicSeed);

      // Create a deterministic but random ordering using the seed
      const orderedElements = array
        .map((element) => {
          // Create a hash combining the element ID and the public seed
          const hash = crypto
            .createHash('sha256')
            .update(
              `${element.id || element.studentId || JSON.stringify(element)}-${publicSeed}`,
            )
            .digest('hex');

          return { element, hash };
        })
        .sort((a, b) => a.hash.localeCompare(b.hash))
        .map((item) => item.element);

      // Take the first 'count' elements
      return orderedElements.slice(0, Math.min(count, orderedElements.length));
    } catch (error: any) {
      this.logger.error(
        `Error in verifiable selection: ${error.message}`,
        error.stack,
      );
      // Fall back to random selection on error
      return this.raffleRepository.shuffle(array).slice(0, count);
    }
  }

  /**
   * Select raffle winners with filter
   * @param data RaffleFilter object
   * @returns Array of winner objects
   */
  async selectRaffleWinners(data: RaffleFilter) {
    try {
      return await this.raffleRepository.selectRaffleWinners(data);
    } catch (error: any) {
      // Propagate NotFoundException to the controller
      if (error instanceof NotFoundException) {
        throw error;
      }
      // For other errors, log and rethrow
      this.logger.error(
        `Error in selectRaffleWinners: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get all raffle winners with filter
   * @param data Filter object
   * @returns Object with winners data and total count
   */
  async selectAllRaffleWinners(data: any = {}) {
    try {
      // Ensure we have default values for pagination
      const filter = {
        limit: data.limit || 10,
        page: data.page || 1,
        raffleId: data.raffleId,
      };

      const result = await this.raffleRepository.getAllRaffleWinners(filter);

      // Add a message if no winners are found
      if (result.total === 0) {
        return {
          ...result,
          message: 'No raffle winners found',
        };
      }

      return result;
    } catch (error: any) {
      this.logger.error(`Error getting all raffle winners: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add participants to all active raffles based on schedule
   * @returns void
   */
  async addParticipant(): Promise<any> {
    const activeRaffles = await this.getAllRafflesWithParams();
    const { data } = activeRaffles;
    const currentDate = new Date();

    for (const raffle of data) {
      const { id, endDate, startDate, maxParticipants } = raffle;
      const start = new Date(startDate);
      const end = new Date(endDate);
      const daysRemaining =
        Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) +
        1;
      const participantsPerDay = Math.ceil(maxParticipants / daysRemaining);

      if (currentDate >= start && currentDate <= end) {
        const { students } = await this.studentProfileService.getAllStudents();
        const selectedParticipants = await this.getRandomElements(
          students,
          participantsPerDay,
          false,
        );

        for (const participant of selectedParticipants) {
          await this.enterRaffle(participant.id, id);
        }

        if (currentDate.toDateString() === end.toDateString()) {
          await this.selectWinners(id);
        }
      }
    }
  }

  /**
   * Instantly run a raffle, select participants and winners
   * @param data Object containing raffleId, institutionId, program, level
   * @returns Array of winner objects
   */
  async instantRaffle(data: any) {
    if (!data || !data.raffleId) {
      throw new BadRequestException(
        'Invalid raffle data: raffleId is required',
      );
    }

    const { raffleId, institutionId, program, level } = data;
    const raffle = await this.raffleRepository.getRaffleById(raffleId);
    if (!raffle || raffle.length === 0) {
      throw new BadRequestException(`Raffle with id ${raffleId} not found`);
    }

    const raffleData = raffle[0];
    if (!raffleData) {
      throw new BadRequestException(`Raffle with id ${raffleId} not found`);
    }

    // Ensure required properties exist
    const maxParticipants = raffleData.maxParticipants || 10;
    const totalWinners = raffleData.totalWinners || 1;

    try {
      const { total } = await this.studentProfileService.getAllStudents(
        institutionId,
        program,
        level,
      );

      if (maxParticipants > total) {
        throw new BadRequestException(
          'Not enough students to meet the max participants requirement',
        );
      }

      // Get students data instead of using the data object directly
      const { students } = await this.studentProfileService.getAllStudents(
        institutionId,
        program,
        level,
      );

      if (!students || !Array.isArray(students) || students.length === 0) {
        throw new BadRequestException(
          'No eligible students found for the raffle',
        );
      }

      const selectedParticipants = await this.getRandomElements(
        students,
        maxParticipants,
        false,
      );
      this.logger.log(
        `Selected ${selectedParticipants.length} participants for raffle ${raffleId}`,
      );

      // Add participants to raffle
      for (const participant of selectedParticipants) {
        if (!participant || !participant.id) {
          this.logger.warn('Invalid participant data, skipping');
          continue;
        }

        try {
          await this.raffleRepository.addParticipant({
            studentId: participant.id,
            raffleId,
            entryDate: new Date(),
          });
        } catch (error: any) {
          this.logger.error(
            `Failed to add participant ${participant.id}: ${error.message}`,
          );
        }
      }

      // Get all participants for the raffle
      const getParticipants =
        await this.raffleRepository.getParticipantsByRaffleId(raffleId);
      if (!getParticipants || getParticipants.length === 0) {
        throw new BadRequestException('No participants found for the raffle');
      }

      // Get raffle options
      const useWeightedSelection =
        data.useWeightedSelection !== undefined
          ? data.useWeightedSelection
          : raffleData.useWeightedSelection || false;

      const cooldownDays = data.cooldownDays || raffleData.cooldownDays || 30;

      // Select winners
      const winnersCount = Math.min(totalWinners, getParticipants.length);
      const winners = await this.getRandomElements(
        getParticipants,
        winnersCount,
        useWeightedSelection,
        raffleId,
        cooldownDays,
      );

      // Add winners to database
      for (const winner of winners) {
        if (!winner || !winner.studentId) {
          this.logger.warn('Invalid winner data, skipping');
          continue;
        }

        try {
          await this.raffleRepository.addWinner({
            raffleId,
            userId: winner.studentId,
          });
        } catch (error: any) {
          this.logger.error(
            `Failed to add winner ${winner.studentId}: ${error.message}`,
          );
        }
      }

      // Send notification to raffle creator
      await this.sendRaffleCreatorNotification(raffleId, winners.length);

      return winners;
    } catch (error: any) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(
        `Error running instant raffle: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to run instant raffle: ${error.message}`,
      );
    }
  }

  /**
   * Get all participants for a raffle
   * @param data RaffleFilter object
   * @returns Array of participant objects
   */
  async getAllRaffleParticipants(data: RaffleFilter) {
    // Log the winner filter value for debugging
    if (data.winner !== undefined) {
      // The winner value should already be a proper boolean from our DTO schema
      this.logger.debug(
        `Getting raffle participants with winner filter: ${data.winner} (type: ${typeof data.winner})`,
      );
    }
    return this.raffleRepository.getAllRaffleParticipants(data);
  }

  /**
   * Activate all raffles
   * @returns Result of activation
   */
  async activateRaffle() {
    return this.raffleRepository.activateRaffle();
  }

  /**
   * Deactivate all raffles
   * @returns Result of deactivation
   */
  async deactivateRaffle() {
    return this.raffleRepository.deactivateRaffle();
  }

  /**
   * Get winners for a specific raffle
   * @param raffleId ID of the raffle
   * @returns Array of winner objects
   */
  /**
   * Get winners for a specific raffle with better error handling
   * @param raffleId ID of the raffle
   * @returns Object with winners data and total count
   */
  async getWinner(raffleId: string) {
    try {
      // First check if the raffle exists
      const raffle = await this.raffleRepository.getRaffleByIdSimple(raffleId);
      if (!raffle) {
        this.logger.warn(`Raffle with ID ${raffleId} not found`);
        return { data: [], total: 0, message: 'Raffle not found' };
      }

      // Get winners
      const getWinners = await this.raffleRepository.selectRaffleWinners({
        raffleId: raffleId,
        limit: 50,
        page: 1,
      });

      if (getWinners.total === 0) {
        return {
          data: [],
          total: 0,
          message: `No winners found for raffle "${raffle.name}"`,
          raffle: {
            id: raffle.id,
            name: raffle.name,
            description: raffle.description,
            status: raffle.status,
          },
        };
      }

      return {
        ...getWinners,
        raffle: {
          id: raffle.id,
          name: raffle.name,
          description: raffle.description,
          status: raffle.status,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Error getting winners for raffle ${raffleId}: ${error.message}`,
      );
      if (error.message.includes('not found')) {
        return { data: [], total: 0, message: 'Raffle not found' };
      }
      throw error;
    }
  }

  /**
   * Get a raffle by its ID
   * @param id ID of the raffle
   * @returns Raffle object
   */
  async getRaffleById(id: string) {
    return this.raffleRepository.getRaffleById(id);
  }

  /**
   * Send notification to raffle creator when a raffle is successfully run
   * @param raffleId ID of the raffle that was run
   * @param winnersCount Number of winners selected
   * @returns void
   */
  private async sendRaffleCreatorNotification(
    raffleId: string,
    winnersCount: number,
  ): Promise<void> {
    try {
      // Get the raffle details
      const raffle = await this.raffleRepository.getRaffleByIdSimple(raffleId);
      if (!raffle || !raffle.createdBy) {
        this.logger.warn(
          `Cannot send creator notification: Raffle ${raffleId} not found or has no creator`,
        );
        return;
      }

      // Get the creator's user information
      const creator = await this.userRepository.getUserByKey(
        'id',
        raffle.createdBy,
      );
      if (!creator || !creator.email) {
        this.logger.warn(
          `Cannot send creator notification: Creator ${raffle.createdBy} not found or has no email`,
        );
        return;
      }

      // Format dates for display
      const runDate = new Date().toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });

      // Send email notification to the creator
      await this.emailService.sendCustomEmail({
        email: creator.email,
        subject: `Raffle "${raffle.name}" Successfully Run`,
        template: 'notification',
        context: {
          title: `Raffle "${raffle.name}" Successfully Run`,
          greeting: `Hello,`,
          message: `
            <p>Your raffle <strong>${raffle.name}</strong> was successfully run on ${runDate}.</p>
            <p><strong>Raffle Details:</strong></p>
            <ul>
              <li><strong>Raffle Name:</strong> ${raffle.name}</li>
              <li><strong>Description:</strong> ${raffle.description || 'N/A'}</li>
              <li><strong>Winners Selected:</strong> ${winnersCount}</li>
              <li><strong>Run Date:</strong> ${runDate}</li>
            </ul>
            <p>You can view the winners by visiting the raffle details page.</p>
          `,
          actionUrl: `/raffles/${raffleId}`,
          actionText: 'View Raffle Results',
        },
      });

      this.logger.log(
        `Raffle creator notification sent to ${creator.email} for raffle ${raffleId}`,
      );
    } catch (error: any) {
      // Log error but don't throw to prevent disrupting the main flow
      this.logger.error(
        `Failed to send raffle creator notification: ${error.message}`,
        error.stack,
      );
    }
  }
}
