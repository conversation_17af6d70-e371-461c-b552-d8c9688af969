import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { RaffleService } from './raffle.service';
import { NotificationService } from '@app/shared/notification/notification.service';
import { NotificationEvent } from '@app/shared/constants/notification.constant';

@Injectable()
export class RaffleScheduler {
  private readonly logger = new Logger(RaffleScheduler.name);

  constructor(
    private readonly raffleService: RaffleService,
    private readonly notificationService: NotificationService,
  ) {}
  //Destructure NotificationEvent

  // Stagger execution to avoid conflicts with leaderboard refresh (runs at 2 minutes past every 5-minute interval)
  @Cron('2 */5 * * * *')
  async runRaffleJob() {
    const startTime = Date.now();

    try {
      this.logger.log('Starting raffle job execution');
      await this.raffleService.runRaffle();

      //Send notification to an Admin
      const { RAFFLE } = NotificationEvent;
      this.notificationService.sendNotificationToStream(
        RAFFLE,
        'Raffle has been run',
      );

      const duration = Date.now() - startTime;
      this.logger.log(`Raffle job completed successfully in ${duration}ms`);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error('Failed to run raffle job', {
        message: error.message,
        stack: error.stack,
        duration,
        timestamp: new Date().toISOString(),
      });

      // Don't throw in production to prevent scheduler crashes
      if (process.env.NODE_ENV !== 'production') {
        throw error;
      }
    }
  }
  @Cron(CronExpression.EVERY_HOUR)
  async selectParticipantsJob() {
    try {
      await this.raffleService.getAllRafflesWithParams();
    } catch (error: any) {
      this.logger.error('Failed to select participants', {
        message: error.message,
        stack: error.stack,
      });
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async activateRaffleJob() {
    try {
      await this.raffleService.activateRaffle();
      await this.raffleService.deactivateRaffle();
    } catch (error: any) {
      this.logger.error('Failed to activate/deactivate raffles', {
        message: error.message,
        stack: error.stack,
      });
    }
  }
}
