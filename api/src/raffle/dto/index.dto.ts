import { z } from 'zod';
import {
  insertRaffleSchema,
  studentDegrees,
  studentProgrammes,
} from '@/db/schema';
import { createZodDto } from 'nestjs-zod';
import { createQueryBooleanSchema } from '@/common/utils/zod-schema.utils';

const insertRaffle = insertRaffleSchema.extend({
  name: z.string(),
  totalWinners: z
    .number({ message: 'Must be a positive Integer' })
    .int()
    .positive()
    .optional(),
  description: z
    .string()
    .min(5, { message: 'Must have a minimum of  10' })
    .optional(),
  startDate: z
    .date()
    .refine((date) => date >= new Date(), {
      message: 'Start date must be now or in the future',
    })
    .optional(),
  endDate: z
    .date()
    .refine((date) => date >= new Date(), {
      message: 'End date must be now or in the future',
    })
    .optional(),
  institutionId: z.array(z.string().uuid('Invalid institution ID')).optional(),
  degree: z.enum(studentDegrees).optional(),
  programme: z.enum(studentProgrammes).optional(),
  level: z.array(z.number().int().positive()).optional(),
  scheduled: z.boolean().default(false),
  createdBy: z.string().uuid('Invalid ID of createdBy').optional(),
  // New fields for enhanced raffle selection
  useWeightedSelection: z.boolean().default(false).optional(),
  cooldownDays: z.number().int().positive().optional(),
  useVerifiableSelection: z.boolean().default(false).optional(),
});
export class RaffleSchemaDto extends createZodDto(insertRaffle) {}
export type RaffleDto = z.infer<typeof insertRaffle>;

const raffleFilter = z.object({
  institutionId: z.string().optional(),
  program: z.string().optional(),
  level: z.coerce.number().optional(),
  raffleId: z.string().min(20).optional(),
  search: z.string().optional(),
  sort: z.string().optional(),
  order: z.enum(['asc', 'desc']).optional(),
  status: z.enum(['active', 'draft', 'expired']).optional(),
  limit: z.coerce.number().int().positive().optional().default(10),
  page: z.coerce.number().int().positive().optional().default(1),
  winner: createQueryBooleanSchema(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  createdBy: z.string().uuid('Invalid createdBy ID').optional(),
  // New fields for enhanced raffle selection
  useWeightedSelection: createQueryBooleanSchema(false),
  cooldownDays: z.union([z.number(), z.string()]).optional(),
  useVerifiableSelection: createQueryBooleanSchema(false),
});

const raffleData = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  maxParticipants: z.number(),
  totalWinners: z.number(),
  degree: z.array(z.string()),
  scheduled: z.boolean(),
  programme: z.array(z.string()),
  level: z.string(),
  status: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
  created_by: z.string().uuid('Invalid Created by ID'),
  institutions: z.array(z.any()),
});

export type RaffleData = z.infer<typeof raffleData>;
export type RaffleFilter = z.infer<typeof raffleFilter>;
export class queryParamsRaffleDto extends createZodDto(raffleFilter) {}
