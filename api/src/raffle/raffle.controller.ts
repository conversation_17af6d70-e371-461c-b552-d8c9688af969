import {
  Controller,
  Get,
  Post,
  Logger,
  UseGuards,
  Body,
  Put,
  Param,
  Query,
  NotFoundException,
} from '@nestjs/common';
import { RaffleService } from './raffle.service';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiTags,
  ApiNotFoundResponse,
  ApiOperation,
  ApiBody,
  ApiCreatedResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import * as indexDto from './dto/index.dto';
import { ZodValidationPipe } from 'nestjs-zod';
import { User } from '@/guards/user.decorator';
import { type User as UserDecoratorType } from '@/db/schema';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { RaffleRoutes } from '@app/shared/constants/raffle.constants';

@ApiTags('Raffle')
@Controller({ version: '1', path: 'raffle' })
export class RaffleController {
  constructor(private readonly raffleService: RaffleService) {}
  private readonly logger = new Logger(RaffleController.name);

  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'raffle', action: 'create', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  @ApiOperation({
    summary: 'Create a new raffle',
    description:
      'Create a new raffle with specified parameters for random selection of participants and winners',
  })
  @ApiBody({
    type: indexDto.RaffleSchemaDto,
    description: 'Raffle creation parameters',
  })
  @ApiCreatedResponse({
    description: 'Raffle has been successfully created',
  })
  @ApiBadRequestResponse({
    description:
      'Invalid raffle data provided or insufficient eligible students',
  })
  createRaffle(
    @Body() data: indexDto.RaffleDto,
    @User() user: UserDecoratorType,
  ): Promise<any> {
    try {
      data.createdBy = user.id;
      return this.raffleService.createRaffle(data);
    } catch (error: any) {
      this.logger.error('Failed to create raffle', error.message);
      throw error;
    }
  }

  @Get()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'raffle', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  @ApiOperation({
    summary: 'Get all raffles',
    description:
      'Retrieve a list of all raffles with optional filtering parameters',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter raffles by status (active, completed, etc.)',
    type: String,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of raffles to return',
    type: Number,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiOkResponse({
    description: 'List of raffles retrieved successfully',
  })
  getAllRafflesWithParams(
    @Query(new ZodValidationPipe(indexDto.queryParamsRaffleDto))
    query: indexDto.RaffleFilter,
  ): Promise<{ data: indexDto.RaffleData[]; total: number }> {
    try {
      return this.raffleService.getAllRafflesWithParams(query);
    } catch (error: any) {
      this.logger.error('Failed to get raffles', error.stack);
      throw error;
    }
  }

  @Put()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'raffle', action: 'update', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  @ApiOperation({
    summary: 'Update a raffle',
    description: 'Update an existing raffle with new parameters',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the raffle to update',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiBody({
    type: indexDto.RaffleSchemaDto,
    description: 'Updated raffle data',
  })
  @ApiOkResponse({
    description: 'Raffle updated successfully',
  })
  @ApiNotFoundResponse({
    description: 'Raffle with the specified ID was not found',
  })
  async updateRaffle(
    @Body() data: indexDto.RaffleDto,
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ) {
    try {
      return this.raffleService.updateRaffle(data, id);
    } catch (error: any) {
      this.logger.error('Failed to update raffle', error.stack);
      throw error;
    }
  }
  @Get(RaffleRoutes.GET_WINNERS)
  @ApiOperation({
    summary: 'Get or select raffle winners',
    description:
      'Retrieve winners for a specific raffle or select new winners if requested',
  })
  @ApiParam({
    name: 'raffleId',
    description: 'ID of the raffle to get winners for',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiQuery({
    name: 'selectNew',
    required: false,
    description:
      'Whether to select new winners (true) or just retrieve existing ones (false)',
    type: Boolean,
  })
  @ApiQuery({
    name: 'useWeightedSelection',
    required: false,
    description: 'Whether to use weighted selection based on previous wins',
    type: Boolean,
  })
  @ApiQuery({
    name: 'cooldownDays',
    required: false,
    description: 'Number of days to consider for the cooldown period',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Returns raffle winners for the specified raffle ID',
  })
  @ApiNotFoundResponse({
    description: 'Raffle with the specified ID was not found',
  })
  @UseRoles({ resource: 'raffle', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async selectWin(
    @Param('raffleId', new CustomParseUUIDPipe()) raffleId: string,
    @Query('selectNew') selectNew?: string,
    @Query('useWeightedSelection') useWeightedSelection?: string,
    @Query('cooldownDays', new CustomParseUUIDPipe({ optional: true }))
    cooldownDays?: string,
  ): Promise<any> {
    try {
      // Create a proper filter object with the raffleId
      const filter: indexDto.RaffleFilter = {
        raffleId,
        limit: 10,
        page: 1,
      };

      // Check if we should select new winners
      if (selectNew === 'true') {
        // Parse options for weighted selection
        const useWeightedSelectionBool = useWeightedSelection === 'true';
        const cooldownDaysNum = cooldownDays ? parseInt(cooldownDays, 10) : 30;

        this.logger.log(`Selecting new winners for raffle ${raffleId}`);
        return await this.raffleService.selectWinners(
          raffleId,
          useWeightedSelectionBool,
          cooldownDaysNum,
        );
      }

      // Otherwise just fetch existing winners
      this.logger.log(`Fetching winners for raffle ${raffleId}`);
      return await this.raffleService.selectRaffleWinners(filter);
    } catch (error: any) {
      this.logger.error(
        `Failed to get winners for raffle ${raffleId}`,
        error.stack,
      );

      // Handle NotFoundException specifically to return a 404 response
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw error;
    }
  }

  @Post(RaffleRoutes.RUN)
  @ApiOperation({
    summary: 'Run a raffle immediately',
    description:
      'Execute a raffle immediately, selecting participants and winners',
  })
  @ApiQuery({
    name: 'raffleId',
    required: true,
    description: 'ID of the raffle to run',
    type: String,
  })
  @ApiQuery({
    name: 'useWeightedSelection',
    required: false,
    description: 'Whether to use weighted selection based on previous wins',
    type: Boolean,
  })
  @ApiQuery({
    name: 'cooldownDays',
    required: false,
    description: 'Number of days to consider for the cooldown period',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Raffle executed successfully with winners selected',
  })
  @ApiBadRequestResponse({
    description:
      'Failed to run raffle due to invalid parameters or insufficient participants',
  })
  @ApiNotFoundResponse({
    description: 'Raffle with the specified ID was not found',
  })
  async runRuffleNow(
    @Query(new ZodValidationPipe(indexDto.queryParamsRaffleDto))
    query: indexDto.queryParamsRaffleDto,
  ) {
    try {
      // Add support for weighted selection and cooldown period
      return this.raffleService.instantRaffle({
        ...query,
        useWeightedSelection: Boolean(query.useWeightedSelection),
        cooldownDays: query.cooldownDays
          ? parseInt(query.cooldownDays as string, 10)
          : 30,
      });
    } catch (error: any) {
      this.logger.error('Failed to run raffle', error.stack);
      throw error;
    }
  }

  @Get(RaffleRoutes.GET_ALL_WINNERS)
  @ApiOperation({
    summary: 'Get all raffle winners',
    description: 'Retrieve winners across all raffles with optional filtering',
  })
  @ApiQuery({
    name: 'raffleId',
    required: false,
    description: 'Filter winners by raffle ID',
    type: String,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of winners to return',
    type: Number,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Raffle winners retrieved successfully',
  })
  @UseRoles({ resource: 'raffle', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  selectWinners(
    @Query(new ZodValidationPipe(indexDto.queryParamsRaffleDto))
    query: indexDto.queryParamsRaffleDto,
  ): Promise<any> {
    try {
      return this.raffleService.selectAllRaffleWinners(query);
    } catch (error: any) {
      this.logger.error('Failed to get winners', error.stack);
      throw error;
    }
  }

  @Get(RaffleRoutes.GET_PARTICIPANTS)
  @ApiOperation({
    summary: 'Get raffle participants',
    description:
      'Retrieve participants for a specific raffle or across all raffles',
  })
  @ApiQuery({
    name: 'raffleId',
    required: false,
    description: 'Filter participants by raffle ID',
    type: String,
  })
  @ApiQuery({
    name: 'winner',
    required: false,
    description: 'Filter participants by winner status',
    type: Boolean,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of participants to return',
    type: Number,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Raffle participants retrieved successfully',
  })
  @UseRoles({ resource: 'raffle', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  selectParticipants(
    @Query(new ZodValidationPipe(indexDto.queryParamsRaffleDto))
    query: indexDto.queryParamsRaffleDto,
  ): Promise<any> {
    try {
      return this.raffleService.getAllRaffleParticipants(query);
    } catch (error: any) {
      this.logger.error(
        `Failed to get participants: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(RaffleRoutes.GET_PRIZES)
  @ApiOperation({
    summary: 'Get raffle prizes and winners',
    description:
      'Retrieve prizes and winners for a specific raffle or across all raffles',
  })
  @ApiQuery({
    name: 'raffleId',
    required: false,
    description: 'Raffle ID to filter prizes by',
    type: String,
  })
  @ApiOkResponse({
    description: 'Raffle prizes and winners retrieved successfully',
  })
  @ApiNotFoundResponse({
    description: 'Raffle with the specified ID was not found',
  })
  @UseRoles({ resource: 'raffle', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getWinners(
    @Query('raffleId', new CustomParseUUIDPipe({ optional: true }))
    raffleId?: string,
  ): Promise<any> {
    try {
      if (!raffleId) {
        // If no raffleId is provided, return all winners
        this.logger.log('No raffleId provided, returning all winners');
        return this.raffleService.selectAllRaffleWinners({
          limit: 10,
          page: 1,
        });
      }

      // UUID validation is now handled by the CustomParseUUIDPipe
      return this.raffleService.getWinner(raffleId);
    } catch (error: any) {
      this.logger.error(`Failed to get winners: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get(RaffleRoutes.GET_BY_ID)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'raffle', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  @ApiOperation({
    summary: 'Get raffle by ID',
    description: 'Retrieve detailed information about a specific raffle',
  })
  @ApiParam({
    name: 'id',
    description: 'Raffle ID',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Raffle retrieved successfully',
  })
  @ApiNotFoundResponse({
    description: 'Raffle with the specified ID was not found',
  })
  async getRaffle(@Param('id', new CustomParseUUIDPipe()) id: string) {
    try {
      return this.raffleService.getRaffleById(id);
    } catch (error: any) {
      this.logger.error('Failed to get raffle', error.stack);
      throw error;
    }
  }
}
