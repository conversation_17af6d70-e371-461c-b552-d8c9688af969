import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { clubMembersQueryParamsDto, ClubParams } from './club.dto';
import {
  Club,
  student_club_memberships,
  student_clubs,
  student_club_roles,
  StudentClubRole,
} from '@/db/schema/clubs';
import {
  and,
  asc,
  count,
  desc,
  eq,
  getTableColumns,
  ilike,
  ne,
  or,
  SQL,
  sql,
} from 'drizzle-orm';
import {
  countries,
  institutions,
  student_profiles,
  StudentProfile,
  User,
  user_roles,
  users,
} from '@/db/schema';
import { PaginationParams } from '@/common/interfaces/pagination.interface';
import { EmailService } from '@/mail/email.service';
import { alias } from 'drizzle-orm/pg-core';
import { Foreign_Key_Violation_Error_Code } from '@app/shared/constants/database.constants';
import { CacheService } from '@app/shared/redis/cache.service';
import {
  generateClubKey,
  invalidateAllClubCaches,
  invalidateClubCreationCaches,
} from './utils/cache.utils';

import type { Request } from 'express';
import { AppClients } from '@app/shared/constants/auth.constants';
import { PointConstant } from '@app/shared/constants/points-system.constant';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { CACHE_TTL } from '@app/shared/constants/cache.constant';

@Injectable()
export class ClubService {
  private readonly CACHE_PREFIX = 'club';
  private readonly logger = new Logger(ClubService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly emailService: EmailService,
    private readonly pointSystemRepository: PointSystemRepository,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Adds student club
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param clubInput
   * @param [adminId]
   * @returns
   */
  async addStudentClub(clubInput: ClubParams, adminId?: string) {
    const {
      description,
      name,
      club_admin,
      institution_id,
      country_id,
      club_logo_url,
      club_banner_url,
      is_active,
    } = clubInput;

    // Check for existing club with same name
    const [existingClub] = await this.drizzle.db
      .select({ count: count() })
      .from(student_clubs)
      .where(sql`LOWER(${student_clubs.name}) = LOWER(${name})`);

    if (existingClub && existingClub.count > 0) {
      throw new ConflictException(`Club with name ${name} already exists`);
    }

    try {
      const createdClubData = await this.drizzle.db.transaction(async (tx) => {
        // Create the new club
        const [newClub] = await tx
          .insert(student_clubs)
          .values({
            name: name.trim(),
            description: description,
            club_admin: club_admin ?? adminId,
            institution_id: institution_id,
            country_id: country_id,
            club_logo_url: club_logo_url,
            club_banner_url: club_banner_url,
            is_active: is_active ?? false,
            created_by: adminId,
          })
          .returning();

        if (!newClub) {
          throw new InternalServerErrorException(
            'An error occurred while creating club, Please try again',
          );
        }

        // If club_admin is provided, create membership for admin
        if (club_admin) {
          const [studentProfile] = await tx
            .select()
            .from(student_profiles)
            .where(eq(student_profiles.user_id, club_admin));

          if (!studentProfile) {
            throw new BadRequestException(
              'Club admin Student Profile not found',
            );
          }

          await tx.insert(student_club_memberships).values({
            student_id: studentProfile.id,
            club_id: newClub.id,
            role: student_club_roles.ADMIN,
            is_active: true,
          });
        }

        return newClub;
      });

      // Targeted cache invalidation for club creation
      await invalidateClubCreationCaches(this.cacheService, {
        clubId: createdClubData.id,
        institutionId: createdClubData.institution_id,
        prefix: this.CACHE_PREFIX,
      });

      // Cache the newxs club data
      const cacheKey = generateClubKey(
        this.cacheService,
        createdClubData.id,
        this.CACHE_PREFIX,
      );

      // Get complete club data with relations
      const completeClubData = await this.getClubById(createdClubData.id);
      if (completeClubData) {
        await this.cacheService.set(
          cacheKey,
          completeClubData,
          CACHE_TTL.SEVEN_DAYS,
        );
      }

      return completeClubData || createdClubData;
    } catch (error) {
      this.logger.error('Failed to create student club:', error);
      throw error;
    }
  }
  /**
   * Removes student club
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param clubId
   * @returns
   */
  async removeStudentClub(clubId: string) {
    try {
      const result = await this.drizzle.db.transaction(async (tx) => {
        await tx
          .delete(student_club_memberships)
          .where(eq(student_club_memberships.club_id, clubId));

        return await tx
          .delete(student_clubs)
          .where(eq(student_clubs.id, clubId))
          .returning();
      });

      // Comprehensive cache invalidation for club deletion
      await invalidateAllClubCaches(this.cacheService, {
        clubId,
        prefix: this.CACHE_PREFIX,
        operation: 'delete',
      });

      return result;
    } catch (error: any) {
      if (error?.code === Foreign_Key_Violation_Error_Code) {
        throw new ConflictException(
          'Club has members, remove members before deleting club',
        );
      }
      this.logger.error('Failed to remove student club:', error);
      throw error;
    }
  }
  /**
   * Updates student club
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param clubId
   * @param clubUpdateInput
   * @returns
   */
  async updateStudentClub(clubId: string, clubUpdateInput: ClubParams) {
    const {
      description,
      name,
      institution_id,
      club_logo_url,
      club_banner_url,
      is_active,
    } = clubUpdateInput;

    // Check for existing club with same name, excluding current club
    const [clubWithSameName] = await this.drizzle.db
      .select()
      .from(student_clubs)
      .where(
        and(
          sql`LOWER(${student_clubs.name}) = LOWER(${name})`,
          ne(student_clubs.id, clubId),
        ),
      );

    if (clubWithSameName) {
      throw new BadRequestException(
        `Club with the name ${name} already exists`,
      );
    }

    try {
      const [updatedClub] = await this.drizzle.db
        .update(student_clubs)
        .set({
          description,
          name: name.trim(),
          institution_id,
          club_logo_url,
          club_banner_url,
          is_active,
        })
        .where(eq(student_clubs.id, clubId))
        .returning();

      if (!updatedClub) {
        throw new BadRequestException('Club not found');
      }

      // Comprehensive cache invalidation for club update
      await invalidateAllClubCaches(this.cacheService, {
        clubId,
        institutionId: updatedClub.institution_id,
        prefix: this.CACHE_PREFIX,
        operation: 'update',
      });

      // Get complete club data with relations
      const completeClubData = await this.getClubById(clubId);
      if (completeClubData) {
        const cacheKey = generateClubKey(
          this.cacheService,
          clubId,
          this.CACHE_PREFIX,
        );
        await this.cacheService.set(
          cacheKey,
          completeClubData,
          CACHE_TTL.SEVEN_DAYS,
        );
      }

      return completeClubData || updatedClub;
    } catch (error) {
      this.logger.error('Failed to update student club:', error);
      throw error;
    }
  }
  /**
   * Gets club by id
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param clubId
   * @returns
   */
  async getClubById(clubId: string) {
    // Generate cache key for this club
    const cacheKey = generateClubKey(
      this.cacheService,
      clubId,
      this.CACHE_PREFIX,
    );

    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    // If not in cache, fetch from database
    const clubAdmin = alias(users, 'clubAdmin');
    const [result] = await this.drizzle.db
      .select({
        id: student_clubs.id,
        name: student_clubs.name,
        description: student_clubs.description,
        club_admin: sql`CASE WHEN ${clubAdmin.id} IS NULL THEN to_json(${student_clubs.club_admin}) ELSE json_build_object(
          'id', ${clubAdmin.id},
          'email', ${clubAdmin.email},
          'role', ${clubAdmin.role},
          'state', ${clubAdmin.state},
          'profile_pic_url', ${clubAdmin.profile_pic_url},
          'created_at', ${clubAdmin.created_at},
          'updated_at', ${clubAdmin.updated_at},
          'deleted', ${clubAdmin.deleted},
          'deleted_at', ${clubAdmin.deleted_at}
        ) END`,
        institution_id: student_clubs.institution_id,
        country_id: student_clubs.country_id,
        is_active: student_clubs.is_active,
        club_logo_url: student_clubs.club_logo_url,
        club_banner_url: student_clubs.club_banner_url,
        created_at: student_clubs.created_at,
        updated_at: student_clubs.updated_at,
        created_by: {
          id: users.id,
          email: users.email,
          role: users.role,
          state: users.state,
          profile_pic_url: users.profile_pic_url,
          created_at: users.created_at,
          updated_at: users.updated_at,
          deleted: users.deleted,
          deleted_at: users.deleted_at,
        },
        country: {
          id: countries.id,
          name: countries.name,
          code: countries.code,
          disabled: countries.disabled,
          created_at: countries.created_at,
          updated_at: countries.updated_at,
        },
        institution: {
          id: institutions.id,
          name: institutions.name,
          location: institutions.location,
          address: institutions.address,
          city: institutions.city,
          state: institutions.state,
          domain: institutions.domain,
          country_id: institutions.country_id,
          disabled: institutions.disabled,
          created_at: institutions.created_at,
          updated_at: institutions.updated_at,
        },
        memberCount: this.drizzle.db.$count(
          student_club_memberships,
          and(
            eq(student_club_memberships.club_id, clubId),
            eq(student_club_memberships.is_active, true),
          ),
        ),
      })
      .from(student_clubs)
      .leftJoin(institutions, eq(institutions.id, student_clubs.institution_id))
      .leftJoin(countries, eq(countries.id, student_clubs.country_id))
      .leftJoin(users, eq(users.id, student_clubs.created_by))
      .leftJoin(clubAdmin, eq(clubAdmin.id, student_clubs.club_admin))
      .where(eq(student_clubs.id, clubId));

    // If found, cache the result
    if (result) {
      await this.cacheService.set(cacheKey, result, CACHE_TTL.SEVEN_DAYS);
    }

    return result;
  }
  /**
   * Gets all clubs
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param user
   * @param {
   *       page = 1,
   *       limit = 10,
   *       sort = 'id',
   *       order = 'asc',
   *       search,
   *       status,
   *       country_id,
   *       all,
   *     }
   * @param req
   * @returns
   */
  async getAllClubs(
    user: User,
    {
      page = 1,
      limit = 10,
      sort = 'id',
      order = 'asc',
      search,
      status,
      country_id,
      all,
    }: PaginationParams & {
      sort: keyof Club;
      status?: 'active' | 'inactive';
      country_id: string;
      all?: boolean;
    },
    req: Request,
  ) {
    // Don't use cache for filtered/paginated/search requests
    if (search || !all || status || country_id) {
      return this.getClubsFromDB(
        user,
        {
          page,
          limit,
          sort,
          order,
          search,
          status,
          country_id,
        },
        req,
      );
    }

    // Generate cache key based on user role AND order parameter
    const cacheKey = this.cacheService.generateKey(
      ['all', user.role, order, sort, page.toString(), limit.toString()],
      this.CACHE_PREFIX,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug(
        `Retrieved clubs from cache with order=${order}, sort=${sort}`,
      );
      return cachedData;
    }

    const clubs = await this.getClubsFromDB(
      user,
      {
        page,
        limit,
        sort,
        order,
        search,
        status,
        country_id,
      },
      req,
    );

    // Cache the results with a shorter TTL to ensure fresh data
    await this.cacheService.set(cacheKey, clubs, CACHE_TTL.ONE_HOUR);

    return clubs;
  }

  /**
   * Gets clubs from db
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param user
   * @param {
   *       page = 1,
   *       limit = 10,
   *       sort = 'id',
   *       order = 'asc',
   *       search,
   *       status,
   *       country_id,
   *     }
   * @param req
   * @returns
   */
  private async getClubsFromDB(
    user: User,
    {
      page = 1,
      limit = 10,
      sort = 'id',
      order = 'asc',
      search,
      status,
      country_id,
    }: PaginationParams & {
      sort: keyof Club;
      status?: 'active' | 'inactive';
      country_id: string;
    },
    req: Request,
  ) {
    const client = req.headers['x-client-type'] as string;
    const filters = [];

    // Ensure sort is a valid column in student_clubs
    const validSortColumns = Object.keys(student_clubs);
    const sortColumn = validSortColumns.includes(sort) ? sort : 'id';

    if (status) {
      filters.push(eq(student_clubs.is_active, status === 'active'));
    }
    if (country_id) {
      filters.push(eq(student_clubs.country_id, country_id));
    }
    if (search) {
      filters.push(
        or(
          ilike(student_clubs.name, `%${search}%`),
          ilike(student_clubs.description, `%${search}%`),
        ),
      );
    }

    if (
      user.role === user_roles.STUDENT ||
      user.role === user_roles.STUDENT_ADMIN
    ) {
      return this.getClubInStudentInstitution(
        user.student_profile as StudentProfile,
        {
          page,
          limit,
          sort,
          order,
        },
        filters,
        client,
      );
    }

    const clubAdmin = alias(users, 'clubAdmin');
    const data = await this.drizzle.db
      .select({
        ...getTableColumns(student_clubs),
        club_admin: sql`CASE WHEN ${clubAdmin.id} IS NULL THEN to_json(${student_clubs.club_admin}) ELSE json_build_object(
          'id', ${clubAdmin.id},
          'email', ${clubAdmin.email},
          'role', ${clubAdmin.role},
          'state', ${clubAdmin.state},
          'profile_pic_url', ${clubAdmin.profile_pic_url},
          'created_at', ${clubAdmin.created_at},
          'updated_at', ${clubAdmin.updated_at},
          'deleted', ${clubAdmin.deleted},
          'deleted_at', ${clubAdmin.deleted_at}
        ) END`,
        country: {
          ...getTableColumns(countries),
        },
        institution: {
          ...getTableColumns(institutions),
        },
        created_by: {
          ...getTableColumns(users),
        },
        memberCount: this.drizzle.db.$count(
          student_club_memberships,
          and(
            eq(student_club_memberships.club_id, student_clubs.id),
            eq(student_club_memberships.is_active, true),
          ),
        ),
      })
      .from(student_clubs)
      .where(and(...filters))
      .leftJoin(institutions, eq(institutions.id, student_clubs.institution_id))
      .leftJoin(countries, eq(countries.id, student_clubs.country_id))
      .leftJoin(users, eq(users.id, student_clubs.created_by))
      .leftJoin(clubAdmin, eq(clubAdmin.id, student_clubs.club_admin))
      .groupBy(
        student_clubs.id,
        institutions.id,
        countries.id,
        users.id,
        clubAdmin.id,
      )
      .orderBy(
        order === 'asc'
          ? asc(student_clubs[sortColumn as keyof typeof student_clubs] as any)
          : desc(
              student_clubs[sortColumn as keyof typeof student_clubs] as any,
            ),
      )
      .limit(limit)
      .offset((page - 1) * limit);

    const total = await this.drizzle.db.$count(student_clubs, and(...filters));

    return {
      data,
      total,
    };
  }

  /**
   * Gets club in student institution
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param studentProfile
   * @param {
   *       page = 1,
   *       limit = 10,
   *       sort = 'id',
   *       order = 'asc',
   *     }
   * @param filters
   * @param client
   * @returns
   */
  private async getClubInStudentInstitution(
    studentProfile: StudentProfile,
    {
      page = 1,
      limit = 10,
      sort = 'id',
      order = 'asc',
    }: Omit<PaginationParams, 'search'> & {
      sort: keyof Club;
    },
    filters: (SQL<unknown> | undefined)[],
    client: string,
  ) {
    // Ensure sort is a valid column in student_clubs
    const validSortColumns = Object.keys(student_clubs);
    const sortColumn = validSortColumns.includes(sort) ? sort : 'id';

    this.logger.debug(
      `Sorting by ${sortColumn} in ${order} order for student institution clubs`,
    );

    const cacheKey = this.cacheService.generateKey(
      [
        'student',
        studentProfile.id,
        studentProfile.institution_id,
        page.toString(),
        limit.toString(),
        sortColumn,
        order,
        ...filters.map((f) => f?.toString() || ''),
        client,
      ],
      this.CACHE_PREFIX,
    );

    this.logger.debug(
      `Getting student institution clubs with order=${order}, sort=${sortColumn}, cacheKey=${cacheKey}`,
    );

    // Skip cache for now to ensure we're getting fresh data
    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug(
        `Retrieved student institution clubs from cache with order=${order}, sort=${sortColumn}`,
      );
      return cachedData;
    }

    if (client === AppClients.WEB) {
      filters.push(eq(student_clubs.club_admin, studentProfile.user_id));
    }

    const clubAdmin = alias(users, 'clubAdmin');
    const data = await this.drizzle.db
      .select({
        ...getTableColumns(student_clubs),
        club_admin: sql`CASE WHEN ${clubAdmin.id} IS NULL THEN to_json(${student_clubs.club_admin}) ELSE json_build_object(
          'id', ${clubAdmin.id},
          'email', ${clubAdmin.email},
          'role', ${clubAdmin.role},
          'state', ${clubAdmin.state},
          'profile_pic_url', ${clubAdmin.profile_pic_url},
          'created_at', ${clubAdmin.created_at},
          'updated_at', ${clubAdmin.updated_at},
          'deleted', ${clubAdmin.deleted},
          'deleted_at', ${clubAdmin.deleted_at}
        ) END`,
        country: {
          ...getTableColumns(countries),
        },
        institution: {
          ...getTableColumns(institutions),
        },
        created_by: {
          ...getTableColumns(users),
        },
        student_club_memberships: {
          ...getTableColumns(student_club_memberships),
        },
        memberCount: this.drizzle.db.$count(
          student_club_memberships,
          and(
            eq(student_club_memberships.club_id, student_clubs.id),
            eq(student_club_memberships.is_active, true),
          ),
        ),
      })
      .from(student_clubs)
      .leftJoin(
        student_club_memberships,
        and(
          eq(student_clubs.id, student_club_memberships.club_id),
          eq(student_club_memberships.student_id, studentProfile.id),
          eq(student_club_memberships.is_active, true),
        ),
      )
      .leftJoin(countries, eq(student_clubs.country_id, countries.id))
      .leftJoin(institutions, eq(student_clubs.institution_id, institutions.id))
      .leftJoin(clubAdmin, eq(student_clubs.club_admin, clubAdmin.id))
      .leftJoin(users, eq(student_clubs.created_by, users.id))
      .where(
        and(
          ...filters,
          eq(student_clubs.institution_id, studentProfile.institution_id),
        ),
      )
      .orderBy(
        order === 'asc'
          ? asc(student_clubs[sortColumn as keyof typeof student_clubs] as any)
          : desc(
              student_clubs[sortColumn as keyof typeof student_clubs] as any,
            ),
      )
      .limit(limit)
      .offset((page - 1) * limit)
      .then((res) =>
        res.map((club: any) => {
          club.isMember =
            club?.student_club_memberships !== null &&
            club?.student_club_memberships?.is_active;
          return club;
        }),
      );

    const total = await this.drizzle.db.$count(
      student_clubs,
      and(
        ...filters,
        eq(student_clubs.institution_id, studentProfile.institution_id),
      ),
    );

    const result = {
      data,
      total,
    };

    // Cache the results with a consistent TTL
    await this.cacheService.set(cacheKey, result, CACHE_TTL.TEN_MINUTES);

    this.logger.debug(
      `Cached student institution clubs with order=${order}, sort=${sortColumn}, results=${data.length}`,
    );

    return result;
  }

  /**
   * Gets club members by club id
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param clubId
   * @param {
   *       search,
   *       sort,
   *       order,
   *       limit,
   *       page,
   *       role,
   *       all,
   *       status,
   *     }
   * @returns
   */
  async getClubMembersByClubId(
    clubId: string,
    {
      search,
      sort,
      order,
      limit,
      page,
      role,
      all,
      status,
    }: clubMembersQueryParamsDto & {
      sort: keyof StudentProfile;
    },
  ) {
    this.logger.debug(
      `Getting club members by club ID with order=${order}, sort=${sort}`,
    );

    // Don't use cache for filtered/paginated/search requests
    if (search || !all || status || role) {
      return this.getClubMembersFromDB(clubId, {
        search,
        sort,
        order,
        limit,
        page,
        role,
        all,
        status,
      });
    }

    // Generate cache key based on club ID, order, and sort
    const cacheKey = this.cacheService.generateKey(
      ['members', clubId, order, sort, page.toString(), limit.toString()],
      this.CACHE_PREFIX,
    );

    this.logger.debug(`Cache key for club members: ${cacheKey}`);

    // If not in cache, get from database
    const members = await this.getClubMembersFromDB(clubId, {
      search,
      sort,
      order,
      limit,
      page,
      role,
      all,
      status,
    });

    // Cache the results with a consistent TTL
    await this.cacheService.set(cacheKey, members, CACHE_TTL.TEN_MINUTES);

    this.logger.debug(
      `Cached club members with order=${order}, sort=${sort}, results=${members.data.length}`,
    );

    return members;
  }

  /**
   * Sets the status of a club.
   * @param {string} clubId - The ID of the club.
   * @param {boolean} status - The status to set for the club.
   * @returns {Promise<any>} - A promise that resolves to the result of updating the club status.
   */
  async setClubStatus(clubId: string, status: boolean): Promise<any> {
    const result = await this.drizzle.db
      .update(student_clubs)
      .set({ is_active: status })
      .where(eq(student_clubs.id, clubId))
      .returning();
    return result;
  }

  /**
   * Adds student to club
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param clubId
   * @param studentId
   * @returns
   */
  async addStudentToClub(clubId: string, studentId: string) {
    const [membership] = await this.drizzle.db
      .select({
        ...getTableColumns(student_club_memberships),
        club: { ...getTableColumns(student_clubs) },
        user: {
          id: users.id,
        },
      })
      .from(student_club_memberships)
      .leftJoin(
        student_clubs,
        eq(student_clubs.id, student_club_memberships.club_id),
      )
      .leftJoin(student_profiles, eq(student_profiles.id, studentId))
      .leftJoin(users, eq(users.id, student_profiles.user_id))
      .where(
        and(
          eq(student_club_memberships.student_id, studentId),
          eq(student_club_memberships.club_id, clubId),
        ),
      );

    if (membership?.is_active) {
      throw new ConflictException(
        `Student ${studentId} is already a member of club ${clubId}`,
      );
    }

    try {
      await this.drizzle.db.transaction(async (tx) => {
        // If membership exists but is inactive, update it
        if (membership) {
          await tx
            .update(student_club_memberships)
            .set({ is_active: true })
            .where(
              and(
                eq(student_club_memberships.student_id, studentId),
                eq(student_club_memberships.club_id, clubId),
              ),
            );
        } else {
          // Create new membership
          await tx.insert(student_club_memberships).values({
            student_id: studentId,
            club_id: clubId,
            is_active: true,
            role: student_club_roles.MEMBER,
          });

          //Award Points
          await this.pointSystemRepository.awardPointsToStudent(
            PointConstant.MODULES.CLUB,
            PointConstant.ACTIONS.JOIN_CLUB,
            studentId,
          );
        }

        // Invalidation will be done after transaction completes
      });

      // Get club details to access institution_id for cache invalidation
      const [clubDetails] = await this.drizzle.db
        .select()
        .from(student_clubs)
        .where(eq(student_clubs.id, clubId));

      // Comprehensive cache invalidation for membership change
      await invalidateAllClubCaches(this.cacheService, {
        clubId,
        studentId,
        institutionId: clubDetails?.institution_id,
        prefix: this.CACHE_PREFIX,
        operation: 'membership',
      });

      // Fetch and cache updated club data
      const updatedClub = await this.getClubById(clubId);
      if (updatedClub) {
        const cacheKey = generateClubKey(
          this.cacheService,
          clubId,
          this.CACHE_PREFIX,
        );
        await this.cacheService.set(
          cacheKey,
          updatedClub,
          CACHE_TTL.SEVEN_DAYS,
        );
      }

      // Comprehensive cache invalidation for club update
      await invalidateAllClubCaches(this.cacheService, {
        clubId,
        institutionId: updatedClub.institution_id,
        prefix: this.CACHE_PREFIX,
        operation: 'update',
      });

      return updatedClub;
    } catch (error) {
      this.logger.error('Failed to add student to club', error);
      throw error;
    }
  }

  /**
   * Retrieves the student club information based on the provided student ID.
   * @param studentId - The ID of the student.
   * @returns A Promise that resolves to the student club information.
   */
  async getStudentClubByStudentId(studentId: string) {
    const cacheKey = this.cacheService.generateKey(
      ['student-clubs', studentId],
      this.CACHE_PREFIX,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved student clubs from cache');
      return cachedData;
    }

    const clubAdmin = alias(users, 'clubAdmin');
    const result = await this.drizzle.db
      .select({
        membership: {
          id: student_club_memberships.id,
          role: student_club_memberships.role,
          is_active: student_club_memberships.is_active,
        },
        club: {
          id: student_clubs.id,
          name: student_clubs.name,
          description: student_clubs.description,
          club_admin: sql`CASE WHEN ${clubAdmin.id} IS NULL THEN to_json(${student_clubs.club_admin}) ELSE json_build_object(
            'id', ${clubAdmin.id},
            'email', ${clubAdmin.email},
            'role', ${clubAdmin.role},
            'state', ${clubAdmin.state},
            'profile_pic_url', ${clubAdmin.profile_pic_url},
            'created_at', ${clubAdmin.created_at},
            'updated_at', ${clubAdmin.updated_at},
            'deleted', ${clubAdmin.deleted},
            'deleted_at', ${clubAdmin.deleted_at}
          ) END`,
          institution_id: student_clubs.institution_id,
          country_id: student_clubs.country_id,
          is_active: student_clubs.is_active,
          club_logo_url: student_clubs.club_logo_url,
          club_banner_url: student_clubs.club_banner_url,
          created_at: student_clubs.created_at,
          updated_at: student_clubs.updated_at,
        },
        institution: {
          id: institutions.id,
          name: institutions.name,
          location: institutions.location,
          address: institutions.address,
          city: institutions.city,
          state: institutions.state,
          domain: institutions.domain,
          country_id: institutions.country_id,
          disabled: institutions.disabled,
          created_at: institutions.created_at,
          updated_at: institutions.updated_at,
        },
      })
      .from(student_club_memberships)
      .where(
        and(
          eq(student_club_memberships.student_id, studentId),
          eq(student_club_memberships.is_active, true),
        ),
      )
      .leftJoin(
        student_clubs,
        eq(student_clubs.id, student_club_memberships.club_id),
      )
      .leftJoin(institutions, eq(institutions.id, student_clubs.institution_id))
      .leftJoin(clubAdmin, eq(clubAdmin.id, student_clubs.club_admin));

    await this.cacheService.set(cacheKey, result, CACHE_TTL.SEVEN_DAYS);

    return result;
  }

  /**
   * Gets club members by institution id
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param institutionId
   * @returns
   */
  async getClubMembersByInstitutionId(institutionId: string) {
    const cacheKey = this.cacheService.generateKey(
      ['institution-clubs', institutionId],
      this.CACHE_PREFIX,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved institution club members from cache');
      return cachedData;
    }

    const clubAdmin = alias(users, 'clubAdmin');
    const data = await this.drizzle.db
      .select({
        ...getTableColumns(student_clubs),
        club_admin: sql`CASE WHEN ${clubAdmin.id} IS NULL THEN to_json(${student_clubs.club_admin}) ELSE json_build_object(
          'id', ${clubAdmin.id},
          'email', ${clubAdmin.email},
          'role', ${clubAdmin.role},
          'state', ${clubAdmin.state},
          'profile_pic_url', ${clubAdmin.profile_pic_url},
          'created_at', ${clubAdmin.created_at},
          'updated_at', ${clubAdmin.updated_at},
          'deleted', ${clubAdmin.deleted},
          'deleted_at', ${clubAdmin.deleted_at}
        ) END`,
        country: {
          ...getTableColumns(countries),
        },
        institution: {
          ...getTableColumns(institutions),
        },
        created_by: {
          ...getTableColumns(users),
        },
        memberCount: this.drizzle.db.$count(
          student_club_memberships,
          and(
            eq(student_club_memberships.club_id, student_clubs.id),
            eq(student_club_memberships.is_active, true),
          ),
        ),
      })
      .from(student_clubs)
      .leftJoin(
        student_club_memberships,
        eq(student_club_memberships.club_id, student_clubs.id),
      )
      .leftJoin(institutions, eq(institutions.id, student_clubs.institution_id))
      .leftJoin(countries, eq(countries.id, student_clubs.country_id))
      .leftJoin(users, eq(users.id, student_clubs.created_by))
      .leftJoin(clubAdmin, eq(clubAdmin.id, student_clubs.club_admin))
      .where(eq(student_clubs.institution_id, institutionId))
      .groupBy(
        student_clubs.id,
        institutions.id,
        countries.id,
        users.id,
        clubAdmin.id,
      );

    const total = await this.drizzle.db.$count(
      student_clubs,
      eq(student_clubs.institution_id, institutionId),
    );

    const result = {
      data,
      total,
    };

    // Cache the results with a consistent TTL
    await this.cacheService.set(cacheKey, result, CACHE_TTL.TEN_MINUTES);

    return result;
  }

  /**
   * Adds the isMember flag to club data based on the user's membership status
   * @param clubsData The clubs data to add the isMember flag to
   * @param user The authenticated user
   * @returns The clubs data with the isMember flag added
   */
  async addIsMemberFlagToClubs(clubsData: any, user?: User) {
    if (!clubsData?.data) {
      return clubsData;
    }

    // If user is not authenticated or has no student profile, set isMember to false for all clubs
    if (!user?.student_profile?.id) {
      clubsData.data.forEach((club: any) => {
        club.isMember = false;
      });
      return clubsData;
    }

    // Get student clubs
    const studentClubs = await this.getStudentClubByStudentId(
      user.student_profile.id,
    );

    // Create a map of club IDs the student is a member of
    const memberClubIds = new Set();
    if (studentClubs && Array.isArray(studentClubs)) {
      studentClubs.forEach((item) => {
        if (item.club && item.membership && item.membership.is_active) {
          memberClubIds.add(item.club.id);
        }
      });
    }

    // Add isMember flag to each club
    clubsData.data.forEach((club: any) => {
      club.isMember = memberClubIds.has(club.id);
    });

    return clubsData;
  }

  /**
   * Updates the membership role of a student in a club.
   *
   * @param clubId - The ID of the club.
   * @param studentId - The ID of the student.
   * @param role - The new role of the student in the club.
   * @returns A promise that resolves to the result of the update operation.
   * @throws BadRequestException if the club is not found or the student is not a member of the club.
   */
  async updateStudentMembershipRole(
    clubId: string,
    studentId: string,
    role: StudentClubRole,
  ) {
    const cacheKey = this.cacheService.generateKey(
      ['membership', clubId, studentId],
      this.CACHE_PREFIX,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug('Retrieved membership from cache');
      return cachedData;
    }

    const [club] = await this.drizzle.db
      .select({
        firstName: student_profiles.first_name,
        lastName: student_profiles.last_name,
        clubName: student_clubs.name,
        email: users.email,
        userId: users.id,
      })
      .from(student_club_memberships)
      .where(
        and(eq(student_clubs.id, clubId), eq(student_profiles.id, studentId)),
      )
      .leftJoin(
        student_clubs,
        eq(student_clubs.id, student_club_memberships.club_id),
      )
      .leftJoin(
        student_profiles,
        eq(student_profiles.id, student_club_memberships.student_id),
      )
      .leftJoin(users, eq(users.id, student_profiles.user_id));

    if (!club)
      throw new BadRequestException(
        'Club not found or Student is not a member of the club',
      );

    try {
      const result = await this.drizzle.db.transaction(async (tx) => {
        const updatedMembership = await tx
          .update(student_club_memberships)
          .set({ role })
          .where(
            and(
              eq(student_club_memberships.club_id, clubId),
              eq(student_club_memberships.student_id, studentId),
            ),
          )
          .returning();

        if (role == student_club_roles.ADMIN) {
          await tx
            .update(student_clubs)
            .set({ club_admin: club.userId })
            .where(eq(student_clubs.id, clubId));

          await tx
            .update(users)
            .set({ role: user_roles.STUDENT_ADMIN })
            .where(club.userId ? eq(users.id, club.userId) : undefined);

          await this.emailService.sendStudentAdminPromotion({
            email: club.email ?? '',
            clubName: club.clubName ?? '',
            studentName: `${club.firstName} ${club.lastName}`,
          });
        }

        return updatedMembership;
      });

      await this.cacheService.set(cacheKey, result, CACHE_TTL.ONE_DAY);

      // Comprehensive cache invalidation for membership change
      await invalidateAllClubCaches(this.cacheService, {
        clubId,
        studentId,
        prefix: this.CACHE_PREFIX,
        operation: 'membership',
      });

      const updatedClub = await this.getClubById(clubId);
      if (updatedClub) {
        const clubCacheKey = generateClubKey(
          this.cacheService,
          clubId,
          this.CACHE_PREFIX,
        );
        await this.cacheService.set(
          clubCacheKey,
          updatedClub,
          CACHE_TTL.SEVEN_DAYS,
        );
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to update student membership role', error);
      throw error;
    }
  }

  /**
   * Activates club
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param clubId
   * @param active
   * @returns
   */
  async activateClub(clubId: string, active: boolean) {
    try {
      const result = await this.drizzle.db
        .update(student_clubs)
        .set({ is_active: active })
        .where(eq(student_clubs.id, clubId))
        .returning();

      if (result.length > 0) {
        // Comprehensive cache invalidation for club update
        await invalidateAllClubCaches(this.cacheService, {
          clubId,
          institutionId: result[0]?.institution_id,
          prefix: this.CACHE_PREFIX,
          operation: 'update',
        });
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to ${active ? 'activate' : 'deactivate'} club`,
        error,
      );
      throw error;
    }
  }

  /**
   * Gets inactive clubs
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @returns
   */
  async getInactiveClubs() {
    const result = await this.drizzle.db
      .select()
      .from(student_clubs)
      .where(eq(student_clubs.is_active, false));
    return result;
  }

  /**
   * Gets active clubs
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param {
   *     page,
   *     limit,
   *     sort,
   *     order,
   *   }
   * @returns
   */
  async getActiveClubs({
    page,
    limit,
    sort,
    order,
  }: PaginationParams & {
    sort: keyof Club;
  }) {
    const cacheKey = this.cacheService.generateKey(
      ['active', page.toString(), limit.toString(), sort, order],
      this.CACHE_PREFIX,
    );

    this.logger.debug(
      `Getting active clubs with order=${order}, sort=${sort}, cacheKey=${cacheKey}`,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug(
        `Retrieved active clubs from cache with order=${order}, sort=${sort}`,
      );
      return cachedData;
    }

    // Ensure sort is a valid column in student_clubs
    const validSortColumns = Object.keys(student_clubs);
    const sortColumn = validSortColumns.includes(sort) ? sort : 'id';

    this.logger.debug(`Sorting by ${sortColumn} in ${order} order`);

    const clubAdmin = alias(users, 'clubAdmin');
    const data = await this.drizzle.db
      .select({
        ...getTableColumns(student_clubs),
        club_admin: sql`CASE WHEN ${clubAdmin.id} IS NULL THEN to_json(${student_clubs.club_admin}) ELSE json_build_object(
          'id', ${clubAdmin.id},
          'email', ${clubAdmin.email},
          'role', ${clubAdmin.role},
          'state', ${clubAdmin.state},
          'profile_pic_url', ${clubAdmin.profile_pic_url},
          'created_at', ${clubAdmin.created_at},
          'updated_at', ${clubAdmin.updated_at},
          'deleted', ${clubAdmin.deleted},
          'deleted_at', ${clubAdmin.deleted_at}
        ) END`,
        country: {
          ...getTableColumns(countries),
        },
        institution: {
          ...getTableColumns(institutions),
        },
        created_by: {
          ...getTableColumns(users),
        },
        memberCount: this.drizzle.db.$count(
          student_club_memberships,
          and(
            eq(student_club_memberships.club_id, student_clubs.id),
            eq(student_club_memberships.is_active, true),
          ),
        ),
      })
      .from(student_clubs)
      .leftJoin(institutions, eq(institutions.id, student_clubs.institution_id))
      .leftJoin(countries, eq(countries.id, student_clubs.country_id))
      .leftJoin(users, eq(users.id, student_clubs.created_by))
      .leftJoin(clubAdmin, eq(clubAdmin.id, student_clubs.club_admin))
      .leftJoin(
        student_club_memberships,
        eq(student_club_memberships.club_id, student_clubs.id),
      )
      .where(eq(student_clubs.is_active, true))
      .groupBy(
        student_clubs.id,
        institutions.id,
        countries.id,
        users.id,
        clubAdmin.id,
      )
      .orderBy(
        order === 'asc'
          ? asc(student_clubs[sortColumn as keyof typeof student_clubs] as any)
          : desc(
              student_clubs[sortColumn as keyof typeof student_clubs] as any,
            ),
      )
      .limit(limit)
      .offset((page - 1) * limit);

    const total = await this.drizzle.db.$count(
      student_clubs,
      eq(student_clubs.is_active, true),
    );

    const result = {
      data,
      total,
    };

    // Cache the results with a consistent TTL
    await this.cacheService.set(cacheKey, result, CACHE_TTL.TEN_MINUTES);

    this.logger.debug(
      `Cached active clubs with order=${order}, sort=${sortColumn}, results=${data.length}`,
    );

    return result;
  }
  /**
   * Removes student from club
   * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
   * @param clubId
   * @param student
   * @returns
   */
  async removeStudentFromClub(clubId: string, studentId: string) {
    // Check if student profile exists
    if (!studentId) {
      this.logger.warn(
        `Cannot remove student from club: Student profile is missing or invalid`,
      );
      throw new BadRequestException('Student profile not found');
    }

    const [membership] = await this.drizzle.db
      .select({
        clubId: student_club_memberships.club_id,
        student: {
          id: student_club_memberships.student_id,
          institution_id: student_profiles.institution_id,
        },
        created_by: student_clubs.created_by,
        role: student_club_memberships.role,
      })
      .from(student_club_memberships)
      .where(
        and(
          eq(student_club_memberships.club_id, clubId),
          eq(student_club_memberships.student_id, studentId),
        ),
      )
      .leftJoin(
        student_clubs,
        eq(student_clubs.id, student_club_memberships.club_id),
      )
      .leftJoin(student_profiles, eq(student_profiles.id, studentId));

    if (!membership) {
      throw new BadRequestException('Student is not a member of the club');
    }
    const result = await this.drizzle.db
      .update(student_club_memberships)
      .set({ is_active: false })
      .where(
        and(
          eq(student_club_memberships.club_id, clubId),
          eq(student_club_memberships.student_id, studentId),
        ),
      )
      .returning();

    if (membership.role === student_club_roles.ADMIN) {
      await this.drizzle.db
        .update(student_clubs)
        .set({ club_admin: membership.created_by })
        .where(eq(student_clubs.id, clubId));
    }

    // Comprehensive cache invalidation for membership change (leaving club)
    await invalidateAllClubCaches(this.cacheService, {
      clubId,
      studentId,
      institutionId: membership.student.institution_id as string,
      prefix: this.CACHE_PREFIX,
      operation: 'membership',
    });

    return result;
  }

  private async getClubMembersFromDB(
    clubId: string,
    {
      search,
      sort,
      order,
      limit,
      page,
      role,
      all,
      status,
    }: clubMembersQueryParamsDto & {
      sort: keyof StudentProfile;
    },
  ) {
    // Don't use cache for filtered/paginated/search requests
    if (search || !all || status || role) {
      this.logger.debug(
        `Getting club members from DB with order=${order}, sort=${sort}`,
      );
      return this.getClubMembersFromDatabase(clubId, {
        search,
        sort,
        order,
        limit,
        page,
        role,
        status,
      });
    }

    // Include order and sort in the cache key
    const cacheKey = this.cacheService.generateKey(
      ['members', clubId, order, sort, page.toString(), limit.toString()],
      this.CACHE_PREFIX,
    );

    this.logger.debug(
      `Getting club members with order=${order}, sort=${sort}, cacheKey=${cacheKey}`,
    );

    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      this.logger.debug(
        `Retrieved club members from cache with order=${order}, sort=${sort}`,
      );
      return cachedData;
    }

    const members = await this.getClubMembersFromDatabase(clubId, {
      search,
      sort,
      order,
      limit,
      page,
      role,
      status,
    });

    // Cache the results with a consistent TTL
    await this.cacheService.set(cacheKey, members, CACHE_TTL.TEN_MINUTES);

    this.logger.debug(
      `Cached club members with order=${order}, sort=${sort}, results=${members.data.length}`,
    );

    return members;
  }

  private async getClubMembersFromDatabase(
    clubId: string,
    {
      search,
      sort,
      order,
      limit,
      page,
      role,
      status,
    }: Omit<clubMembersQueryParamsDto, 'all'> & {
      sort: keyof StudentProfile;
    },
  ) {
    const filters = [];

    // Ensure sort is a valid column in student_profiles
    const validSortColumns = Object.keys(student_profiles);
    const sortColumn = validSortColumns.includes(sort) ? sort : 'id';

    this.logger.debug(
      `Sorting club members by ${sortColumn} in ${order} order`,
    );

    if (status) {
      filters.push(eq(student_club_memberships.is_active, status === 'active'));
    }
    if (role) {
      filters.push(eq(student_club_memberships.role, role));
    }
    if (search) {
      filters.push(
        or(
          ilike(student_profiles.first_name, `%${search}%`),
          ilike(student_profiles.last_name, `%${search}%`),
        ),
      );
    }

    const data = await this.drizzle.db
      .select({
        ...getTableColumns(student_profiles),
        user: {
          ...getTableColumns(users),
        },
        membership: {
          ...getTableColumns(student_club_memberships),
        },
      })
      .from(student_club_memberships)
      .where(and(eq(student_club_memberships.club_id, clubId), ...filters))
      .leftJoin(
        student_profiles,
        eq(student_profiles.id, student_club_memberships.student_id),
      )
      .leftJoin(users, eq(users.id, student_profiles.user_id))
      .orderBy(
        order === 'asc'
          ? asc(
              student_profiles[
                sortColumn as keyof typeof student_profiles
              ] as any,
            )
          : desc(
              student_profiles[
                sortColumn as keyof typeof student_profiles
              ] as any,
            ),
      )
      .limit(limit)
      .offset((page - 1) * limit);

    const total = await this.drizzle.db.$count(
      student_club_memberships,
      and(eq(student_club_memberships.club_id, clubId), ...filters),
    );

    return {
      data,
      total,
    };
  }
}
