import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Put,
  Post,
  UseGuards,
  Query,
  UseInterceptors,
  UploadedFiles,
  MaxFileSizeValidator,
  FileTypeValidator,
  BadRequestException,
  Req,
} from '@nestjs/common';
import { ClubService } from './club.service';
import { ZodSerializerDto } from 'nestjs-zod';
import {
  UpdateStudentClubMembershipDto,
  clubQueryParamsDto,
  ClubDto,
  addStudentToClubDto,
  clubMembersQueryParamsDto,
} from './club.dto';
import {
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiBearerAuth,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { AppClients } from '@app/shared/constants/auth.constants';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { User } from '@/guards/user.decorator';
import {
  Club,
  StudentProfile,
  type User as UserDecoratorType,
} from '@/db/schema';
import type { AuthenticatedStudent } from '@/@types/auth.types';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ParseFilesPipe } from '@/validators/custom-max-file-size.validator';
import { Two_MB } from '@app/shared/constants/mcq.constants';
import { UploadService } from '@/upload/upload.service';
import { StudentProfileService } from '@/student_profile/student_profile.service';
import type { Request } from 'express';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { ClubRoutes } from '@app/shared/constants/club.constants';

@Controller({ version: '1', path: 'club' })
@ApiTags('Club')
export class ClubController {
  constructor(
    private readonly student_club: ClubService,
    private readonly uploadService: UploadService,
    private readonly studentService: StudentProfileService,
  ) {}
  private readonly logger = new Logger(ClubController.name);

  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club', action: 'create', possession: 'any' })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Create a new student club' })
  @ApiBody({ type: ClubDto, description: 'Club data to create' })
  @ApiOkResponse({
    description: 'Club has been successfully created.',
    type: ClubDto,
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async addStudentClub(
    @User() user: UserDecoratorType,
    @Body() clubInput: ClubDto,
  ) {
    try {
      return await this.student_club.addStudentClub(clubInput, user.id);
    } catch (error: any) {
      this.logger.error('Failed to add student club:', error.message);
      throw error;
    }
  }

  @Put(ClubRoutes.UPDATE_CLUB)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club', action: 'update', possession: 'any' })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Update an existing student club' })
  @ApiParam({ name: 'id', description: 'Club ID', type: 'string' })
  @ApiBody({ type: ClubDto, description: 'Updated club data' })
  @ApiCreatedResponse({
    description: 'The record has been successfully updated.',
    type: ClubDto,
  })
  @ApiBearerAuth()
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'club_logo_image', maxCount: 1 },
      { name: 'club_banner_image', maxCount: 1 },
    ]),
  )
  @CLIENT_TYPE(AppClients.WEB)
  async updateStudentClub(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() clubUpdateInput: ClubDto,
    @UploadedFiles(
      new ParseFilesPipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: Two_MB }),
          new FileTypeValidator({ fileType: /(jpg|jpeg|png|gif)$/ }),
        ],
      }),
    )
    files: {
      club_logo_image: Express.Multer.File[];
      club_banner_image: Express.Multer.File[];
    },
  ) {
    try {
      if (files.club_logo_image && files.club_logo_image[0]) {
        clubUpdateInput.club_logo_url = (
          await this.uploadService.uploadFileToS3(files.club_logo_image[0])
        ).imageUrl;
      }
      if (files.club_banner_image && files.club_banner_image[0]) {
        clubUpdateInput.club_banner_url = (
          await this.uploadService.uploadFileToS3(files.club_banner_image[0])
        ).imageUrl;
      }
      return await this.student_club.updateStudentClub(id, clubUpdateInput);
    } catch (error: any) {
      this.logger.error('Error updating student club:', error.message);
      throw error;
    }
  }

  @Delete(ClubRoutes.DELETE_CLUB)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club', action: 'delete', possession: 'any' })
  @ApiOperation({ summary: 'Delete a student club' })
  @ApiParam({ name: 'id', description: 'Club ID to delete', type: 'string' })
  @ApiNoContentResponse({
    description: 'The club has been successfully deleted.',
  })
  @ApiNotFoundResponse({ description: 'Club not found.' })
  @CLIENT_TYPE(AppClients.WEB)
  async removeStudentClub(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<void> {
    try {
      // Call the service method to remove the student club
      await this.student_club.removeStudentClub(id);
    } catch (error: any) {
      this.logger.error('Error removing student club:', error.message);
      throw error;
    }
  }

  @Get(ClubRoutes.GET_ACTIVE_CLUBS)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club', action: 'read', possession: 'any' })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Get all active clubs' })
  @ApiQuery({ type: clubQueryParamsDto })
  @ApiOkResponse({
    description: 'Successfully fetched active clubs.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getActiveClubs(
    @Query() query: clubQueryParamsDto,
    @User() user?: UserDecoratorType,
  ) {
    try {
      const result = await this.student_club.getActiveClubs(
        query as clubQueryParamsDto & {
          sort: keyof Club;
        },
      );

      // Add isMember flag to clubs
      return await this.student_club.addIsMemberFlagToClubs(result, user);
    } catch (error: any) {
      this.logger.error('Error fetching active clubs:', error.message);
      throw error;
    }
  }

  @Get(ClubRoutes.GET_INACTIVE_CLUBS)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club', action: 'update', possession: 'any' })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Get all inactive clubs' })
  @ApiOkResponse({
    description: 'Successfully fetched inactive clubs.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.WEB)
  async deactivateClub() {
    try {
      return await this.student_club.getInactiveClubs();
    } catch (error: any) {
      this.logger.error('Error Getting inactive club:', error.message);
      throw error;
    }
  }

  @Get(ClubRoutes.GET_CLUB_BY_ID)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Get a specific club by ID' })
  @ApiParam({ name: 'id', description: 'Club ID', type: 'string' })
  @ApiOkResponse({
    description: 'Successfully fetched the student club.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getStudentClub(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User() user?: UserDecoratorType,
  ) {
    try {
      // Call the service method to get the student club by ID
      const club = await this.student_club.getClubById(id);

      // For single club, we need to handle the isMember flag differently
      if (club) {
        if (user?.student_profile?.id) {
          // Get student clubs
          const studentClubs =
            await this.student_club.getStudentClubByStudentId(
              user.student_profile.id,
            );

          // Check if student is a member of this club
          const isMember =
            studentClubs &&
            Array.isArray(studentClubs) &&
            studentClubs.some(
              (item) =>
                item.club &&
                item.club.id === id &&
                item.membership &&
                item.membership.is_active,
            );

          // Add isMember flag to club
          club.isMember = isMember;
        } else {
          // Set isMember to false if user is not logged in
          club.isMember = false;
        }
      }

      return club;
    } catch (error: any) {
      // Handle and log the error
      this.logger.error('Error fetching student club by ID:', error);
      throw error;
    }
  }

  @Get()
  @ApiBearerAuth()
  @ZodSerializerDto(ClubDto)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club', action: 'read', possession: 'any' })
  @ApiOperation({ summary: 'Get all clubs with pagination' })
  @ApiQuery({ type: clubQueryParamsDto })
  @ApiOkResponse({
    description: 'Successfully fetched all clubs.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getAllClubs(
    @User() user: UserDecoratorType,
    @Query() query: clubQueryParamsDto,
    @Req() req: Request,
  ) {
    try {
      const result = await this.student_club.getAllClubs(
        user,
        query as clubQueryParamsDto & {
          sort: keyof Club;
          country_id: number;
        },
        req,
      );

      // Add isMember flag to clubs
      return await this.student_club.addIsMemberFlagToClubs(result, user);
    } catch (error: any) {
      this.logger.error('Error fetching all clubs:', error);
      throw error;
    }
  }
  //Get clubs members by Club ID
  @Get(ClubRoutes.GET_CLUB_MEMBERS)
  @ApiBearerAuth()
  @ZodSerializerDto(ClubDto)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club', action: 'read', possession: 'any' })
  @ApiOperation({ summary: 'Get all members of a specific club' })
  @ApiParam({ name: 'id', description: 'Club ID', type: 'string' })
  @ApiQuery({ type: clubMembersQueryParamsDto })
  @ApiOkResponse({
    description: 'Successfully fetched all club members.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getClubMembers(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Query() query: clubMembersQueryParamsDto,
  ) {
    try {
      return await this.student_club.getClubMembersByClubId(
        id,
        query as clubMembersQueryParamsDto & {
          sort: keyof StudentProfile;
        },
      );
    } catch (error: any) {
      this.logger.error('Error fetching club members:', error);
      throw error;
    }
  }
  //Add a student to a club using Student Profile ID
  @Post(ClubRoutes.ADD_STUDENT_TO_CLUB)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'club_membership',
    action: 'create',
    possession: 'any',
  })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Add a student to a club' })
  @ApiParam({ name: 'id', description: 'Club ID', type: 'string' })
  @ApiBody({ type: addStudentToClubDto })
  @ApiOkResponse({
    description: 'Successfully added a student to a club.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async addStudentToClub(
    @Param('id', new CustomParseUUIDPipe()) clubId: string,
    @Body() data: addStudentToClubDto,
  ) {
    try {
      await this.student_club.addStudentToClub(clubId, data.student_id);
      return {
        message: 'Student added to club successfully',
      };
    } catch (error: any) {
      this.logger.error('Error adding student to club:', error.message);
      throw error;
    }
  }

  //Disable and Enable Club
  @Put(ClubRoutes.UPDATE_CLUB_STATUS)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club', action: 'update', possession: 'any' })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Activate or deactivate a club' })
  @ApiParam({ name: 'id', description: 'Club ID', type: 'string' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        is_active: {
          type: 'boolean',
          description: 'Club active status',
        },
      },
    },
  })
  @ApiOkResponse({
    description: 'Club status updated successfully.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.WEB)
  async disableClub(
    @Param('id', new CustomParseUUIDPipe()) clubId: string,
    @Body('is_active') disable: boolean,
  ) {
    try {
      return await this.student_club.setClubStatus(clubId, disable);
    } catch (error: any) {
      this.logger.error('Error disabling club:', error.message);
      throw error;
    }
  }

  //Get student club by student ID
  @Get(ClubRoutes.GET_CLUB_BY_STUDENT_ID)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'club_membership', action: 'read', possession: 'any' })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Get all clubs for a specific student' })
  @ApiParam({ name: 'id', description: 'Student ID', type: 'string' })
  @ApiOkResponse({
    description: 'Successfully fetched the student club.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getStudentClubByStudentId(
    @Param('id', CustomParseUUIDPipe) id: string,
  ) {
    try {
      return await this.student_club.getStudentClubByStudentId(id);
    } catch (error: any) {
      this.logger.error(
        'Error fetching student club by Student ID:',
        error.message,
      );
      throw error;
    }
  }

  @Get(ClubRoutes.GET_CLUB_BY_INSTITUTION_ID)
  @ApiBearerAuth()
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Get all clubs for a specific institution' })
  @ApiParam({ name: 'id', description: 'Institution ID', type: 'string' })
  @ApiOkResponse({
    description: 'Successfully fetched the student club.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getClubByInstitutionId(
    @Param('id', CustomParseUUIDPipe) id: string,
    @User() user?: UserDecoratorType,
  ) {
    try {
      // Get clubs from service
      const result = await this.student_club.getClubMembersByInstitutionId(id);

      // Add isMember flag to clubs
      return await this.student_club.addIsMemberFlagToClubs(result, user);
    } catch (error: any) {
      this.logger.error('Error fetching student club by ID:', error.message);
      throw error;
    }
  }

  //Update student membership role
  @Put(ClubRoutes.UPDATE_STUDENT_MEMBERSHIP_ROLE)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'club_membership',
    action: 'update',
    possession: 'any',
  })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: "Update a student's role in a club" })
  @ApiParam({ name: 'clubId', description: 'Club ID', type: 'string' })
  @ApiParam({ name: 'studentId', description: 'Student ID', type: 'string' })
  @ApiBody({ type: UpdateStudentClubMembershipDto })
  @ApiOkResponse({
    description: 'Student membership role has been successfully updated.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.WEB)
  async updateStudentMembershipRole(
    @Param('clubId', CustomParseUUIDPipe) clubId: string,
    @Param('studentId', CustomParseUUIDPipe) studentId: string,
    @Body() data: UpdateStudentClubMembershipDto,
  ) {
    try {
      return await this.student_club.updateStudentMembershipRole(
        clubId,
        studentId,
        data.role,
      );
    } catch (error: any) {
      this.logger.error(
        'Error updating student membership role:',
        error.message,
      );
      throw error;
    }
  }

  //Update student membership role
  @Delete(ClubRoutes.REMOVE_STUDENT_FROM_CLUB)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'club_membership',
    action: 'delete',
    possession: 'any',
  })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Remove a student from a club' })
  @ApiParam({ name: 'clubId', description: 'Club ID', type: 'string' })
  @ApiParam({ name: 'studentId', description: 'Student ID', type: 'string' })
  @ApiOkResponse({
    description: 'Student removed from the club successfully.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.WEB)
  async removeStudentFromClub(
    @Param('clubId', CustomParseUUIDPipe) clubId: string,
    @Param('studentId', CustomParseUUIDPipe) studentId: string,
  ) {
    try {
      return await this.student_club.removeStudentFromClub(clubId, studentId);
    } catch (error: any) {
      this.logger.error(
        'Error updating student membership role:',
        error.message,
      );
      throw error;
    }
  }

  @Put(ClubRoutes.LEAVE_CLUB)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'club_membership',
    action: 'update',
    possession: 'own',
  })
  @ZodSerializerDto(ClubDto)
  @ApiOperation({ summary: 'Leave a club (for authenticated student)' })
  @ApiParam({ name: 'clubId', description: 'Club ID to leave', type: 'string' })
  @ApiOkResponse({
    description: 'Student has left the club.',
    type: ClubDto,
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async leaveClub(
    @Param('clubId', new CustomParseUUIDPipe()) clubId: string,
    @User() user: AuthenticatedStudent,
  ) {
    try {
      if (!user.student_profile) {
        throw new BadRequestException('Student profile not found');
      }

      return await this.student_club.removeStudentFromClub(
        clubId,
        user.student_profile.id,
      );
    } catch (error: any) {
      this.logger.error('Error leaving the club:', error.message);
      throw error;
    }
  }
}
