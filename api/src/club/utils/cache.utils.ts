import { Logger } from '@nestjs/common';
import { CacheService } from '@app/shared/redis/cache.service';
import { user_roles } from '@/db/schema';

const logger = new Logger('ClubCache');

/**
 * Generates a cache key for a specific club
 */
export const generateClubKey = (
  cacheService: CacheService,
  id: string,
  prefix: string,
): string => {
  return cacheService.generateResourceKey(id, prefix);
};

/**
 * Invalidates the general club cache
 */
export const invalidateClubCache = async (
  cacheService: CacheService,
  prefix: string,
): Promise<void> => {
  try {
    await cacheService.del(`${prefix}:all`);
    logger.debug('Club list cache invalidated');
  } catch (error) {
    logger.warn('Failed to invalidate club cache', error);
  }
};

/**
 * Invalidates all club-related caches including role-specific and status-specific caches
 *
 * This function is used to invalidate general club caches when a club or its membership
 * is modified. It targets role-specific, status-specific, and club-specific caches.
 */
export const invalidateClubCaches = async (
  cacheService: CacheService,
  id: string,
  prefix: string,
): Promise<void> => {
  try {
    const keysToInvalidate = [
      // Role-specific list caches
      `all:${user_roles.ADMIN}`,
      `all:${user_roles.STUDENT}`,
      `all:${user_roles.STUDENT_ADMIN}`,
      `all:${user_roles.SUPER_ADMIN}`,
      // Status-specific caches
      'status:active',
      'status:inactive',
      // Club-specific cache
      id,
      // General list caches
      'all',
      'active',
    ];

    await cacheService.invalidateMany(keysToInvalidate, prefix);
    logger.debug(`Invalidated club caches for club ${id}`);
  } catch (error) {
    logger.warn(`Failed to invalidate club caches for club ${id}`, error);
  }
};

/**
 * Invalidates student-specific club membership caches
 *
 * This function is used to invalidate caches related to a student's club memberships
 * when they join or leave a club.
 *
 * @param cacheService - The cache service instance
 * @param studentId - The ID of the student
 * @param clubId - The ID of the club (optional)
 * @param prefix - The cache prefix
 */
export const invalidateStudentClubCaches = async (
  cacheService: CacheService,
  studentId: string,
  clubId: string | undefined,
  prefix: string,
): Promise<void> => {
  try {
    const keysToInvalidate = [
      // Student's club memberships
      `student-clubs:${studentId}`,
      // Student's specific club membership if clubId is provided
      ...(clubId ? [`membership:${clubId}:${studentId}`] : []),
    ];

    await cacheService.invalidateMany(keysToInvalidate, prefix);
    logger.debug(
      `Invalidated student club caches for student ${studentId}${
        clubId ? ` and club ${clubId}` : ''
      }`,
    );
  } catch (error) {
    logger.warn(
      `Failed to invalidate student club caches for student ${studentId}`,
      error,
    );
  }
};

/**
 * Invalidates institution-specific club caches
 *
 * @param cacheService - The cache service instance
 * @param institutionId - The ID of the institution
 * @param prefix - The cache prefix
 */
export const invalidateInstitutionClubCaches = async (
  cacheService: CacheService,
  institutionId: string,
  prefix: string,
): Promise<void> => {
  try {
    const keysToInvalidate = [
      // Institution's clubs
      `institution-clubs:${institutionId}`,
    ];

    await cacheService.invalidateMany(keysToInvalidate, prefix);
    logger.debug(
      `Invalidated institution club caches for institution ${institutionId}`,
    );
  } catch (error) {
    logger.warn(
      `Failed to invalidate institution club caches for institution ${institutionId}`,
      error,
    );
  }
};

/**
 * Comprehensive cache invalidation for club operations
 *
 * This function provides a unified approach to invalidate all relevant caches
 * after a club operation (create, update, delete, membership change).
 *
 * @param cacheService - The cache service instance
 * @param options - Configuration options for cache invalidation
 * @param options.clubId - The ID of the club being modified
 * @param options.studentId - The ID of the student (for membership operations)
 * @param options.institutionId - The ID of the institution
 * @param options.prefix - The cache prefix
 * @param options.operation - The type of operation (create, update, delete, membership)
 */
export interface ClubCacheInvalidationOptions {
  clubId: string;
  studentId?: string;
  institutionId?: string;
  prefix: string;
  operation: 'create' | 'update' | 'delete' | 'membership';
}

/**
 * Targeted cache invalidation for club creation
 *
 * This function provides a more efficient approach to invalidate only necessary
 * caches after a new club is created, without affecting existing club data.
 *
 * @param cacheService - The cache service instance
 * @param options - Configuration options for cache invalidation
 */
export const invalidateClubCreationCaches = async (
  cacheService: CacheService,
  options: {
    clubId: string;
    institutionId?: string;
    prefix: string;
  },
): Promise<void> => {
  const { clubId, institutionId, prefix } = options;

  try {
    const invalidationPromises: Promise<any>[] = [];

    // Only invalidate institution-specific caches if institutionId is provided
    if (institutionId) {
      invalidationPromises.push(
        cacheService.del(
          cacheService.generateKey(
            ['institution-clubs', institutionId],
            prefix,
          ),
        ),
      );
    }

    // Invalidate role-specific list caches that would include the new club
    // We don't need to invalidate individual club caches since this is a new club
    invalidationPromises.push(
      cacheService.del(cacheService.generateKey(['all'], prefix)),
      cacheService.del(cacheService.generateKey(['active'], prefix)),
    );

    // Execute all invalidation operations in parallel
    await Promise.all(invalidationPromises);

    logger.debug(
      `Targeted cache invalidation completed for new club ${clubId}`,
    );
  } catch (error) {
    logger.warn(
      `Failed to perform targeted cache invalidation for new club ${clubId}`,
      error,
    );
    // Don't rethrow the error to prevent blocking the main operation
  }
};

export const invalidateAllClubCaches = async (
  cacheService: CacheService,
  options: ClubCacheInvalidationOptions,
): Promise<void> => {
  const { clubId, studentId, institutionId, prefix, operation } = options;

  try {
    const invalidationPromises: Promise<any>[] = [
      // Always invalidate club-specific cache
      cacheService.del(cacheService.generateKey(['club', clubId], prefix)),

      // Always invalidate club members list cache
      cacheService.del(cacheService.generateKey(['members', clubId], prefix)),

      // Always invalidate general club caches
      invalidateClubCaches(cacheService, clubId, prefix),
    ];

    // For membership operations, invalidate student-specific caches
    if (operation === 'membership' && studentId) {
      invalidationPromises.push(
        invalidateStudentClubCaches(cacheService, studentId, clubId, prefix),
      );
    }

    // For all operations, invalidate institution-specific caches if institutionId is provided
    if (institutionId) {
      invalidationPromises.push(
        invalidateInstitutionClubCaches(cacheService, institutionId, prefix),
      );
    }

    // Execute all invalidation operations in parallel
    await Promise.all(invalidationPromises);

    logger.debug(
      `Comprehensive cache invalidation completed for club ${clubId} (operation: ${operation})`,
    );
  } catch (error) {
    logger.warn(
      `Failed to perform comprehensive cache invalidation for club ${clubId}`,
      error,
    );
    // Don't rethrow the error to prevent blocking the main operation
  }
};
