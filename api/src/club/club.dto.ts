import { querySchema } from '@/common/dto/query-params.dto';
import { studentProfileKeys } from '@/db/schema';
import {
  student_clubs,
  insertMembershipSchema,
  student_club_roles,
  clubRoles,
} from '@/db/schema/clubs';
import { studentProfileQueryParamsSchema } from '@/student_profile/student_profile.dto';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const baseInsertClubSchema = createInsertSchema(student_clubs);
const insertClubSchemaWithOriginalNaming = baseInsertClubSchema.extend({
  name: z
    .string()
    .min(3)
    .max(255)
    .transform((val) => val.trim()),
  description: z
    .string()
    .min(1)
    .max(255)
    .transform((val) => val.trim()),
  club_admin: z.string().uuid('Invalid club admin ID').optional(),
  institution_id: z.coerce.string().uuid('Invalid institution ID'),
  country_id: z.coerce.string().uuid('Invalid country ID'),
  is_active: z.coerce.boolean().optional(),
  club_logo_url: z
    .string()
    .max(255)
    .transform((val) => val?.trim())
    .optional(),
  club_banner_url: z
    .string()
    .max(255)
    .transform((val) => val?.trim())
    .optional(),
});

export const selectClubSchema = createSelectSchema(student_clubs);

export const updateStudentClubMembershipSchema = z.object({
  role: z.nativeEnum(student_club_roles),
});

export const clubKeys = Object.keys(student_clubs) as [string, ...string[]];
export const clubQueryParamsSchema = querySchema.extend({
  sort: z.enum(clubKeys).optional().default('id'),
  country_id: z.string().uuid('Invalid country ID').optional(),
});

const clubMembersQueryParamsSchema = studentProfileQueryParamsSchema.extend({
  sort: z.enum(studentProfileKeys).default('id'),
  role: z.enum(clubRoles).optional(),
  status: z.enum(['active', 'inactive']).optional(),
});

export type ClubParams = z.infer<typeof insertClubSchemaWithOriginalNaming>;
export type ClubMembershipParams = z.infer<typeof insertMembershipSchema>;

export class ClubDto extends createZodDto(insertClubSchemaWithOriginalNaming) {}
export class ClubMembershipDto extends createZodDto(insertMembershipSchema) {}
export class UpdateStudentClubMembershipDto extends createZodDto(
  updateStudentClubMembershipSchema,
) {}
export class clubQueryParamsDto extends createZodDto(clubQueryParamsSchema) {}
export class addStudentToClubDto extends createZodDto(
  z.object({
    student_id: z.string(),
  }),
) {}
export class clubMembersQueryParamsDto extends createZodDto(
  clubMembersQueryParamsSchema,
) {}
