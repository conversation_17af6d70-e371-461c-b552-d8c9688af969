import { ClubController } from './club.controller';
import { ClubService } from './club.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UploadService } from '@/upload/upload.service';
import { StudentProfileService } from '@/student_profile/student_profile.service';
import type { User } from '@/db/schema';
import { Request } from 'express';

// Mock the CLIENT_TYPE decorator
jest.mock('@/guards/request-validation.decorator', () => ({
  CLIENT_TYPE: () => jest.fn(),
  CLIENT_TYPE_KEY: 'CLIENT_TYPE_KEY',
}));

// Mock the Logger constructor
jest.mock('@nestjs/common', () => {
  const original = jest.requireActual('@nestjs/common');
  return {
    ...original,
    Logger: jest.fn().mockImplementation(() => ({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    })),
  };
});

describe('ClubController', () => {
  let controller: ClubController;
  let service: ClubService;
  let uploadService: UploadService;

  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    role: 'admin',
    state: 'active',
    profile_pic_url: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    deleted: false,
    deleted_at: null,
  };

  beforeEach(async () => {
    // Create mock services
    const mockClubService = {
      addStudentClub: jest.fn(),
      updateStudentClub: jest.fn(),
      removeStudentClub: jest.fn(),
      getClubById: jest.fn(),
      getAllClubs: jest.fn(),
      getActiveClubs: jest.fn(),
      getInactiveClubs: jest.fn(),
      getClubMembersByClubId: jest.fn(),
      addStudentToClub: jest.fn(),
      setClubStatus: jest.fn(),
      getStudentClubByStudentId: jest.fn(),
      getClubMembersByInstitutionId: jest.fn(),
      updateStudentMembershipRole: jest.fn(),
      removeStudentFromClub: jest.fn(),
      addIsMemberFlagToClubs: jest.fn(),
    };

    const mockUploadService = {
      uploadFileToS3: jest.fn(),
    };

    const mockStudentProfileService = {
      getStudentProfileById: jest.fn(),
    };

    // Create the controller directly
    controller = new ClubController(
      mockClubService as unknown as ClubService,
      mockUploadService as unknown as UploadService,
      mockStudentProfileService as unknown as StudentProfileService,
    );

    // Assign the mocks to the test variables
    service = mockClubService as unknown as ClubService;
    uploadService = mockUploadService as unknown as UploadService;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('addStudentClub', () => {
    it('should call validateClientType and addStudentClub successfully', async () => {
      const clubInput: any = {
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };
      const result = { id: '1', ...clubInput };

      (service.addStudentClub as jest.Mock).mockResolvedValue(result);

      const response = await controller.addStudentClub(mockUser, clubInput);

      expect(service.addStudentClub).toHaveBeenCalledWith(
        clubInput,
        mockUser.id,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const clubInput: any = { name: 'Test Club' };
      const error = new Error('Failed to add student club');

      (service.addStudentClub as jest.Mock).mockRejectedValue(error);

      try {
        await controller.addStudentClub(mockUser, clubInput);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });
  });

  describe('updateStudentClub', () => {
    it('should call validateClientType and updateStudentClub successfully', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = {
        name: 'Updated Club',
        description: 'Updated Description',
        institution_id: 'inst-123',
      };
      const result = { id, ...clubUpdateInput };
      const mockFiles = {
        club_logo_image: [],
        club_banner_image: [],
      };

      (service.updateStudentClub as jest.Mock).mockResolvedValue(result);

      const response = await controller.updateStudentClub(
        id,
        clubUpdateInput,
        mockFiles,
      );

      expect(service.updateStudentClub).toHaveBeenCalledWith(
        id,
        clubUpdateInput,
      );
      expect(response).toEqual(result);
    });

    it('should handle BadRequestException', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = { name: 'Updated Club' };
      const mockFiles = {
        club_logo_image: [],
        club_banner_image: [],
      };
      const error = new BadRequestException(`Club with ID ${id} not found`);

      (service.updateStudentClub as jest.Mock).mockRejectedValue(error);

      try {
        await controller.updateStudentClub(id, clubUpdateInput, mockFiles);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });

    it('should upload files if provided', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = { name: 'Updated Club' };
      const result = { id, ...clubUpdateInput };
      const mockLogoFile = {
        fieldname: 'club_logo_image',
        originalname: 'logo.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test'),
        size: 4,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      };
      const mockBannerFile = {
        fieldname: 'club_banner_image',
        originalname: 'banner.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test'),
        size: 4,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      };
      const mockFiles = {
        club_logo_image: [mockLogoFile as Express.Multer.File],
        club_banner_image: [mockBannerFile as Express.Multer.File],
      };

      (uploadService.uploadFileToS3 as jest.Mock).mockResolvedValueOnce({
        imageUrl: 'https://example.com/logo.jpg',
      });
      (uploadService.uploadFileToS3 as jest.Mock).mockResolvedValueOnce({
        imageUrl: 'https://example.com/banner.jpg',
      });
      (service.updateStudentClub as jest.Mock).mockResolvedValue(result);

      await controller.updateStudentClub(id, clubUpdateInput, mockFiles);

      expect(uploadService.uploadFileToS3).toHaveBeenCalledTimes(2);
      expect(uploadService.uploadFileToS3).toHaveBeenCalledWith(mockLogoFile);
      expect(uploadService.uploadFileToS3).toHaveBeenCalledWith(mockBannerFile);
      expect(clubUpdateInput.club_logo_url).toBe(
        'https://example.com/logo.jpg',
      );
      expect(clubUpdateInput.club_banner_url).toBe(
        'https://example.com/banner.jpg',
      );
    });
  });

  describe('removeStudentClub', () => {
    it('should call validateClientType and removeStudentClub successfully', async () => {
      const id = 'test-uuid';

      (service.removeStudentClub as jest.Mock).mockResolvedValue(undefined);

      await controller.removeStudentClub(id);

      expect(service.removeStudentClub).toHaveBeenCalledWith(id);
    });

    it('should handle BadRequestException', async () => {
      const id = 'test-uuid';
      const error = new BadRequestException(`Club with ID ${id} not found`);

      (service.removeStudentClub as jest.Mock).mockRejectedValue(error);

      try {
        await controller.removeStudentClub(id);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });
  });

  describe('getStudentClub', () => {
    it('should call validateClientType and getClubById successfully', async () => {
      const id = 'test-uuid';
      const result = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };

      (service.getClubById as jest.Mock).mockResolvedValue(result);

      const response = await controller.getStudentClub(id);

      expect(service.getClubById).toHaveBeenCalledWith(id);
      expect(response).toEqual(result);
    });

    it('should handle NotFoundException', async () => {
      const id = 'test-uuid';
      const error = new NotFoundException(`Club with ID ${id} not found`);

      (service.getClubById as jest.Mock).mockRejectedValue(error);

      try {
        await controller.getStudentClub(id);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });
  });

  describe('getAllClubs', () => {
    // Create a mock request object to be reused
    const createMockRequest = () =>
      ({
        headers: { 'x-client-type': 'web' },
        cookies: {},
        signedCookies: {},
        get: jest.fn(),
        header: jest.fn(),
        accepts: jest.fn(),
        acceptsEncoding: jest.fn(),
        acceptsLanguages: jest.fn(),
        range: jest.fn(),
        param: jest.fn(),
        is: jest.fn(),
        protocol: 'http',
        secure: false,
        ip: '127.0.0.1',
        ips: [],
        subdomains: [],
        path: '',
        hostname: '',
        host: '',
        fresh: false,
        stale: true,
        xhr: false,
        body: {},
        params: {},
        query: {},
        route: {},
        originalUrl: '',
        baseUrl: '',
        url: '',
        method: 'GET',
      }) as unknown as Request;

    it('should fetch all clubs successfully', async () => {
      const result = {
        data: [
          {
            id: '1',
            name: 'Test Club',
            description: 'Test Description',
            institution_id: 'inst-123',
            country_id: 'country-123',
          },
        ],
        total: 1,
      };
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'name',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };

      (service.getAllClubs as jest.Mock).mockResolvedValue(result);
      (service.addIsMemberFlagToClubs as jest.Mock).mockResolvedValue(result);
      const mockRequest = createMockRequest();

      const response = await controller.getAllClubs(
        mockUser,
        queryParams,
        mockRequest,
      );

      expect(service.getAllClubs).toHaveBeenCalledWith(
        mockUser,
        queryParams,
        mockRequest,
      );
      expect(service.addIsMemberFlagToClubs).toHaveBeenCalledWith(
        result,
        mockUser,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const errorMessage = 'Error fetching all clubs';
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'name',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };
      const error = new Error(errorMessage);

      (service.getAllClubs as jest.Mock).mockRejectedValue(error);
      const mockRequest = createMockRequest();

      try {
        await controller.getAllClubs(mockUser, queryParams, mockRequest);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });
  });

  describe('removeStudentFromClub', () => {
    it('should remove a student from a club successfully', async () => {
      const clubId = 'club-123';
      const studentId = 'student-123';
      const result = { success: true };

      (service.removeStudentFromClub as jest.Mock).mockResolvedValue(result);

      const response = await controller.removeStudentFromClub(
        clubId,
        studentId,
      );

      expect(service.removeStudentFromClub).toHaveBeenCalledWith(
        clubId,
        studentId,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors from service', async () => {
      const clubId = 'club-123';
      const studentId = 'student-123';
      const error = new Error('Error removing student from club');

      (service.removeStudentFromClub as jest.Mock).mockRejectedValue(error);

      try {
        await controller.removeStudentFromClub(clubId, studentId);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });
  });
});
