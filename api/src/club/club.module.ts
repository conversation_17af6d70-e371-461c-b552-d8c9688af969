import { Modu<PERSON> } from '@nestjs/common';
import { ClubController } from './club.controller';
import { ClubService } from './club.service';
import { JwtHelperModule } from '@/jwt-helper/jwt-helper.module';
import { UploadModule } from '@/upload/upload.module';
import { PointSystemModule } from '@/point-system/point_system.module';
import { StudentProfileModule } from '@/student_profile/student_profile.module';

@Module({
  imports: [
    JwtHelperModule,
    UploadModule,
    PointSystemModule,
    StudentProfileModule,
  ],
  providers: [ClubService],
  exports: [ClubService],
  controllers: [ClubController],
})
export class ClubModule {}
