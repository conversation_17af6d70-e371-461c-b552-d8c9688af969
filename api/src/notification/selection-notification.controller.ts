import {
  <PERSON>,
  Post,
  Body,
  UseGuards,
  Logger,
  Param,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UseRoles } from 'nest-access-control';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { RoleGuard } from '@/guards/role.guard';
import { SelectionNotificationService } from './selection-notification.service';
import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

// Define the DTO for sending push notifications
const sendPushNotificationSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  title: z.string().min(1, 'Title is required'),
  body: z.string().min(1, 'Body is required'),
  data: z.record(z.any()).optional(),
  notificationCode: z.string().optional(),
  notification_type_id: z.string().uuid('Invalid notification type ID'),
  channels: z.array(z.string()).optional().default(['push']),
  overridePreferences: z.boolean().optional().default(true),
});

export class SendPushNotificationDto extends createZodDto(
  sendPushNotificationSchema,
) {}

@ApiTags('Selection Notifications')
@Controller({ version: '1', path: 'notification/selection' })
export class SelectionNotificationController {
  private readonly logger = new Logger(SelectionNotificationController.name, {
    timestamp: true,
  });

  constructor(
    private readonly selectionNotificationService: SelectionNotificationService,
  ) {}

  @Post('push')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'create', possession: 'any' })
  @ApiOperation({ summary: 'Send a push notification to a user' })
  @ApiResponse({
    status: 200,
    description: 'Push notification sent successfully',
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async sendPushNotification(@Body() data: SendPushNotificationDto) {
    try {
      const jobId =
        await this.selectionNotificationService.sendDirectPushNotification({
          userId: data.userId,
          notificationTypeId: data.notification_type_id,
          title: data.title,
          body: data.body,
          data: data.data || {},
          channels: data.channels || ['push'],
          overridePreferences: data.overridePreferences ?? true,
        });

      return {
        success: true,
        message: 'Push notification sent successfully',
        jobId,
      };
    } catch (error) {
      this.logger.error('Error sending push notification', error);
      throw error;
    }
  }

  @Post('raffle-winner/:userId')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'create', possession: 'any' })
  @ApiOperation({ summary: 'Send a raffle winner notification to a user' })
  @ApiResponse({
    status: 200,
    description: 'Raffle winner notification sent successfully',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async sendRaffleWinnerNotification(
    @Param('userId') userId: string,
    @Body()
    data: {
      raffleId: string;
      raffleName: string;
      raffleDescription?: string;
      prize?: string;
      claimDeadline?: Date;
      actionUrl?: string;
    },
  ) {
    try {
      const jobId =
        await this.selectionNotificationService.sendRaffleWinnerNotification(
          userId,
          data.raffleId,
          data.raffleName,
          data.raffleDescription,
          data.prize,
          data.claimDeadline,
          data.actionUrl,
        );

      return {
        success: true,
        message: 'Raffle winner notification sent successfully',
        jobId,
      };
    } catch (error) {
      this.logger.error('Error sending raffle winner notification', error);
      throw error;
    }
  }

  @Post('opportunity-selection/:userId')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'create', possession: 'any' })
  @ApiOperation({
    summary: 'Send an opportunity selection notification to a user',
  })
  @ApiResponse({
    status: 200,
    description: 'Opportunity selection notification sent successfully',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async sendOpportunitySelectionNotification(
    @Param('userId') userId: string,
    @Body()
    data: {
      opportunityId: string;
      opportunityTitle: string;
      opportunityDescription?: string;
      startDate?: Date;
      endDate?: Date;
      confirmDeadline?: Date;
      applicationUrl?: string;
    },
  ) {
    try {
      const jobId =
        await this.selectionNotificationService.sendOpportunitySelectionNotification(
          userId,
          data.opportunityId,
          data.opportunityTitle,
          data.opportunityDescription,
          data.startDate,
          data.endDate,
          data.confirmDeadline,
          data.applicationUrl,
        );

      return {
        success: true,
        message: 'Opportunity selection notification sent successfully',
        jobId,
      };
    } catch (error) {
      this.logger.error(
        'Error sending opportunity selection notification',
        error,
      );
      throw error;
    }
  }
}
