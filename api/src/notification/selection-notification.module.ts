import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SelectionNotificationService } from './selection-notification.service';
import { SelectionNotificationController } from './selection-notification.controller';
import { EnhancedNotificationQueueModule } from '@app/shared/enhanced-notification-queue/enhanced-notification-queue.module';
import { UserRepository } from '@/repositories/user.repository';
import { QueueModule } from '@app/shared/queue/queue.module';

@Module({
  imports: [EnhancedNotificationQueueModule, QueueModule.forFeature()],
  controllers: [SelectionNotificationController],
  providers: [SelectionNotificationService, UserRepository],
  exports: [SelectionNotificationService],
})
export class SelectionNotificationModule {}
