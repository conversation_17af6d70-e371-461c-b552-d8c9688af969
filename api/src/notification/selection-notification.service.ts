import { Injectable, Logger } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { QueueService } from '@app/shared/queue/queue.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EnhancedNotificationQueueService } from '@app/shared/enhanced-notification-queue/enhanced-notification-queue.service';
import { users, student_profiles } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { notification_types } from '@/db/schema/notification_system';
import { EmailService } from '@/mail/email.service';
import { UserRepository } from '@/repositories/user.repository';

@Injectable()
export class SelectionNotificationService {
  private readonly logger = new Logger(SelectionNotificationService.name);

  constructor(
    private readonly queueService: QueueService,
    private readonly drizzle: DrizzleService,
    private readonly notificationQueueService: EnhancedNotificationQueueService,
    private readonly moduleRef: ModuleRef,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Send notification to raffle winner
   * @param userId User ID of the winner
   * @param raffleId ID of the raffle
   * @param raffleName Name of the raffle
   * @param raffleDescription Description of the raffle (optional)
   * @param prize Prize information (optional)
   * @param claimDeadline Deadline to claim the prize (optional)
   * @param actionUrl URL for claiming the prize (optional)
   */
  async sendRaffleWinnerNotification(
    userId: string,
    raffleId: string,
    raffleName: string,
    raffleDescription?: string,
    prize?: string,
    claimDeadline?: Date,
    actionUrl?: string,
  ): Promise<string> {
    try {
      this.logger.log(
        `Attempting to send raffle winner notification to user ${userId} for raffle ${raffleId} (${raffleName})`,
      );

      // Get user email and profile info
      const [userData] = await this.drizzle.db
        .select({
          email: users.email,
          id: users.id,
        })
        .from(users)
        .where(eq(users.id, userId));

      if (!userData) {
        throw new Error(`User with ID ${userId} not found`);
      }

      this.logger.log(`Found user email: ${userData.email} for user ${userId}`);

      // Try to get student profile for name information
      let firstName = '';
      let lastName = '';
      try {
        const [profile] = await this.drizzle.db
          .select({
            firstName: student_profiles.first_name,
            lastName: student_profiles.last_name,
          })
          .from(student_profiles)
          .where(eq(student_profiles.user_id, userId));

        if (profile) {
          firstName = profile.firstName || '';
          lastName = profile.lastName || '';
          this.logger.debug(
            `Found student profile for user ${userId}: ${firstName} ${lastName}`,
          );
        }
      } catch (profileError: any) {
        this.logger.warn(
          `Could not retrieve student profile for user ${userId}: ${profileError.message || 'Unknown error'}`,
        );
      }

      // Format claim deadline if provided
      let formattedClaimDeadline: string | undefined;
      if (claimDeadline) {
        // Format date as 'Thu, 08 May 2025 11:58:12'
        formattedClaimDeadline = claimDeadline.toUTCString();
      }

      // Get user's name or use email username as fallback
      const userName = firstName
        ? `${firstName} ${lastName || ''}`.trim()
        : userData.email.split('@')[0];

      // Prepare email context
      const emailContext = {
        subject: '🎉 Congratulations, You Won!',
        userName: userName,
        raffleName,
        raffleDescription,
        prize,
        claimDeadline: formattedClaimDeadline,
        actionUrl,
      };

      this.logger.log(
        `Preparing to send raffle winner email to ${userData.email} with template: raffle-winner`,
      );

      // First, try to send email directly using the EmailService
      try {
        // Get the EmailService from the app module
        const emailService = this.moduleRef.get(EmailService, {
          strict: false,
        });

        if (emailService) {
          await emailService.sendCustomEmail({
            email: userData.email,
            subject: '🎉 Congratulations, You Won!',
            template: 'raffle-winner',
            context: emailContext,
          });

          this.logger.log(
            `Successfully sent raffle winner email directly to ${userData.email}`,
          );
        } else {
          this.logger.warn(
            `EmailService not available, falling back to queue for ${userData.email}`,
          );
        }
      } catch (directEmailError: any) {
        this.logger.warn(
          `Failed to send raffle winner email directly, falling back to queue: ${directEmailError.message}`,
        );
      }

      // Send email through queue as a backup or primary method if direct send fails or isn't available
      const emailJobId = await this.queueService.addSingleEmailJob({
        to: userData.email,
        subject: '🎉 Congratulations, You Won!',
        template: 'raffle-winner',
        context: emailContext,
      });

      this.logger.log(
        `Added raffle winner email job ${emailJobId} to queue for ${userData.email}`,
      );

      // Try to send through notification system as well
      try {
        // Get notification type for raffle winners
        const [notificationType] = await this.drizzle.db
          .select()
          .from(notification_types)
          .where(eq(notification_types.code, 'raffle_winner'));

        if (notificationType) {
          // Send through notification queue
          const notificationJobId =
            await this.notificationQueueService.sendNotificationToUser({
              notificationTypeId: notificationType.id,
              targetAudience: {
                filters: { userId },
              },
              data: {
                title: '🎉 Congratulations, You Won!',
                body: `You've been selected as a winner in the ${raffleName} raffle!`,
                raffleName,
                raffleDescription,
                prize,
                claimDeadline: formattedClaimDeadline,
                actionUrl,
                raffleId,
                type: 'raffle_winner',
              },
              channels: ['push', 'email'],
            });

          this.logger.log(
            `Added raffle winner notification job ${notificationJobId} to queue for user ${userId}`,
          );
        } else {
          this.logger.warn(
            `No 'raffle_winner' notification type found in the database`,
          );
        }
      } catch (error: any) {
        // Log error but don't fail the entire process
        this.logger.error(
          `Failed to send raffle winner notification through queue: ${error.message}`,
          error.stack,
        );
      }

      return emailJobId;
    } catch (error: any) {
      this.logger.error(
        `Failed to send raffle winner notification: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send notification to opportunity selection
   * @param userId User ID of the selected user
   * @param opportunityId ID of the opportunity
   * @param opportunityTitle Title of the opportunity
   * @param opportunityDescription Description of the opportunity (optional)
   * @param startDate Start date of the opportunity (optional)
   * @param endDate End date of the opportunity (optional)
   * @param confirmDeadline Deadline to confirm participation (optional)
   * @param applicationUrl URL for confirming participation (optional)
   */
  async sendOpportunitySelectionNotification(
    userId: string,
    opportunityId: string,
    opportunityTitle: string,
    opportunityDescription?: string,
    startDate?: Date,
    endDate?: Date,
    confirmDeadline?: Date,
    applicationUrl?: string,
  ): Promise<string> {
    try {
      this.logger.log(
        `Attempting to send opportunity selection notification to user ${userId} for opportunity ${opportunityId} (${opportunityTitle})`,
      );

      // Validate that the user is active and not deleted
      const validUser = await this.userRepository.validateActiveUser(userId);
      if (!validUser) {
        this.logger.warn(
          `Skipping opportunity selection notification for user ${userId}: user not active or deleted`,
        );
        return 'skipped-inactive-user';
      }

      // Get user email and profile info
      const [userData] = await this.drizzle.db
        .select({
          email: users.email,
          id: users.id,
        })
        .from(users)
        .where(eq(users.id, userId));

      if (!userData) {
        throw new Error(`User with ID ${userId} not found`);
      }

      this.logger.log(`Found user email: ${userData.email} for user ${userId}`);

      // Try to get student profile for name information
      let firstName = '';
      let lastName = '';
      try {
        const [profile] = await this.drizzle.db
          .select({
            firstName: student_profiles.first_name,
            lastName: student_profiles.last_name,
          })
          .from(student_profiles)
          .where(eq(student_profiles.user_id, userId));

        if (profile) {
          firstName = profile.firstName || '';
          lastName = profile.lastName || '';
          this.logger.debug(
            `Found student profile for user ${userId}: ${firstName} ${lastName}`,
          );
        }
      } catch (profileError: any) {
        this.logger.warn(
          `Could not retrieve student profile for user ${userId}: ${profileError.message || 'Unknown error'}`,
        );
      }

      // Format dates if provided
      let formattedStartDate: string | undefined;
      let formattedEndDate: string | undefined;
      let formattedConfirmDeadline: string | undefined;

      if (startDate) {
        // Format date as 'Thu, 08 May 2025 11:58:12'
        formattedStartDate = startDate.toUTCString();
      }
      if (endDate) {
        // Format date as 'Thu, 08 May 2025 11:58:12'
        formattedEndDate = endDate.toUTCString();
      }
      if (confirmDeadline) {
        // Format date as 'Thu, 08 May 2025 11:58:12'
        formattedConfirmDeadline = confirmDeadline.toUTCString();
      }

      // Get user's name or use email username as fallback
      const userName = firstName
        ? `${firstName} ${lastName || ''}`.trim()
        : userData.email.split('@')[0];

      // Prepare email context
      const emailContext = {
        subject: "🎉 You've Been Selected!",
        userName: userName,
        opportunityTitle,
        opportunityDescription,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        confirmDeadline: formattedConfirmDeadline,
        applicationUrl,
      };

      this.logger.log(
        `Preparing to send opportunity selection email to ${userData.email} with template: opportunity-selection`,
      );

      // First, try to send email directly using the EmailService
      try {
        // Get the EmailService from the app module
        const emailService = this.moduleRef.get(EmailService, {
          strict: false,
        });

        if (emailService) {
          await emailService.sendCustomEmail({
            email: userData.email,
            subject: "🎉 You've Been Selected!",
            template: 'opportunity-selection',
            context: emailContext,
          });

          this.logger.log(
            `Successfully sent opportunity selection email directly to ${userData.email}`,
          );
        } else {
          this.logger.warn(
            `EmailService not available, falling back to queue for ${userData.email}`,
          );
        }
      } catch (directEmailError: any) {
        this.logger.warn(
          `Failed to send opportunity selection email directly, falling back to queue: ${directEmailError.message}`,
        );
      }

      // Send email through queue as a backup or primary method if direct send fails or isn't available
      const emailJobId = await this.queueService.addSingleEmailJob({
        to: userData.email,
        subject: "🎉 You've Been Selected!",
        template: 'opportunity-selection',
        context: emailContext,
      });

      this.logger.log(
        `Added opportunity selection email job ${emailJobId} to queue for ${userData.email}`,
      );

      // Try to send through notification system as well
      try {
        // Get notification type for opportunity selections
        const [notificationType] = await this.drizzle.db
          .select()
          .from(notification_types)
          .where(eq(notification_types.code, 'opportunity_selection'));

        if (notificationType) {
          // Send through notification queue
          const notificationJobId =
            await this.notificationQueueService.sendNotificationToUser({
              notificationTypeId: notificationType.id,
              targetAudience: {
                filters: { userId },
              },
              data: {
                title: "🎉 You've Been Selected!",
                body: `You've been selected for the ${opportunityTitle} opportunity!`,
                opportunityTitle,
                opportunityDescription,
                startDate: formattedStartDate,
                endDate: formattedEndDate,
                confirmDeadline: formattedConfirmDeadline,
                applicationUrl,
                opportunityId,
                opportunity_id: opportunityId,
                type: 'opportunity_selection',
              },
              channels: ['push', 'email'],
            });

          this.logger.debug(
            `Sent opportunity selection notification through queue to user ${userId} (Job ID: ${notificationJobId})`,
          );
        }
      } catch (error: any) {
        // Log error but don't fail the entire process
        this.logger.error(
          `Failed to send opportunity selection notification through queue: ${error.message}`,
          error.stack,
        );
      }

      return emailJobId;
    } catch (error: any) {
      this.logger.error(
        `Failed to send opportunity selection notification: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send a push notification to a specific user
   * @param userId User ID to send the notification to
   * @param title Title of the notification
   * @param body Body text of the notification
   * @param data Additional data to include with the notification (optional)
   * @param notificationCode Code of the notification type (default: 'general')
   * @returns The notification job ID
   */
  async sendPushNotification(
    userId: string,
    title: string,
    body: string,
    data: Record<string, any> = {},
    notificationCode: string = 'general',
  ): Promise<string> {
    try {
      // Validate that the user is active and not deleted
      const validUser = await this.userRepository.validateActiveUser(userId);
      if (!validUser) {
        this.logger.warn(
          `Skipping push notification for user ${userId}: user not active or deleted`,
        );
        throw new Error(
          `User with ID ${userId} is not active or has been deleted`,
        );
      }

      // Get notification type
      const [notificationType] = await this.drizzle.db
        .select()
        .from(notification_types)
        .where(eq(notification_types.code, notificationCode));

      if (!notificationType) {
        throw new Error(
          `Notification type with code ${notificationCode} not found`,
        );
      }

      // Convert all data values to strings for FCM compatibility
      const stringifiedData: Record<string, string> = {};

      // Add title and body
      stringifiedData.title = title;
      stringifiedData.body = body;
      stringifiedData.type = notificationCode;

      // Convert all other data values to strings
      Object.entries(data).forEach(([key, value]) => {
        stringifiedData[key] =
          typeof value === 'object' ? JSON.stringify(value) : String(value);
      });

      // Send push notification through queue
      const notificationJobId =
        await this.notificationQueueService.sendNotificationToUser({
          notificationTypeId: notificationType.id,
          targetAudience: {
            filters: { userId },
          },
          data: stringifiedData,
          channels: ['push'],
          overridePreferences: true,
        });

      this.logger.debug(
        `Sent push notification to user ${userId} (Job ID: ${notificationJobId})`,
      );

      return notificationJobId;
    } catch (error: any) {
      this.logger.error(
        `Failed to send push notification to user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send a push notification directly using notification type ID
   * @param params Parameters for the notification
   * @returns The notification job ID
   */
  async sendDirectPushNotification(params: {
    userId: string;
    notificationTypeId: string;
    title: string;
    body: string;
    data?: Record<string, any>;
    channels?: string[];
    overridePreferences?: boolean;
  }): Promise<string> {
    const {
      userId,
      notificationTypeId,
      title,
      body,
      data = {},
      channels = ['push'],
      overridePreferences = true,
    } = params;

    try {
      // Validate that the user is active and not deleted
      const validUser = await this.userRepository.validateActiveUser(userId);
      if (!validUser) {
        this.logger.warn(
          `Skipping direct push notification for user ${userId}: user not active or deleted`,
        );
        throw new Error(
          `User with ID ${userId} is not active or has been deleted`,
        );
      }

      // Convert all data values to strings for FCM compatibility
      const stringifiedData: Record<string, string> = {};

      // Add title and body
      stringifiedData.title = title;
      stringifiedData.body = body;

      // Convert all other data values to strings
      Object.entries(data).forEach(([key, value]) => {
        stringifiedData[key] =
          typeof value === 'object' ? JSON.stringify(value) : String(value);
      });

      // Send notification through queue
      const notificationJobId =
        await this.notificationQueueService.sendNotificationToUser({
          notificationTypeId,
          targetAudience: {
            filters: { userId },
          },
          data: stringifiedData,
          channels,
          overridePreferences,
        });

      this.logger.debug(
        `Sent direct push notification to user ${userId} (Job ID: ${notificationJobId})`,
      );

      return notificationJobId;
    } catch (error: any) {
      this.logger.error(
        `Failed to send direct push notification to user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
