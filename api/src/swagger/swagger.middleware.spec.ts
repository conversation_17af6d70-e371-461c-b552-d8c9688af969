import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { Test, TestingModule } from '@nestjs/testing';
import { NextFunction, Request, Response } from 'express';
import { swaggerMiddleware } from './swagger.middleware';

describe('SwaggerMiddleware', () => {
  let service: EnvConfig;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNextFunction: NextFunction;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [EnvConfig],
    }).compile();

    service = module.get<EnvConfig>(EnvConfig);
    service.API_DOC_USERNAME = 'testuser';
    service.API_DOC_PASSWORD = 'testpass';

    mockRequest = { headers: {} };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
    };
    mockNextFunction = jest.fn();
  });

  it('should respond with 401 if no authorization header is present', () => {
    const middleware = swaggerMiddleware(service);
    middleware(
      mockRequest as Request,
      mockResponse as Response,
      mockNextFunction,
    );
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.set).toHaveBeenCalledWith('WWW-Authenticate', 'Basic');
    expect(mockNextFunction).toHaveBeenCalled();
  });

  it('should respond with 401 if credentials are invalid', () => {
    const middleware = swaggerMiddleware(service);
    mockRequest = {
      headers: {
        authorization:
          'Basic ' + Buffer.from('wronguser:wrongpass').toString('base64'),
      },
    };
    middleware(
      mockRequest as Request,
      mockResponse as Response,
      mockNextFunction,
    );
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.set).toHaveBeenCalledWith('WWW-Authenticate', 'Basic');
    expect(mockNextFunction).toHaveBeenCalled();
  });

  it('should call next if credentials are valid', () => {
    const middleware = swaggerMiddleware(service);
    mockRequest = {
      headers: {
        authorization:
          'Basic ' + Buffer.from('testuser:testpass').toString('base64'),
      },
    };
    middleware(
      mockRequest as Request,
      mockResponse as Response,
      mockNextFunction,
    );
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.set).not.toHaveBeenCalled();
    expect(mockNextFunction).toHaveBeenCalled();
  });
});
