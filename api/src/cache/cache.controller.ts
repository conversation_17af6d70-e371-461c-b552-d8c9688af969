import {
  Controller,
  Delete,
  Get,
  Param,
  Post,
  UseGuards,
  Logger,
  Query,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiQuery,
} from '@nestjs/swagger';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { CacheService } from '@app/shared/cache/cache.service';
import { RedisService } from '@app/shared/redis/redis.service';
import {
  CACHE_PREFIXES,
  CacheRoutes,
} from '@app/shared/constants/cache.constant';
import { CacheManagerService } from '@app/shared/cache/cache-manager.service';
import { CacheWarmerService } from './cache-warmer.service';

@ApiTags('Cache')
@Controller({ version: '1', path: 'cache' })
@UseGuards(RoleGuard)
@ApiBearerAuth()
export class CacheController {
  private readonly logger = new Logger(CacheController.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly redisService: RedisService,
    private readonly cacheManager: CacheManagerService,
    private readonly cacheWarmer: CacheWarmerService,
  ) {}

  @Get(CacheRoutes.STATS)
  @UseRoles({ resource: 'cache', action: 'read', possession: 'any' })
  @ApiOperation({ summary: 'Get cache statistics' })
  async getStats() {
    const cacheStats = this.cacheService.getStats();
    const memoryStats = await this.cacheManager.getMemoryStats();
    const keyStats = await this.cacheManager.getKeyStatsByPrefix();

    return {
      performance: cacheStats,
      memory: memoryStats,
      keys: keyStats,
    };
  }

  @Delete(CacheRoutes.DELETE_ALL)
  @UseRoles({ resource: 'cache', action: 'delete', possession: 'any' })
  @ApiOperation({ summary: 'Clear all cache entries' })
  async clearAll() {
    this.logger.warn('Clearing all cache entries');
    await this.redisService.clearAll();
    return { message: 'All caches cleared successfully' };
  }

  @Delete(CacheRoutes.DELETE_BY_PREFIX)
  @UseRoles({ resource: 'cache', action: 'delete', possession: 'any' })
  @ApiOperation({ summary: 'Clear cache entries by prefix' })
  async clearByPrefix(@Param('prefix') prefix: string) {
    // Validate prefix
    const validPrefixes = Object.values(CACHE_PREFIXES);
    if (!validPrefixes.includes(prefix as any)) {
      throw new BadRequestException({
        error: 'Invalid cache prefix',
        validPrefixes,
      });
    }

    const count = await this.cacheManager.clearByPrefix(prefix);

    return {
      message: `Cleared ${count} cache entries with prefix '${prefix}'`,
    };
  }

  @Post(CacheRoutes.WARMUP_CACHE)
  @UseRoles({ resource: 'cache', action: 'create', possession: 'any' })
  @ApiOperation({ summary: 'Warm up cache for frequently accessed data' })
  @ApiQuery({ name: 'entity', enum: CACHE_PREFIXES, required: false })
  async warmupCache(@Query('entity') entity?: string) {
    this.logger.log(`Starting cache warmup${entity ? ` for ${entity}` : ''}`);

    // Trigger cache warming for specific entities
    await this.cacheWarmer.manualWarmup(entity);

    return {
      message: `Cache warmup initiated${entity ? ` for ${entity}` : ''}`,
    };
  }

  @Post(CacheRoutes.RUN_CLEANUP)
  @UseRoles({ resource: 'cache', action: 'create', possession: 'any' })
  @ApiOperation({ summary: 'Run cache cleanup process' })
  async runCleanup() {
    this.logger.log('Manually triggering cache cleanup');
    await this.cacheManager.cleanupStaleCache();
    return { message: 'Cache cleanup completed' };
  }
}
