import { Modu<PERSON> } from '@nestjs/common';
import { <PERSON>ache<PERSON>ontroller } from './cache.controller';
import { CacheWarmerService } from './cache-warmer.service';
import { CacheModule as SharedCacheModule } from '@app/shared/cache/cache.module';
import { CacheManagerService } from '@app/shared/cache/cache-manager.service';
import { ScheduleModule } from '@nestjs/schedule';
import { SkillsModule } from '@/skills/skills.module';
import { EnhancedNotificationModule } from '@app/shared/enhanced-notification/enhanced-notification.module';
import { McqModule } from '@/mcq/mcq.module';

@Module({
  imports: [
    SharedCacheModule,
    ScheduleModule.forRoot(),
    EnhancedNotificationModule,
    SkillsModule,
    McqModule,
  ],
  controllers: [CacheController],
  providers: [CacheWarmerService, CacheManagerService],
  exports: [CacheWarmerService, CacheManagerService],
})
export class CacheModule {}
