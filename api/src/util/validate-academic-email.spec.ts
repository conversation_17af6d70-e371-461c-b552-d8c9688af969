import { validateAcademicEmail } from './validate-academic-email';

describe('validateAcademicEmail', () => {
  it('should return true for a valid academic email with .edu domain', () => {
    const email = '<EMAIL>';
    expect(validateAcademicEmail(email)).toBe(true);
  });

  it('should return true for a valid academic email with .ac domain', () => {
    const email = '<EMAIL>';
    expect(validateAcademicEmail(email)).toBe(true);
  });

  it('should return false for a non-academic email', () => {
    const email = '<EMAIL>';
    expect(validateAcademicEmail(email)).toBe(false);
  });

  it('should return false for an email without a domain', () => {
    const email = 'test@';
    expect(validateAcademicEmail(email)).toBe(false);
  });

  it('should return false for an email with a non-academic domain', () => {
    const email = '<EMAIL>';
    expect(validateAcademicEmail(email)).toBe(false);
  });

  it('should return false for an email with an invalid format', () => {
    const email = 'invalid-email-format';
    expect(validateAcademicEmail(email)).toBe(false);
  });
});
