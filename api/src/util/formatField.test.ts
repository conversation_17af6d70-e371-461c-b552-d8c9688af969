import { formatField } from './formatField';

describe('formatField', () => {
  it('should return the first element if the field is a non-empty array', () => {
    expect(formatField([1, 2, 3])).toBe(1);
  });

  it('should return null if the field is an empty array', () => {
    expect(formatField([])).toBeNull();
  });

  it('should return the field if it is not an array and is truthy', () => {
    expect(formatField('test')).toBe('test');
    expect(formatField(123)).toBe(123);
    expect(formatField(true)).toBe(true);
  });

  it('should return null if the field is not an array and is falsy', () => {
    expect(formatField(null)).toBeNull();
    expect(formatField(undefined)).toBeNull();
    expect(formatField('')).toBeNull();
    expect(formatField(0)).toBeNull();
    expect(formatField(false)).toBeNull();
  });
});
