import { getTokenFromHeader } from './get-token';
import { Request } from 'express';

describe('getTokenFromHeader', () => {
  it('should return null if authorization header is not present', () => {
    const req = {
      headers: {},
    } as Request;
    expect(getTokenFromHeader(req)).toBeNull();
  });

  it('should return null if authorization header does not start with <PERSON><PERSON>', () => {
    const req = {
      headers: {
        authorization: 'Basic abcdefg',
      },
    } as Request;
    expect(getTokenFromHeader(req)).toBeNull();
  });

  it('should return null if token is not present', () => {
    const req = {
      headers: {
        authorization: 'Bearer',
      },
    } as Request;
    expect(getTokenFromHeader(req)).toBeNull();
  });

  it('should return the token if authorization header is valid', () => {
    const req = {
      headers: {
        authorization: 'Bearer abcdefg',
      },
    } as Request;
    expect(getTokenFromHeader(req)).toBe('abcdefg');
  });
});
