import { assignDateField } from './date-util';

describe('assignDateField', () => {
  it('should return the ISO string of the provided date', () => {
    const date = '2023-10-01T00:00:00.000Z';
    expect(assignDateField(date)).toBe(new Date(date).toISOString());
  });

  it('should return the current date ISO string if value is null', () => {
    const result = assignDateField(null);
    expect(result).toBe(new Date(result).toISOString());
  });

  it('should return the current date ISO string if value is undefined', () => {
    const result = assignDateField(undefined);
    expect(result).toBe(new Date(result).toISOString());
  });

  it('should return the current date ISO string if value is an empty string', () => {
    const result = assignDateField('');
    expect(result).toBe(new Date(result).toISOString());
  });
});
