import { trimAndValidate } from './trim-validate';

describe('trimAndValidate', () => {
  it('should return undefined for null input', () => {
    expect(trimAndValidate(null)).toBeUndefined();
  });

  it('should return undefined for undefined input', () => {
    expect(trimAndValidate(undefined)).toBeUndefined();
  });

  it('should trim and return string for string input', () => {
    expect(trimAndValidate('  hello  ')).toBe('hello');
  });

  it('should convert number to string and trim it', () => {
    expect(trimAndValidate(123)).toBe('123');
  });

  it('should return empty string for input with only spaces', () => {
    expect(trimAndValidate('   ')).toBe('');
  });

  it('should handle empty string input', () => {
    expect(trimAndValidate('')).toBe('');
  });

  it('should handle zero as input', () => {
    expect(trimAndValidate(0)).toBe('0');
  });

  it('should handle negative number as input', () => {
    expect(trimAndValidate(-123)).toBe('-123');
  });
});
