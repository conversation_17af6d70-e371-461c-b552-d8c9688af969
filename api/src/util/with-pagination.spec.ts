import { withPagination } from './with-pagination';
import { PgSelect } from 'drizzle-orm/pg-core';
import { jest } from '@jest/globals';

describe('withPagination', () => {
  let mockQueryBuilder: PgSelect;

  beforeEach(() => {
    mockQueryBuilder = {
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
    } as unknown as PgSelect;
  });

  it('should apply default pagination when no page and pageSize are provided', () => {
    withPagination(mockQueryBuilder);

    expect(mockQueryBuilder.limit).toHaveBeenCalledWith(10);
    expect(mockQueryBuilder.offset).toHaveBeenCalledWith(0);
  });

  it('should apply correct pagination when page and pageSize are provided', () => {
    withPagination(mockQueryBuilder, 2, 20);

    expect(mockQueryBuilder.limit).toHaveBeenCalledWith(20);
    expect(mockQueryBuilder.offset).toHaveBeenCalledWith(20);
  });

  it('should apply correct pagination when page is provided and pageSize is default', () => {
    withPagination(mockQueryBuilder, 3);

    expect(mockQueryBuilder.limit).toHaveBeenCalledWith(10);
    expect(mockQueryBuilder.offset).toHaveBeenCalledWith(20);
  });

  it('should apply correct pagination when pageSize is provided and page is default', () => {
    withPagination(mockQueryBuilder, undefined, 15);

    expect(mockQueryBuilder.limit).toHaveBeenCalledWith(15);
    expect(mockQueryBuilder.offset).toHaveBeenCalledWith(0);
  });
});
