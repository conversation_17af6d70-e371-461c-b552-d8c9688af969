export function validateAcademicEmail(email: string): boolean {
  const domain = email.split('@')[1];
  if (!domain) return false;

  // Regular expression to match common academic email patterns
  const academicPattern =
    /^(?!gmail\.com$|yahoo\.com$|hotmail\.com$|outlook\.com$|live\.com$|aol\.com$)(.*\.(ac|edu|ac\.uk|edu\.au|edu\.ca|edu\.in|edu\.sg|edu\.my|edu\.ph|edu\.hk|edu\.gh))$/;

  return academicPattern.test(domain);
}
