// Test suite for filter functions
describe('Filters', () => {
  describe('add', () => {
    const add = (a: number, b: number) => a + b;

    it('should add two positive numbers correctly', () => {
      expect(add(2, 3)).toBe(5);
    });

    it('should add a positive and negative number correctly', () => {
      expect(add(5, -3)).toBe(2);
    });

    it('should add two negative numbers correctly', () => {
      expect(add(-2, -4)).toBe(-6);
    });

    it('should add zero correctly', () => {
      expect(add(0, 5)).toBe(5);
      expect(add(5, 0)).toBe(5);
      expect(add(0, 0)).toBe(0);
    });

    it('should handle decimal numbers', () => {
      expect(add(1.5, 2.7)).toBeCloseTo(4.2);
    });
  });
});
