import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import {
  ImageFileDTO,
  ImageFileDTOSchema,
  ImageResponseDTO,
  ImageResponseDTOSchema,
} from './upload-image.dto';
import { AwsConfigService } from './aws-config.service';

import { Logger } from '@nestjs/common';
@Injectable()
export class UploadService {
  constructor(private readonly s3Client: AwsConfigService) {}
  private readonly logger = new Logger(UploadService.name);
  async uploadFileToS3(file: Express.Multer.File): Promise<ImageResponseDTO> {
    // Validate the file data using Zod
    const parsedFile = ImageFileDTOSchema.safeParse(file);

    if (!parsedFile.success) {
      this.logger.error('Invalid file data:', parsedFile.error);
      throw new BadRequestException('Invalid file data');
    }

    const validatedFile: ImageFileDTO = parsedFile.data;
    // Check if the buffer is not empty
    if (!validatedFile.buffer || validatedFile.buffer.length === 0) {
      throw new BadRequestException('Empty file buffer');
    }

    const bucketName = process.env.AWS_BUCKET_NAME;
    if (!bucketName) {
      throw new InternalServerErrorException('AWS bucket name is not defined');
    }

    const key = `${Date.now()}-${validatedFile.originalname}`;
    // Set up S3 upload parameters
    const params = {
      Bucket: bucketName,
      Key: key,
      Body: validatedFile.buffer,
      ContentType: file.mimetype,
      ContentLength: file.size,
      Metadata: {
        originalName: file.originalname,
      },
    };

    // Upload file to S3
    let s3Response;
    try {
      s3Response = await this.s3Client.s3.send(new PutObjectCommand(params));
    } catch (error: any) {
      this.logger.error('S3 upload error:', error);
      throw new InternalServerErrorException(
        `Failed to upload file to S3 ${error.message}`,
      );
    }

    // Validate the S3 response using Zod
    const parsedResponse = ImageResponseDTOSchema.safeParse(s3Response);
    if (!parsedResponse.success) {
      this.logger.error('Invalid S3 response:', parsedResponse.error);
      throw new InternalServerErrorException(
        `Invalid S3 response ${parsedResponse.error}`,
      );
    }

    const imageUrl = `${process.env.AWS_CLOUDFRONT_URL}/${bucketName}/${key}`;
    return {
      ...parsedResponse.data,
      imageUrl,
    };
  }
}
