import { UploadService } from './upload.service';
import { AwsConfigService } from './aws-config.service';
import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';

// Mock the Logger
jest.mock('@nestjs/common', () => {
  const original = jest.requireActual('@nestjs/common');
  return {
    ...original,
    Logger: jest.fn().mockImplementation(() => ({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    })),
  };
});

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3', () => ({
  PutObjectCommand: jest.fn().mockImplementation((params) => ({
    input: params,
  })),
}));

describe('UploadService', () => {
  let service: UploadService;
  let mockS3Client: any;
  let mockAwsConfigService: AwsConfigService;
  let originalEnv: NodeJS.ProcessEnv;

  // Create a mock file for testing
  const mockFile = {
    fieldname: 'file',
    originalname: 'test-image.jpg',
    encoding: '7bit',
    mimetype: 'image/jpeg',
    buffer: Buffer.from('test image content'),
    size: 100,
    destination: '',
    filename: '',
    path: '',
  } as Express.Multer.File;

  // Mock S3 response
  const mockS3Response = {
    ETag: '"mock-etag"',
    Location: 'https://mock-bucket.s3.amazonaws.com/mock-key',
    Key: 'mock-key',
    Bucket: 'mock-bucket',
  };

  beforeEach(() => {
    // Save original env vars
    originalEnv = { ...process.env };

    // Set up test environment variables
    process.env.AWS_BUCKET_NAME = 'test-bucket';
    process.env.AWS_CLOUDFRONT_URL = 'https://test-cloudfront.com';

    // Create a mock S3 client
    mockS3Client = {
      send: jest.fn().mockResolvedValue(mockS3Response),
    };

    // Create a mock AwsConfigService
    mockAwsConfigService = {
      s3: mockS3Client,
    } as any;

    // Create service directly
    service = new UploadService(mockAwsConfigService);
  });

  afterEach(() => {
    // Restore original environment variables
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadFileToS3', () => {
    it('should successfully upload a file to S3', async () => {
      // Set up environment variables for this test
      process.env.AWS_BUCKET_NAME = 'test-bucket';
      process.env.AWS_CLOUDFRONT_URL = 'https://test-cloudfront.com';

      const result = await service.uploadFileToS3(mockFile);

      // Check that S3 client was called with correct parameters
      expect(mockS3Client.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            Bucket: 'test-bucket',
            Body: mockFile.buffer,
            ContentType: mockFile.mimetype,
            Metadata: {
              originalName: mockFile.originalname,
            },
            Key: expect.stringMatching(/^\d+-test-image\.jpg$/),
          }),
        }),
      );

      // Check the returned result
      expect(result).toEqual(
        expect.objectContaining({
          ...mockS3Response,
          imageUrl: expect.stringContaining(
            'https://test-cloudfront.com/test-bucket/',
          ),
        }),
      );
    });

    it('should throw BadRequestException for invalid file data', async () => {
      // Create an invalid file that will fail Zod validation
      // Since Zod schema allows most fields to be optional, we need to create a truly invalid object
      const invalidFile = null as any;

      await expect(service.uploadFileToS3(invalidFile)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw BadRequestException for empty file buffer', async () => {
      // Create a file with empty buffer
      const emptyBufferFile = {
        ...mockFile,
        buffer: Buffer.alloc(0),
      };

      await expect(service.uploadFileToS3(emptyBufferFile)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw InternalServerErrorException when AWS_BUCKET_NAME is not defined', async () => {
      // Remove the bucket name from env
      delete process.env.AWS_BUCKET_NAME;

      await expect(service.uploadFileToS3(mockFile)).rejects.toThrow(
        InternalServerErrorException,
      );
    });

    it('should throw InternalServerErrorException when S3 upload fails', async () => {
      // Mock S3 client to throw an error
      mockS3Client.send.mockRejectedValueOnce(new Error('S3 upload failed'));

      await expect(service.uploadFileToS3(mockFile)).rejects.toThrow(
        InternalServerErrorException,
      );
    });

    it('should throw InternalServerErrorException for invalid S3 response', async () => {
      // Set up environment variables for this test
      process.env.AWS_BUCKET_NAME = 'test-bucket';
      process.env.AWS_CLOUDFRONT_URL = 'https://test-cloudfront.com';

      // Mock S3 client to return an invalid response that will fail Zod validation
      // Since the Zod schema is permissive, we need to return something that's not an object
      mockS3Client.send.mockResolvedValueOnce(null);

      await expect(service.uploadFileToS3(mockFile)).rejects.toThrow(
        InternalServerErrorException,
      );
    });

    it('should generate a unique key for each upload', async () => {
      // Set up environment variables for this test
      process.env.AWS_BUCKET_NAME = 'test-bucket';
      process.env.AWS_CLOUDFRONT_URL = 'https://test-cloudfront.com';

      // Mock Date.now to return a predictable value
      const originalDateNow = Date.now;
      const mockTimestamp = 1234567890;
      Date.now = jest.fn(() => mockTimestamp);

      await service.uploadFileToS3(mockFile);

      // Check that the key includes the timestamp and original filename
      expect(mockS3Client.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            Key: `${mockTimestamp}-${mockFile.originalname}`,
          }),
        }),
      );

      // Restore original Date.now
      Date.now = originalDateNow;
    });
  });
});
