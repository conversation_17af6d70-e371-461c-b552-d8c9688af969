import { z } from 'zod';

export const ImageFileDTOSchema = z.object({
  fieldname: z.string().optional(),
  originalname: z.string().optional(),
  encoding: z.string().optional(),
  mimetype: z.string().optional(),
  buffer: z.any().optional(),
  size: z.number().optional(),
});
export const ImageResponseDTOSchema = z.object({
  ETag: z.string().optional(),
  Location: z.string().optional(),
  key: z.string().optional(),
  Key: z.string().optional(),
  Bucket: z.string().optional(),
  imageUrl: z.string().optional(),
});

export type ImageResponseDTO = z.infer<typeof ImageResponseDTOSchema>;

export type ImageFileDTO = z.infer<typeof ImageFileDTOSchema>;
