import { S3Client } from '@aws-sdk/client-s3';

import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AwsConfigService {
  public s3: S3Client;
  constructor(envConfig: EnvConfig) {
    this.s3 = new S3Client({
      region: envConfig.AWS_REGION,
      credentials: {
        accessKeyId: envConfig.AWS_ACCESS_KEY_ID,
        secretAccessKey: envConfig.AWS_SECRET_ACCESS_KEY,
      },
      endpoint: envConfig.AWS_ENDPOINT,
      // Remove or set forcePathStyle based on actual needs
      forcePathStyle: !!envConfig.AWS_ENDPOINT,
    });
  }
}
