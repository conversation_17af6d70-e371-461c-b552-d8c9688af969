import { Test, TestingModule } from '@nestjs/testing';
import { UploadController } from './upload.controller';
import { UploadService } from './upload.service';
import { RolesBuilder } from 'nest-access-control';
import { ROLES_BUILDER_TOKEN } from 'nest-access-control';
import { Reflector } from '@nestjs/core';

describe('UploadController', () => {
  let controller: UploadController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UploadController],
      providers: [
        {
          provide: UploadService,
          useValue: {
            uploadFileToS3: jest.fn(),
            uploadFile: jest.fn(),
            deleteFile: jest.fn(),
          },
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: ROLES_BUILDER_TOKEN,
          useValue: new RolesBuilder(),
        },
      ],
    }).compile();

    controller = module.get<UploadController>(UploadController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
