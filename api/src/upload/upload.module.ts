import { Module } from '@nestjs/common';
import { UploadService } from './upload.service';
import { UploadController } from './upload.controller';
import { MulterModule } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { AwsConfigService } from './aws-config.service';

@Module({
  imports: [
    MulterModule.registerAsync({
      useFactory: () => ({
        storage: memoryStorage(),
      }),
    }),
  ],
  exports: [AwsConfigService, UploadService],
  controllers: [UploadController],
  providers: [UploadService, AwsConfigService],
})
export class UploadModule {}
