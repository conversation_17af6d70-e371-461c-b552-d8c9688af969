import { querySchema } from '@/common/dto/query-params.dto';
import {
  skills,
  selectSkillsSchema,
  skillCategory,
  selectSkillCategorySchema,
  skillKeys,
} from '@/db/schema/skills';
import { createInsertSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const baseInsertSkillSchema = createInsertSchema(skills);
const insertSkillSchema = baseInsertSkillSchema.extend({
  skillName: z.string().trim().min(3),
  skillCategoryId: z.string().uuid(),
});

const baseInsertSkillCategorySchema = createInsertSchema(skillCategory);
const insertSkillCategorySchema = baseInsertSkillCategorySchema.extend({
  skillCategoryName: z.string().trim().min(3),
});

const addStudentSkillSchema = z.object({
  skills: z
    .array(
      z.object({
        skillId: z.string().uuid(),
        proficiency: z
          .enum(['beginner', 'intermediate', 'advanced', 'expert'])
          .optional(),
      }),
    )
    .min(1, 'At least one skill is required'),
});

const skillQueryParamsSchema = querySchema.extend({
  sort: z.enum(skillKeys).optional().default('created_at'),
});

export class SkillDto extends createZodDto(insertSkillSchema) {}
export class SkillCategoryDto extends createZodDto(insertSkillCategorySchema) {}
export class AddStudentSkillsDto extends createZodDto(addStudentSkillSchema) {}
export class SkillQueryParamsDto extends createZodDto(skillQueryParamsSchema) {}

export type SkillParam = z.infer<typeof selectSkillsSchema>;
export type SkillCategoryParam = z.infer<typeof selectSkillCategorySchema>;
