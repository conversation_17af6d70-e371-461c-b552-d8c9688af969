import { BadRequestException, Injectable } from '@nestjs/common';
import type { JwtPayload } from 'jsonwebtoken';
import jsonwebtoken from 'jsonwebtoken';
import type { CookieOptions, Request } from 'express';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import cryptoHelper from './helpers/crypto.helper';
import crypto from 'node:crypto';

interface VerifyTokenArg {
  token: string;
  ignoreExpiration?: boolean;
}

@Injectable()
export class JwtHelperService {
  constructor(private readonly envConfig: EnvConfig) {}

  getTokenFromHeader(req: Request) {
    const authorizationHeader = req.headers.authorization;
    if (!authorizationHeader) {
      return null;
    }
    const [bearer, token] = authorizationHeader.split(' ');
    if (!token || bearer !== 'Bearer') {
      return null;
    }
    return token;
  }

  async generateAccessToken({
    userId,
    refreshTokenId,
    expiresIn,
  }: {
    userId: string;
    refreshTokenId?: string;
    expiresIn?: string;
  }) {
    const secret = cryptoHelper.secretFromBuffer(
      this.envConfig.ACCESS_TOKEN_SECRET,
    );
    const jwtId = (await cryptoHelper.asyncRandomBytes(32)).toString('hex');

    const token = jsonwebtoken.sign({ userId, refreshTokenId }, secret, {
      jwtid: jwtId,
      algorithm: 'HS256',
      expiresIn: expiresIn || this.envConfig.ACCESS_TOKEN_EXPIRY,
      issuer: this.envConfig.BACKEND_URL,
      audience: [this.envConfig.FRONTEND_URL],
    });
    return token;
  }

  async generateRefreshToken(userId: string) {
    const secret = cryptoHelper.secretFromBuffer(
      this.envConfig.REFRESH_TOKEN_SECRET,
    );
    const jwtId = (await cryptoHelper.asyncRandomBytes(32)).toString('hex');

    const token = jsonwebtoken.sign({ userId }, secret, {
      jwtid: jwtId,
      algorithm: 'HS256',
      expiresIn: this.envConfig.REFRESH_TOKEN_EXPIRY,
      issuer: this.envConfig.BACKEND_URL,
      audience: [this.envConfig.FRONTEND_URL],
    });
    return { token, jwtId };
  }

  async generateAuthTokens(userId: string) {
    const refreshToken = await this.generateRefreshToken(userId);
    const accessToken = await this.generateAccessToken({
      userId,
      refreshTokenId: refreshToken.jwtId,
    });

    return { accessToken, refreshToken: refreshToken.token };
  }

  async verifyAccessToken(verifyTokenArg: VerifyTokenArg) {
    try {
      const secret = cryptoHelper.secretFromBuffer(
        this.envConfig.ACCESS_TOKEN_SECRET,
      );

      const payload = jsonwebtoken.verify(verifyTokenArg.token, secret, {
        ignoreExpiration: verifyTokenArg.ignoreExpiration,
        algorithms: ['HS256'],
        issuer: this.envConfig.BACKEND_URL,
        audience: [this.envConfig.FRONTEND_URL],
      }) as JwtPayload;

      return payload;
    } catch (error) {
      throw error;
    }
  }

  async verifyRefreshToken(verifyTokenArg: VerifyTokenArg) {
    try {
      const secret = cryptoHelper.secretFromBuffer(
        this.envConfig.REFRESH_TOKEN_SECRET,
      );

      const payload = jsonwebtoken.verify(verifyTokenArg.token, secret, {
        ignoreExpiration: verifyTokenArg.ignoreExpiration,
        algorithms: ['HS256'],
        issuer: this.envConfig.BACKEND_URL,
        audience: [this.envConfig.FRONTEND_URL],
      }) as JwtPayload;

      return payload;
    } catch (error) {
      throw error;
    }
  }

  async refreshAccessToken(req: Request, refreshToken: string) {
    const accessToken = this.getTokenFromHeader(req);
    if (!refreshToken) throw new BadRequestException('Refresh token not found');
    if (!accessToken) throw new BadRequestException('Access token not found');

    const payload = await this.verifyRefreshToken({ token: refreshToken });
    if (!payload) throw new BadRequestException('Invalid refresh token');
    const accessTokenPayload = await this.verifyAccessToken({
      token: accessToken,
      ignoreExpiration: true,
    });
    if (
      !accessTokenPayload ||
      accessTokenPayload.refreshTokenId !== payload.jti
    )
      throw new BadRequestException('Invalid access token');
    const newAccessToken = this.generateAccessToken({
      userId: payload.userId,
      refreshTokenId: payload.jti,
    });
    return newAccessToken;
  }

  generateOtp() {
    const digits = '0123456789';
    let otp = '';

    for (let i = 0; i < 6; i++) {
      const randomValue = crypto.randomInt(0, digits.length);
      otp += digits[randomValue];
    }

    const otpHash = this.hashOtp(otp);

    return { otp, otpHash };
  }

  verifyOtp(otp: string, storedHash: string): boolean {
    const otpHash = this.hashOtp(otp);
    return otpHash === storedHash;
  }

  hashOtp(otp: string): string {
    return crypto
      .createHmac('sha256', this.envConfig.OTP_HASH_SECRET)
      .update(otp)
      .digest('hex');
  }

  getCookieOptions(): CookieOptions {
    const expirationDays = this.envConfig.REFRESH_TOKEN_EXPIRY ?? '';

    return {
      httpOnly: true,
      secure: true,
      signed: false,
      expires: this.envConfig.REFRESH_TOKEN_EXPIRY
        ? new Date(Date.now() + expirationDays)
        : new Date(Date.now() + 1000 * 60 * 60 * 24 * 30), // 30days
      sameSite: 'none',
    };
  }
}
