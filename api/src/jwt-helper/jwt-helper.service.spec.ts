import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { Test } from '@nestjs/testing';
import { JwtHelperService } from './jwt-helper.service';
import cryptoHelper from './helpers/crypto.helper';
import jsonwebtoken from 'jsonwebtoken';

jest.mock('jsonwebtoken', () => ({
  ...jest.requireActual('jsonwebtoken'),
  verify: jest.fn(),
  sign: jest.fn().mockReturnValue('token'),
}));

describe('test JwtHelperService', () => {
  let jwtHelperService: JwtHelperService;

  class EnvConfigMock {}

  beforeEach(async () => {
    const EnvConfigProvider = { provide: EnvConfig, useClass: EnvConfigMock };

    const moduleRef = await Test.createTestingModule({
      imports: [],
      controllers: [],
      providers: [JwtHelperService, EnvConfigProvider],
    }).compile();

    jwtHelperService = moduleRef.get<JwtHelperService>(JwtHelperService);
  });

  describe('JwtHelperService business', () => {
    describe('Getting token from header', () => {
      it('should get token from header', async () => {
        const req = {
          headers: {
            authorization: 'Bearer token',
          },
        };

        const token = jwtHelperService.getTokenFromHeader(req as never);
        expect(token).toBe('token');
      });

      it('should return null if no authorization header', async () => {
        const req = {
          headers: {},
        };

        const token = jwtHelperService.getTokenFromHeader(req as never);
        expect(token).toBe(null);
      });
    });

    describe('Access token', () => {
      it('should generate access token', async () => {
        cryptoHelper.secretFromBuffer = jest.fn().mockResolvedValue('secret');
        cryptoHelper.asyncRandomBytes = jest.fn().mockResolvedValue('random');
        const token = await jwtHelperService.generateAccessToken({
          userId: 'ksflksaflssalaj',
        });

        expect(token).toBe('token');
      });

      it('should verify access ', async () => {
        cryptoHelper.secretFromBuffer = jest.fn().mockResolvedValue('secret');

        jsonwebtoken.verify = jest.fn().mockReturnValue({ userId: 1 });
        const token = await jwtHelperService.verifyAccessToken({
          token: 'token',
        });
        expect(token).toBeDefined();
        expect(token).toEqual({
          userId: 1,
        });
      });

      it('should refresh access token', async () => {
        cryptoHelper.secretFromBuffer = jest.fn().mockResolvedValue('secret');
        cryptoHelper.asyncRandomBytes = jest.fn().mockResolvedValue('random');
        jsonwebtoken.verify = jest.fn().mockReturnValue({ userId: 1 });
        const token = await jwtHelperService.refreshAccessToken(
          {
            cookies: {
              refreshToken: 'refreshToken',
            },
            headers: {
              authorization: 'Bearer token',
            },
          } as never,
          'refreshToken',
        );

        expect(token).toBe('token');
      });
    });
  });
});
