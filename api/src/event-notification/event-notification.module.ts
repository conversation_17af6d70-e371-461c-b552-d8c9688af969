import { Module } from '@nestjs/common';

import { EventNotificationController } from './event-notification.controller';
import { EnhancedNotificationQueueModule } from '@app/shared/enhanced-notification-queue/enhanced-notification-queue.module';
import { EventNotificationScheduler } from './event-notification.scheduler';
import { EventNotificationService } from './event-notification.service';
import { EventNotificationPreferencesService } from './event-notification-preferences.service';

@Module({
  imports: [EnhancedNotificationQueueModule],
  controllers: [EventNotificationController],
  providers: [
    EventNotificationService,
    EventNotificationScheduler,
    EventNotificationPreferencesService,
  ],
  exports: [EventNotificationService, EventNotificationPreferencesService],
})
export class EventNotificationModule {}
