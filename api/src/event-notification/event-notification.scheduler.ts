import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventNotificationService } from './event-notification.service';

@Injectable()
export class EventNotificationScheduler {
  private readonly logger = new Logger(EventNotificationScheduler.name);

  // Define reminder thresholds in minutes
  private readonly REMINDER_THRESHOLDS = [
    1440, // 24 hours
    60,
    10,
  ];

  constructor(
    private readonly eventNotificationService: EventNotificationService,
  ) {}

  /**
   * Check for events that need 24-hour reminders
   * Runs every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async checkDayBeforeReminders() {
    try {
      await this.eventNotificationService.checkAndSendEventReminders(1440);
    } catch (error) {
      this.logger.error(
        'Error checking for 24-hour reminders',
        error instanceof Error ? error.stack : String(error),
      );
    }
  }

  /**
   * Check for events that need 1-hour reminders
   * Runs every 10 minutes
   */
  @Cron(CronExpression.EVERY_10_MINUTES)
  async checkHourBeforeReminders() {
    try {
      await this.eventNotificationService.checkAndSendEventReminders(60);
    } catch (error) {
      this.logger.error(
        'Error checking for 1-hour reminders',
        error instanceof Error ? error.stack : String(error),
      );
    }
  }

  /**
   * Check for events that need 10-minute reminders
   * Runs every 5 minutes
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async checkTenMinuteReminders() {
    try {
      await this.eventNotificationService.checkAndSendEventReminders(10);
    } catch (error) {
      this.logger.error(
        'Error checking for 10-minute reminders',
        error instanceof Error ? error.stack : String(error),
      );
    }
  }
}
