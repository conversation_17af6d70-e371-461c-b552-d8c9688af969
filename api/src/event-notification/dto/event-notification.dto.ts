import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

// Schema for updating event reminder preferences
export const updateEventReminderPreferencesSchema = z.object({
  reminderTimes: z
    .array(z.number().int().positive())
    .min(1)
    .max(5)
    .default([1440, 60]), // Default to 24 hours and 1 hour
});

// Schema for event reminder preferences response
export const eventReminderPreferencesResponseSchema = z.object({
  reminderTimes: z.array(z.number().int().positive()),
  email_enabled: z.boolean().default(true),
  push_enabled: z.boolean().default(true),
  in_app_enabled: z.boolean().default(true),
});

// Create DTO classes
export class UpdateEventReminderPreferencesDto extends createZodDto(
  updateEventReminderPreferencesSchema,
) {}

export class EventReminderPreferencesResponseDto {
  @ApiProperty({
    type: [Number],
    description: 'Reminder times in minutes before event start',
  })
  reminderTimes!: number[];

  @ApiProperty({ description: 'Whether email notifications are enabled' })
  email_enabled!: boolean;

  @ApiProperty({ description: 'Whether push notifications are enabled' })
  push_enabled!: boolean;

  @ApiProperty({ description: 'Whether in-app notifications are enabled' })
  in_app_enabled!: boolean;
}
