import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EnhancedNotificationQueueService } from '@app/shared/enhanced-notification-queue/enhanced-notification-queue.service';
import { events, posts, post_statuses } from '@/db/schema';
import { and, eq, sql } from 'drizzle-orm';
import { NotificationModule } from '@app/shared/constants/notification.constant';
import {
  notification_logs,
  notification_types,
} from '@/db/schema/notification_system';
import { postEngagementsSchema } from '@/db/schema/post_engagements';
import { student_profiles } from '@/db/schema/student_profile';
import { event_notification_preferences } from '@/db/schema/event_notification_preferences';

@Injectable()
export class EventNotificationService {
  private readonly logger = new Logger(EventNotificationService.name);

  // Default reminder times in minutes before event start
  private readonly DEFAULT_REMINDER_TIMES = [1440, 60, 10]; // 24 hours, 1 hour, 10 minutes

  // Maximum number of retry attempts for failed notifications
  private readonly MAX_RETRY_ATTEMPTS = 3;

  // Delay between retry attempts in milliseconds
  private readonly RETRY_DELAY = 5000; // 5 seconds

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly notificationService: EnhancedNotificationQueueService,
  ) {}

  /**
   * Check for upcoming events and send reminders
   * @param thresholdMinutes - Minutes before event to send reminder
   */
  async checkAndSendEventReminders(thresholdMinutes: number): Promise<void> {
    try {
      this.logger.log(
        `Checking for events with ${thresholdMinutes} minute reminder`,
      );

      // Calculate the time window for events
      const now = new Date();
      const reminderTime = new Date(
        now.getTime() + thresholdMinutes * 60 * 1000,
      );

      // Format dates for SQL query
      const currentDate = now.toISOString().split('T')[0];
      const currentTime = now.toTimeString().split(' ')[0];
      const reminderDateTime = reminderTime.toISOString();

      // Find events that are starting within the threshold
      const upcomingEvents = await this.drizzle.db
        .select({
          id: events.id,
          postId: events.postId,
          startDate: events.startDate,
          startTime: events.startTime,
          title: posts.title,
          description: posts.description,
          imageUrl: posts.imageUrl,
          postedBy: posts.postedBy,
          clubId: posts.club_id,
        })
        .from(events)
        .innerJoin(posts, eq(events.postId, posts.id))
        .where(
          and(
            // Event is today
            eq(events.startDate as any, currentDate),
            // Event start time is after current time
            sql`${events.startTime} > ${currentTime}`,
            // Event start time is within the threshold
            sql`CONCAT(${events.startDate}, ' ', ${events.startTime})::timestamp <= ${reminderDateTime}`,
            // Only include active posts
            eq(posts.status, post_statuses.ACTIVE),
            // Exclude events that have already sent this reminder
            sql`NOT EXISTS (
              SELECT 1 FROM ${notification_logs} nl
              WHERE nl.data->>'eventId' = ${events.id}::text
              AND nl.data->>'minutesUntilStart' = ${thresholdMinutes}::text
              AND nl.status = 'sent'
              AND nl.created_at > NOW() - INTERVAL '1 day'
            )`,
          ),
        );

      this.logger.log(
        `Found ${upcomingEvents.length} upcoming events within ${thresholdMinutes} minute threshold`,
      );

      // Process each upcoming event
      for (const event of upcomingEvents) {
        await this.sendEventReminders(event, thresholdMinutes);
      }
    } catch (error: any) {
      this.logger.error(
        `Error checking for event reminders: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Send reminders for a specific event
   * @param event - Event data
   * @param thresholdMinutes - Minutes before event to send reminder
   */
  private async sendEventReminders(
    event: any,
    thresholdMinutes: number,
  ): Promise<void> {
    try {
      // Get notification type for event reminders
      const [notificationType] = await this.drizzle.db
        .select()
        .from(notification_types)
        .where(
          and(
            eq(notification_types.code, 'event_reminder'),
            eq(notification_types.module, NotificationModule.EVENT),
          ),
        );

      if (!notificationType) {
        this.logger.error('Event reminder notification type not found');
        return;
      }

      // Get users who have engaged with this event (RSVP'd, interested, etc.)
      const engagedUsers = await this.drizzle.db
        .select({
          userId: student_profiles.user_id,
        })
        .from(postEngagementsSchema)
        .innerJoin(
          student_profiles,
          eq(postEngagementsSchema.student_profile_id, student_profiles.id),
        )
        .where(eq(postEngagementsSchema.postId, event.postId));

      // Get club members if this is a club event
      let clubMembers: { userId: string }[] = [];
      if (event.clubId) {
        clubMembers = await this.drizzle.db
          .select({
            userId: student_profiles.user_id,
          })
          .from(student_profiles)
          .where(eq(student_profiles.club_id, event.clubId));
      }

      // Combine engaged users and club members, removing duplicates
      const userIds = new Set<string>();
      [...engagedUsers, ...clubMembers].forEach((user) => {
        if (user.userId) userIds.add(user.userId);
      });

      // Filter users based on their event notification preferences
      const interestedUsers: { userId: string; channels: string[] }[] = [];

      for (const userId of userIds) {
        // Get user's event-specific preferences
        const [preferences] = await this.drizzle.db
          .select()
          .from(event_notification_preferences)
          .where(
            and(
              eq(event_notification_preferences.userId, userId),
              eq(event_notification_preferences.eventId, event.id),
            ),
          );

        // Skip users who have explicitly opted out
        if (preferences && preferences.optedIn === false) {
          continue;
        }

        // If user has specific reminder times, check if this threshold is included
        if (
          preferences?.reminderTimes &&
          preferences.reminderTimes.length > 0
        ) {
          if (!preferences.reminderTimes.includes(thresholdMinutes)) {
            // Skip this threshold for this user
            continue;
          }
        }

        // Add user to the list with their preferred channels
        interestedUsers.push({
          userId,
          channels: preferences?.channels || ['push', 'email'],
        });
      }

      // Format event time for display
      const eventTime = new Date(`${event.startDate}T${event.startTime}`);
      const formattedTime = eventTime.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      });
      const formattedDate = eventTime.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
      });

      // Prepare notification data
      const notificationData = {
        eventId: event.id,
        event_id: event.id, // Ensure event_id is always present
        post_id: event.postId,
        title: event.title,
        description: event.description,
        imageUrl: event.imageUrl,
        startDate: event.startDate,
        startTime: event.startTime,
        formattedTime,
        formattedDate,
        minutesUntilStart: thresholdMinutes,
      };

      // Send notification to each interested user with retry logic
      let successCount = 0;
      let failureCount = 0;

      for (const { userId, channels } of interestedUsers) {
        let success = false;
        let attempts = 0;

        while (!success && attempts < this.MAX_RETRY_ATTEMPTS) {
          try {
            attempts++;
            await this.notificationService.sendNotificationToUser({
              notificationTypeId: notificationType.id,
              data: notificationData,
              targetAudience: {
                filters: { userId },
              },
              channels, // Use user's preferred channels
              overridePreferences: false, // Respect user preferences
            });
            success = true;
            successCount++;
          } catch (error: any) {
            this.logger.warn(
              `Failed to send notification to user ${userId} for event ${event.title} (attempt ${attempts}): ${error.message || 'Unknown error'}`,
            );

            if (attempts < this.MAX_RETRY_ATTEMPTS) {
              // Wait before retrying
              await new Promise((resolve) =>
                setTimeout(resolve, this.RETRY_DELAY),
              );
            } else {
              failureCount++;
              this.logger.error(
                `Failed to send notification to user ${userId} for event ${event.title} after ${this.MAX_RETRY_ATTEMPTS} attempts`,
              );
            }
          }
        }
      }

      this.logger.log(
        `Sent ${thresholdMinutes} minute reminders for event ${event.title} to ${successCount}/${interestedUsers.length} users (${failureCount} failed)`,
      );

      // Notify event creator if there were failures
      if (failureCount > 0 && event.postedBy) {
        try {
          await this.notificationService.sendNotificationToUser({
            notificationTypeId: notificationType.id,
            targetAudience: {
              filters: { userId: event.postedBy },
            },
            data: {
              ...notificationData,
              isCreatorNotification: true,
              failureCount,
              totalCount: interestedUsers.length,
              successCount,
            },
            channels: ['in_app'],
            overridePreferences: true, // Always send to creator
          });
        } catch (error: any) {
          this.logger.error(
            `Failed to send notification to event creator ${event.postedBy}: ${error.message || 'Unknown error'}`,
            error?.stack,
          );
        }
      }
    } catch (error: any) {
      this.logger.error(
        `Error sending reminders for event ${event.title}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Get event reminder preferences for a user
   * @param userId - User ID
   */
  async getUserEventReminderPreferences(userId: string): Promise<any> {
    try {
      // Get notification type for event reminders
      const [notificationType] = await this.drizzle.db
        .select()
        .from(notification_types)
        .where(
          and(
            eq(notification_types.code, 'event_reminder'),
            eq(notification_types.module, NotificationModule.EVENT),
          ),
        );

      if (!notificationType) {
        return { reminderTimes: this.DEFAULT_REMINDER_TIMES };
      }

      // Get user preferences for this notification type
      const preferences =
        await this.notificationService.getUserNotificationPreference(
          userId,
          notificationType.id,
        );

      // Extract reminder times from preferences or use defaults
      const reminderTimes =
        preferences?.data?.reminderTimes || this.DEFAULT_REMINDER_TIMES;

      return {
        ...preferences,
        reminderTimes,
      };
    } catch (error: any) {
      this.logger.error(
        `Error getting event reminder preferences for user ${userId}: ${error.message || 'Unknown error'}`,
        error?.stack,
      );
      return { reminderTimes: this.DEFAULT_REMINDER_TIMES };
    }
  }

  /**
   * Update event reminder preferences for a user
   * @param userId - User ID
   * @param reminderTimes - Array of reminder times in minutes
   */
  async updateUserEventReminderPreferences(
    userId: string,
    reminderTimes: number[],
  ): Promise<void> {
    try {
      // Get notification type for event reminders
      const [notificationType] = await this.drizzle.db
        .select()
        .from(notification_types)
        .where(
          and(
            eq(notification_types.code, 'event_reminder'),
            eq(notification_types.module, NotificationModule.EVENT),
          ),
        );

      if (!notificationType) {
        this.logger.error('Event reminder notification type not found');
        return;
      }

      // Get current preferences
      const currentPreferences =
        await this.notificationService.getUserNotificationPreference(
          userId,
          notificationType.id,
        );

      // Update preferences with new reminder times
      await this.notificationService.updateUserNotificationPreferences(userId, [
        {
          notification_type_id: notificationType.id,
          email_enabled: currentPreferences.email_enabled,
          push_enabled: currentPreferences.push_enabled,
          in_app_enabled: currentPreferences.in_app_enabled,
          data: { reminderTimes } as any,
        },
      ]);

      this.logger.log(`Updated event reminder preferences for user ${userId}`);
    } catch (error: any) {
      this.logger.error(
        `Error updating event reminder preferences for user ${userId}: ${error.message || 'Unknown error'}`,
        error?.stack,
      );
    }
  }
}
