import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { event_notification_preferences } from '@/db/schema/event_notification_preferences';
import { and, eq } from 'drizzle-orm';

@Injectable()
export class EventNotificationPreferencesService {
  private readonly logger = new Logger(
    EventNotificationPreferencesService.name,
  );
  private readonly DEFAULT_REMINDER_TIMES = [1440, 60, 10]; // 24 hours, 1 hour, 10 minutes

  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * Get event notification preferences for a user
   * @param userId - User ID
   * @param eventId - Event ID
   * @returns Event notification preferences
   */
  async getEventNotificationPreferences(userId: string, eventId: string) {
    try {
      const [preferences] = await this.drizzle.db
        .select()
        .from(event_notification_preferences)
        .where(
          and(
            eq(event_notification_preferences.userId, userId),
            eq(event_notification_preferences.eventId, eventId),
          ),
        );

      if (!preferences) {
        return {
          optedIn: true,
          reminderTimes: this.DEFAULT_REMINDER_TIMES,
          channels: ['push', 'email'],
        };
      }

      return {
        optedIn: preferences.optedIn,
        reminderTimes: preferences.reminderTimes || this.DEFAULT_REMINDER_TIMES,
        channels: preferences.channels || ['push', 'email'],
      };
    } catch (error: any) {
      this.logger.error(
        `Error getting event notification preferences: ${error.message || 'Unknown error'}`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Set event notification preferences for a user
   * @param userId - User ID
   * @param eventId - Event ID
   * @param optedIn - Whether the user has opted in to notifications
   * @param reminderTimes - Array of reminder times in minutes
   * @param channels - Array of notification channels
   * @returns Updated event notification preferences
   */
  async setEventNotificationPreferences(
    userId: string,
    eventId: string,
    optedIn: boolean = true,
    reminderTimes: number[] = this.DEFAULT_REMINDER_TIMES,
    channels: string[] = ['push', 'email'],
  ) {
    try {
      // Check if preferences already exist
      const [existingPreferences] = await this.drizzle.db
        .select()
        .from(event_notification_preferences)
        .where(
          and(
            eq(event_notification_preferences.userId, userId),
            eq(event_notification_preferences.eventId, eventId),
          ),
        );

      if (existingPreferences) {
        // Update existing preferences
        const [updatedPreferences] = await this.drizzle.db
          .update(event_notification_preferences)
          .set({
            optedIn,
            reminderTimes,
            channels,
            updated_at: new Date().toISOString(),
          })
          .where(eq(event_notification_preferences.id, existingPreferences.id))
          .returning();

        this.logger.log(
          `Updated event notification preferences for user ${userId} and event ${eventId}: optedIn=${optedIn}`,
        );

        return updatedPreferences;
      } else {
        // Create new preferences
        const [newPreferences] = await this.drizzle.db
          .insert(event_notification_preferences)
          .values({
            userId,
            eventId,
            optedIn,
            reminderTimes,
            channels,
          })
          .returning();

        this.logger.log(
          `Created event notification preferences for user ${userId} and event ${eventId}: optedIn=${optedIn}`,
        );

        return newPreferences;
      }
    } catch (error: any) {
      this.logger.error(
        `Error setting event notification preferences: ${error.message || 'Unknown error'}`,
        error?.stack,
      );
      throw error;
    }
  }
}
