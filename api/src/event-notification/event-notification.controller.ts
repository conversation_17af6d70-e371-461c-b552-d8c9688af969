import { Body, Controller, Get, Logger, Put, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UseRoles } from 'nest-access-control';
import { RoleGuard } from '@/guards/role.guard';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { User } from '@/guards/user.decorator';
import type { User as IUser } from '@/db/schema/users';
import { EventNotificationService } from './event-notification.service';
import {
  EventReminderPreferencesResponseDto,
  UpdateEventReminderPreferencesDto,
} from './dto/event-notification.dto';
import { ZodSerializerDto } from 'nestjs-zod';
import { EventNotificationRoutes } from '@app/shared/constants/event-notification.constants';

@Controller({ version: '1', path: 'event-notifications' })
@ApiTags('Event Notifications')
export class EventNotificationController {
  private readonly logger = new Logger(EventNotificationController.name);

  constructor(
    private readonly eventNotificationService: EventNotificationService,
  ) {}

  /**
   * Get user event reminder preferences
   */
  @Get(EventNotificationRoutes.GET_PREFERENCES)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'own' })
  @ApiOperation({ summary: 'Get user event reminder preferences' })
  @ApiResponse({
    status: 200,
    description: 'Preferences retrieved successfully',
    type: EventReminderPreferencesResponseDto,
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getUserEventReminderPreferences(@User() user: IUser) {
    try {
      const preferences =
        await this.eventNotificationService.getUserEventReminderPreferences(
          user.id,
        );
      return { success: true, data: preferences };
    } catch (error) {
      this.logger.error('Error getting user event reminder preferences', error);
      throw error;
    }
  }

  /**
   * Update user event reminder preferences
   */
  @Put(EventNotificationRoutes.UPDATE_PREFERENCES)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'own' })
  @ApiOperation({ summary: 'Update user event reminder preferences' })
  @ApiResponse({
    status: 200,
    description: 'Preferences updated successfully',
  })
  @ZodSerializerDto(UpdateEventReminderPreferencesDto)
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async updateUserEventReminderPreferences(
    @User() user: IUser,
    @Body() data: UpdateEventReminderPreferencesDto,
  ) {
    try {
      await this.eventNotificationService.updateUserEventReminderPreferences(
        user.id,
        data.reminderTimes,
      );
      return {
        success: true,
        message: 'Event reminder preferences updated successfully',
      };
    } catch (error) {
      this.logger.error(
        'Error updating user event reminder preferences',
        error,
      );
      throw error;
    }
  }
}
