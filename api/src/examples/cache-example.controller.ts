import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { CacheExampleService } from './cache-example.service';
import { Public } from '@/guards/guard.decorator';

@ApiTags('Cache Example')
@Controller('examples/cache')
export class CacheExampleController {
  private readonly logger = new Logger(CacheExampleController.name);

  constructor(private readonly exampleService: CacheExampleService) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all examples (cached)' })
  async getAll() {
    this.logger.log('Controller: Get all examples');
    return this.exampleService.getAllExamples();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get example by ID (cached)' })
  async getById(@Param('id') id: string) {
    this.logger.log(`Controller: Get example ${id}`);
    return this.exampleService.getExampleById(id);
  }

  @Get('manual/:id')
  @Public()
  @ApiOperation({ summary: 'Get example by ID with manual caching' })
  async getByIdManual(@Param('id') id: string) {
    this.logger.log(`Controller: Get example ${id} with manual caching`);
    return this.exampleService.getWithManualCache(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new example (invalidates cache)' })
  async create(@Body() data: { name: string; value: number }) {
    this.logger.log(`Controller: Create example ${data.name}`);
    return this.exampleService.createExample(data.name, data.value);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an example (invalidates cache)' })
  async update(
    @Param('id') id: string,
    @Body() updates: { name?: string; value?: number },
  ) {
    this.logger.log(`Controller: Update example ${id}`);
    return this.exampleService.updateExample(id, updates);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an example (invalidates cache)' })
  async delete(@Param('id') id: string) {
    this.logger.log(`Controller: Delete example ${id}`);
    const result = await this.exampleService.deleteExample(id);
    return { success: result };
  }
}
