import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CLIENT_TYPE_KEY, ClientType } from './request-validation.decorator';

@Injectable()
export class ClientTypeGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredClientTypes = this.reflector.get<ClientType[]>(
      CLIENT_TYPE_KEY,
      context.getHandler(),
    );
    if (!requiredClientTypes || requiredClientTypes.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const clientType = request.headers['x-client-type'];

    return requiredClientTypes.includes(clientType);
  }
}
