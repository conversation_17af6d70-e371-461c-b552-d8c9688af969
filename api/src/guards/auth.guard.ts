import type { CanActivate, ExecutionContext } from '@nestjs/common';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtHelperService } from '../jwt-helper/jwt-helper.service';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from './guard.decorator';
import { user_states, UserRole } from '@/db/schema';
import { RepositoryService } from '@/repositories/repository.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private jwtHelperService: JwtHelperService,
    private repositoryService: RepositoryService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const skipUuidValidation = this.reflector.getAllAndOverride<boolean>(
      'skipUuidValidation',
      [context.getHandler(), context.getClass()],
    );

    if (isPublic) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    const token = this.jwtHelperService.getTokenFromHeader(request);

    if (skipUuidValidation) {
      // Skip UUID validation for this route
      return true;
    }

    if (!token) {
      throw new UnauthorizedException();
    }
    try {
      const payload = await this.jwtHelperService.verifyAccessToken({ token });
      const user = await this.repositoryService.getUserByKey(
        'id',
        payload.userId,
      );

      if (!user) throw new UnauthorizedException();

      payload.role = user.role;

      if (
        user.state !== user_states.ACTIVE &&
        user.state !== user_states.VERIFIED
      )
        throw new UnauthorizedException();

      request.user = user;
    } catch {
      throw new UnauthorizedException();
    }
    return true;
  }
}

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const roles = this.reflector.getAllAndOverride<UserRole[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);
    if (!roles) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    return this.matchRoles(roles, request.user.role);
  }

  private matchRoles(
    roles: UserRole[],
    role: UserRole,
  ): boolean | PromiseLike<boolean> {
    return roles.some((r) => r === role);
  }
}
