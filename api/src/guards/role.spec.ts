import { ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RoleGuard } from './role.guard';
import { RolesBuilder } from 'nest-access-control';
import { createMock } from '@golevelup/ts-jest';

describe('RoleGuard', () => {
  let roleGuard: RoleGuard;
  let reflector: Reflector;
  let rolesBuilder: RolesBuilder;

  beforeEach(() => {
    reflector = createMock<Reflector>();
    rolesBuilder = createMock<RolesBuilder>();
    roleGuard = new RoleGuard(reflector, rolesBuilder);
  });

  it('should return user roles', async () => {
    const mockContext = createMock<ExecutionContext>();
    const mockUser = { role: 'admin' };

    jest.spyOn(roleGuard as any, 'getUser').mockResolvedValue(mockUser);

    const roles = await (roleGuard as any).getUserRoles(mockContext);
    expect(roles).toBe('admin');
  });

  it('should return user roles as an array', async () => {
    const mockContext = createMock<ExecutionContext>();
    const mockUser = { role: ['admin', 'user'] };

    jest.spyOn(roleGuard as any, 'getUser').mockResolvedValue(mockUser);

    const roles = await (roleGuard as any).getUserRoles(mockContext);
    expect(roles).toEqual(['admin', 'user']);
  });
});
