import {
  createParamDecorator,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';

export const User = createParamDecorator(
  (roles: string[] = [], ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }
    // If roles are specified, check if the user has at least one of them
    if (roles.length > 0) {
      const userRoles: string[] = user.role || [];
      const hasRole = roles.some((role) => userRoles.includes(role));

      if (!hasRole) {
        throw new ForbiddenException(
          'You do not have the required permissions',
        );
      }
    }

    return user;
  },
);
