import { Module } from '@nestjs/common';
import { StudentProfileController } from './student_profile.controller';
import { StudentProfileService } from './student_profile.service';
import { JwtHelperModule } from 'src/jwt-helper/jwt-helper.module';
import { AuthModule } from 'src/auth/auth.module';
import { PointSystemModule } from '@/point-system/point_system.module';
import { McqModule } from '@/mcq/mcq.module';
import { SkillsModule } from '@/skills/skills.module';

@Module({
  imports: [
    AuthModule,
    JwtHelperModule,
    PointSystemModule,
    McqModule,
    SkillsModule,
  ],
  controllers: [StudentProfileController],
  providers: [StudentProfileService],
  exports: [StudentProfileService],
})
export class StudentProfileModule {}
