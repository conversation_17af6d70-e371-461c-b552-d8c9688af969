import { Test, TestingModule } from '@nestjs/testing';
import { StudentProfileService } from './student_profile.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { LeaderBoardService } from '@/mcq/leader-board/leader-board.service';
import { ConflictException } from '@nestjs/common';
import { StudentProfileParams } from './student_profile.dto';

describe('StudentProfileService', () => {
  let service: StudentProfileService;
  let drizzleService: DrizzleService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StudentProfileService,
        {
          provide: DrizzleService,
          useValue: {
            db: {
              query: {
                student_profiles: {
                  findFirst: jest.fn().mockImplementation(() => ({
                    execute: jest.fn().mockResolvedValue(null),
                  })),
                },
                institutions: {
                  findFirst: jest.fn().mockImplementation(() => ({
                    execute: jest.fn().mockResolvedValue(null),
                  })),
                },
              },
              insert: jest.fn().mockImplementation(() => ({
                values: jest.fn().mockImplementation(() => ({
                  returning: jest.fn(),
                })),
              })),
              update: jest.fn().mockImplementation(() => ({
                set: jest.fn().mockImplementation(() => ({
                  where: jest.fn().mockImplementation(() => ({
                    returning: jest.fn(),
                  })),
                })),
              })),
              delete: jest.fn().mockImplementation(() => ({
                where: jest.fn().mockImplementation(() => ({
                  returning: jest.fn(),
                })),
              })),
              select: jest.fn().mockImplementation(() => ({
                from: jest.fn().mockImplementation(() => ({
                  leftJoin: jest.fn().mockImplementation(() => ({
                    leftJoin: jest.fn().mockImplementation(() => ({
                      leftJoin: jest.fn().mockImplementation(() => ({
                        leftJoin: jest.fn().mockImplementation(() => ({
                          leftJoin: jest.fn().mockImplementation(() => ({
                            where: jest.fn(),
                          })),
                        })),
                      })),
                    })),
                  })),
                })),
              })),
              $count: jest.fn(),
            },
          },
        },
        {
          provide: PointSystemRepository,
          useValue: {
            awardPointsToStudent: jest.fn(),
          },
        },
        {
          provide: LeaderBoardService,
          useValue: {
            getStudentRank: jest.fn().mockResolvedValue([
              {
                total_points: 100,
                rank: 1,
              },
            ]),
          },
        },
      ],
    }).compile();

    service = module.get<StudentProfileService>(StudentProfileService);
    drizzleService = module.get<DrizzleService>(DrizzleService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addStudentProfile', () => {
    it('should throw an error if student already exists', async () => {
      // Arrange
      const studentProfileInput: StudentProfileParams = {
        first_name: 'John',
        last_name: 'Doe',
        country_id: 'country-1',
        institution_id: 'institution-1',
        degree: 'Bachelors',
        programme: 'ICT',
        enrollment_date: 2020,
        graduation_date: 2024,
      };

      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'student' as const,
        state: 'active' as const,
        profile_pic_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted: false,
        deleted_at: null,
        student_profile: {
          id: 'profile-1',
          user_id: 'user-1',
          first_name: 'John',
          last_name: 'Doe',
          other_name: null,
          country_id: 'country-1',
          institution_id: 'institution-1',
          date_of_birth: null,
          phone_number: null,
          enrollment_date: 2020,
          graduation_date: 2024,
          github_profile: null,
          linkedin_profile: null,
          degree: 'Bachelors' as const,
          programme: 'ICT' as const,
          about: null,
          club_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          username_last_updated: new Date().toISOString(),
          deleted: false,
          deleted_at: null,
          username: null,
        },
      };

      // Act & Assert
      await expect(
        service.addStudentProfile(studentProfileInput, mockUser),
      ).rejects.toThrow(ConflictException);
    });

    it('should throw an error if username already exists', async () => {
      // Arrange
      const studentProfileInput: StudentProfileParams = {
        first_name: 'John',
        last_name: 'Doe',
        country_id: 'country-1',
        institution_id: 'institution-1',
        degree: 'Bachelors',
        programme: 'ICT',
        enrollment_date: 2020,
        graduation_date: 2024,
        username: 'johndoe123',
      };

      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'student' as const,
        state: 'active' as const,
        profile_pic_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted: false,
        deleted_at: null,
        student_profile: undefined,
      };

      // Mock username check
      const mockExistingProfile = {
        id: 'profile-2',
        username: 'johndoe123',
      };

      const mockFindFirst = jest.fn().mockImplementation(() => ({
        execute: jest.fn().mockResolvedValue(mockExistingProfile),
      }));

      // Replace the findFirst implementation for this test
      drizzleService.db.query.student_profiles.findFirst = mockFindFirst;

      // Act & Assert
      await expect(
        service.addStudentProfile(studentProfileInput, mockUser),
      ).rejects.toThrow(ConflictException);

      expect(
        drizzleService.db.query.student_profiles.findFirst,
      ).toHaveBeenCalled();
    });
  });

  describe('isProfileValid', () => {
    it('should return false for null or undefined profile', () => {
      expect(service['isProfileValid'](null)).toBe(false);
      expect(service['isProfileValid'](undefined)).toBe(false);
    });

    it('should return false for non-object profile', () => {
      expect(service['isProfileValid']('not an object')).toBe(false);
    });

    it('should return false if any value is null or undefined', () => {
      const profile = {
        id: 'profile-1',
        first_name: 'John',
        last_name: null,
      };
      expect(service['isProfileValid'](profile)).toBe(false);
    });

    it('should return true if all values are valid', () => {
      const profile = {
        id: 'profile-1',
        first_name: 'John',
        last_name: 'Doe',
      };
      expect(service['isProfileValid'](profile)).toBe(true);
    });
  });
});
