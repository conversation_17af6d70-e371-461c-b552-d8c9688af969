import { createZodDto } from 'nestjs-zod';
import {
  insertStudentProfileSchema,
  studentDegrees,
  studentProgrammes,
} from '@/db/schema/student_profile';
import { z } from 'nestjs-zod/z';
import { querySchema } from '@/common/dto/query-params.dto';

// Define the current year
const currentYear = new Date().getFullYear();

const studentProfileSortKeys = {
  id: 'id',
  first_name: 'first_name',
  last_name: 'last_name',
  institution: 'institution',
  level: 'level',
  programme: 'programme',
  degree: 'degree',
} as const;

// Extend the schema to include the optional profile_image field
export const extendedStudentProfileSchema = insertStudentProfileSchema
  .extend({
    degree: z.enum(studentDegrees),
    programme: z.enum(studentProgrammes),
    enrollment_date: z
      .number()
      .int()
      .gte(currentYear - 10),
    graduation_date: z
      .number()
      .int()
      .lte(currentYear + 10),
    profile_pic_url: z.string().url().optional(),
    username: z
      .string()
      .min(3, 'Username must be at least 3 characters long')
      .max(20, 'Username must be at most 20 characters long')
      .refine((val) => /^[a-z0-9_]+$/.test(val), {
        message:
          'Username can only contain lowercase letters, numbers, and underscores',
      })
      .optional(),
  })
  .refine(
    ({ graduation_date, enrollment_date }) => graduation_date > enrollment_date,
    {
      message: 'Graduation date must be greater than enrollment date',
    },
  );

// Create DTO using the extended schema
export class StudentProfileDto extends createZodDto(
  extendedStudentProfileSchema,
) {}

export const studentProfileQueryParamsSchema = querySchema.extend({
  sort: z.nativeEnum(studentProfileSortKeys).default('id'),
  programme: z.enum(studentProgrammes).optional(),
  degree: z.enum(studentDegrees).optional(),
  level: z.coerce.number().min(1).max(6).optional(),
  institution_id: z.string().uuid('Invalid institution ID').optional(),
  username: z.string().optional(),
});
export const StudentProfileCount = querySchema.extend({
  institutionId: z.string().optional(),
  programme: z.string().optional(),
  level: z.string().optional(),
  degree: z.string().optional(),
});
export type StudentProfileCountQueryParam = z.infer<typeof StudentProfileCount>;
export type StudentProfileParams = z.infer<typeof extendedStudentProfileSchema>;
export class StudentProfileQueryParamsDto extends createZodDto(
  studentProfileQueryParamsSchema,
) {}
