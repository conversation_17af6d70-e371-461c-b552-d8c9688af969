import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Pool } from 'pg';
import { readFileSync } from 'fs';
import { join } from 'path';
import 'dotenv/config';
import { Logger } from '@nestjs/common';

const logger = new Logger('LeaderboardScript');

export async function runLeaderboardScripts() {
  // Create the database pool using DATABASE_URL from environment variables
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    max: 1,
  });

  try {
    const drizzle = new DrizzleService(pool);

    const sqlFilePath = join(
      process.cwd(),
      'src/db/raw-query/leader_board.sql',
    );
    const sql = readFileSync(sqlFilePath, 'utf8');

    await drizzle.db.execute(sql);
    logger.log('Leaderboard views created successfully.');

    const views = [
      'leaderboard_day',
      'leaderboard_week',
      'leaderboard_month',
      'leaderboard_current_quarter',
      'leaderboard_first_quarter',
      'leaderboard_second_quarter',
      'leaderboard_third_quarter',
      'leaderboard_fourth_quarter',
      'leaderboard_year',
      'leaderboard_all_time',
    ];

    for (const view of views) {
      await drizzle.db.execute(`REFRESH MATERIALIZED VIEW ${view}`);
    }
  } catch (error: any) {
    logger.error('Failed to create leaderboard views', error.message);
  } finally {
    await pool.end();
  }
}
