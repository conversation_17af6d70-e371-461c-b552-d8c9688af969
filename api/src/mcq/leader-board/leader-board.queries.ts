export const getStudentIdQuery = (userId: string) => `
  SELECT id FROM public.student_profiles WHERE user_id='${userId}'
`;

export const getRankQuery = (viewName: string, studentId: string) => `
  SELECT * FROM ${viewName} WHERE student_id = '${studentId}' AND first_quiz_date IS NOT NULL
`;

export const getLeaderBoardQuery = (
  viewName: string,
  search?: string,
  limit?: number,
  offset?: number,
  sortKey: 'ASC' | 'DESC' = 'ASC',
) => `
  SELECT
    student_id,
    profile_pic_url,
    first_name,
    last_name,
    total_score,
    rank,
    first_quiz_date
 FROM
    ${viewName}
  WHERE 
    total_score > 0 
    AND first_quiz_date IS NOT NULL
    ${search ? `AND (first_name ILIKE '%${search}%' OR last_name ILIKE '%${search}%')` : ''}
  ORDER BY rank ${sortKey} LIMIT ${limit} OFFSET ${offset}
`;

export const getCountQuery = (viewName: string, search?: string) => `
  SELECT COUNT(*) as total_count
  FROM ${viewName}
  WHERE 
    total_score > 0 
    AND first_quiz_date IS NOT NULL
    ${search ? `AND (first_name ILIKE '%${search}%' OR last_name ILIKE '%${search}%')` : ''}
`;

export const getLeaderBoardFilterPeriodWebQuery = (
  viewName: string,
  filters: string[],
  limit: number,
  offset: number,
  sortKey: 'ASC' | 'DESC',
) => `
  SELECT
    student_id,
    profile_pic_url,
    first_name,
    last_name,
    total_score,
    degree,
    programme,
    rank,
    institution_name,
    graduation_date - enrollment_date AS level,
    first_quiz_date
  FROM
    ${viewName}
  WHERE 
    total_score > 0 
    AND first_quiz_date IS NOT NULL
    ${filters.length > 0 ? `AND ${filters.join(' AND ')}` : ''}
  ORDER BY rank ${sortKey} LIMIT ${limit} OFFSET ${offset}
`;

export const getCountFilterPeriodWebQuery = (
  viewName: string,
  filters: string[],
) => `
  SELECT COUNT(*) as total_count
  FROM ${viewName}
  WHERE 
    total_score > 0 
    AND first_quiz_date IS NOT NULL
    ${filters.length > 0 ? `AND ${filters.join(' AND ')}` : ''}
`;

export const getLeaderBoardByRankQuery = (viewName: string, rank: number) =>
  `SELECT student_id, profile_pic_url,first_name,last_name,total_score,rank,first_quiz_date FROM ${viewName} WHERE rank = ${rank} AND first_quiz_date IS NOT NULL`;
