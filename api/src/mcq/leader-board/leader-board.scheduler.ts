import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LeaderBoardService } from './leader-board.service';

@Injectable()
export class LeaderBoardScheduler {
  constructor(private leaderBoardService: LeaderBoardService) {}
  private readonly logger = new Logger(LeaderBoardScheduler.name);

  // Run at exact 5-minute intervals (0 minutes past the hour)
  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleLeaderBoard() {
    const startTime = Date.now();

    try {
      this.logger.log('Starting leaderboard refresh process');
      await this.leaderBoardService.refreshMaterializedViews();

      const duration = Date.now() - startTime;
      this.logger.log(
        `Leaderboard refresh process completed successfully in ${duration}ms`,
      );
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error('Failed to refresh leaderboard views', {
        message: error.message,
        stack: error.stack,
        duration,
        timestamp: new Date().toISOString(),
      });

      // Don't throw in production to prevent scheduler crashes
      if (process.env.NODE_ENV !== 'production') {
        throw error;
      }
    }
  }
}
