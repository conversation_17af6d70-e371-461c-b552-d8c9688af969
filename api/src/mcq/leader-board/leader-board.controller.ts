import {
  Controller,
  Get,
  UseGuards,
  InternalServerErrorException,
  Query,
  Param,
  BadRequestException,
} from '@nestjs/common';
import { LeaderBoardService } from './leader-board.service';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { type User as IUser } from '@/db/schema';
import { PeriodEnumType } from './leader-board.types';
import { periodEnum } from '@/common/dto/query-params.dto';
import * as leaderBoardDto from '../dto/leader-board.dto';
import { ZodValidationPipe } from 'nestjs-zod';
import { User } from '@/guards/user.decorator';
import { MCQ_LeaderBoardRoutes } from '@app/shared/constants/mcq.constants';

@ApiTags('LeaderBoard')
@Controller({ version: '1', path: 'leader-board' })
export class LeaderBoardController {
  constructor(private readonly leaderBoardService: LeaderBoardService) {}

  @Get(MCQ_LeaderBoardRoutes.GET_STUDENT_RANK)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'leader-board', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get student rank',
    description: 'Get the current rank of the authenticated student',
  })
  @ApiQuery({
    name: 'period',
    required: true,
    description: 'Time period for the ranking (daily, weekly, monthly, etc.)',
    enum: PeriodEnumType,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved student rank',
    type: leaderBoardDto.CachedUserRankDto,
  })
  @ApiBadRequestResponse({
    description: 'User does not have a student profile',
  })
  @ApiInternalServerErrorResponse({
    description: 'Failed to retrieve student rank',
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async getStudentRank(
    @Query(new ZodValidationPipe(leaderBoardDto.queryParamsLeaderBoardDto))
    query: leaderBoardDto.queryParamsLeaderBoardDto,
    @User() user: IUser,
  ) {
    const { period } = query;
    if (!user.student_profile) {
      throw new BadRequestException('User does not have a student profile');
    }
    try {
      return await this.leaderBoardService.getStudentRank(
        user.student_profile.id,
        period as PeriodEnumType,
      );
    } catch (error) {
      throw new InternalServerErrorException('Failed to retrieve student rank');
    }
  }

  @Get()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'leader-board', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get leaderboard',
    description: 'Get the leaderboard rankings for a specific time period',
  })
  @ApiQuery({
    name: 'period',
    required: true,
    description: 'Time period for the ranking (daily, weekly, monthly, etc.)',
    enum: PeriodEnumType,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term to filter rankings by student name',
    type: String,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved leaderboard',
    schema: {
      type: 'object',
      properties: {
        data: { type: 'array' },
        total: { type: 'number' },
        currentUser: {
          type: 'object',
          nullable: true,
        },
        userPosition: {
          type: 'number',
          nullable: true,
        },
      },
    },
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async getLeaderBoard(
    @Query(new ZodValidationPipe(leaderBoardDto.queryParamsLeaderBoardDto))
    query: leaderBoardDto.queryParamsLeaderBoardDto,
    @User() user: IUser,
  ) {
    const { period, page, limit, search } = query;

    return this.leaderBoardService.getLeaderBoardByFilterPeriod(
      period as PeriodEnumType,
      page,
      limit,
      user!.student_profile!.id,
      search,
    );
  }

  @Get(MCQ_LeaderBoardRoutes.GET_LEADERBOARD_BY_RANK)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get leaderboard by rank',
    description: 'Get student information at a specific rank position',
  })
  @ApiParam({
    name: 'rank',
    description: 'The rank position to retrieve',
    type: Number,
    required: true,
  })
  @ApiQuery({
    name: 'period',
    required: true,
    description: 'Time period for the ranking',
    enum: PeriodEnumType,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved student at the specified rank',
  })
  @ApiBadRequestResponse({
    description: 'Failed to get leaderboard by rank',
  })
  async getLeaderBoardByRank(
    @Param('rank') rank: number,
    @Query('period', new ZodValidationPipe(periodEnum)) period: PeriodEnumType,
  ) {
    return this.leaderBoardService.getLeaderBoardByRank(
      rank,
      period as PeriodEnumType,
    );
  }

  @Get(MCQ_LeaderBoardRoutes.REFRESH)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'leader-board', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Refresh leaderboard',
    description:
      'Refresh the leaderboard materialized views if data has changed',
  })
  @ApiOkResponse({
    description: 'Leaderboard refresh process completed successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Leaderboard refresh process completed successfully',
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Failed to refresh leaderboard',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async refreshLeaderBoardScore() {
    try {
      await this.leaderBoardService.refreshMaterializedViews();
      return { message: 'Leaderboard refresh process completed successfully' };
    } catch (error) {
      throw error;
    }
  }

  @Get(MCQ_LeaderBoardRoutes.FORCE_REFRESH)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'leader-board', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Force refresh leaderboard',
    description:
      'Force refresh all leaderboard materialized views regardless of data changes',
  })
  @ApiOkResponse({
    description: 'Leaderboard force refresh completed successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Leaderboard force refresh completed successfully',
        },
        duration: {
          type: 'string',
          example: '1234ms',
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Failed to force refresh leaderboard',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async forceRefreshLeaderBoardScore() {
    try {
      const startTime = Date.now();
      await this.leaderBoardService.forceRefreshMaterializedViews();
      const duration = Date.now() - startTime;
      return {
        message: 'Leaderboard force refresh completed successfully',
        duration: `${duration}ms`,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get(MCQ_LeaderBoardRoutes.GET_FOR_WEB)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'leader-board', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get leaderboard for web',
    description:
      'Get the leaderboard with advanced filtering options for web interface',
  })
  @ApiQuery({
    name: 'period',
    required: true,
    description: 'Time period for the ranking',
    enum: PeriodEnumType,
  })
  @ApiQuery({
    name: 'degree',
    required: false,
    description: 'Filter by degree',
    type: String,
  })
  @ApiQuery({
    name: 'institutionId',
    required: false,
    description: 'Filter by institution ID',
    type: String,
  })
  @ApiQuery({
    name: 'program',
    required: false,
    description: 'Filter by program',
    type: String,
  })
  @ApiQuery({
    name: 'level',
    required: false,
    description: 'Filter by level',
    type: String,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term to filter rankings by student name',
    type: String,
  })
  @CLIENT_TYPE(AppClients.WEB)
  async getLeaderBoardWeb(
    @Query(new ZodValidationPipe(leaderBoardDto.queryParamsLeaderBoardDto))
    query: leaderBoardDto.queryParamsLeaderBoardDto,
    @User() user: IUser,
  ) {
    const {
      period,
      degree,
      institutionId,
      program,
      level,
      limit,
      page,
      search,
    } = query;

    try {
      return this.leaderBoardService.getLeaderBoardFilterPeriodWeb(
        period as PeriodEnumType,
        page,
        limit,
        search,
        level,
        institutionId,
        degree,
        program,
        user.id,
      );
    } catch (error) {
      throw error;
    }
  }
}
