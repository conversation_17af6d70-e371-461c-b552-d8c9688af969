import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import {
  CachedLeaderboard,
  CachedUserRank,
  CurrentUserData,
} from '../dto/leader-board.dto';
import {
  getCountFilterPeriodWebQuery,
  getCountQuery,
  getLeaderBoardByRankQuery,
  getLeaderBoardFilterPeriodWebQuery,
  getLeaderBoardQuery,
  getRankQuery,
} from './leader-board.queries';

import { CacheService } from '@app/shared/redis/cache.service';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';
import { PeriodEnumType } from './leader-board.types';

@Injectable()
export class LeaderBoardService {
  private readonly LEADERBOARD_TTL = CACHE_TTL.ONE_DAY;
  private readonly logger = new Logger(LeaderBoardService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
  ) {}

  private async fetchLeaderboardFromDB(
    period: PeriodEnumType,
    page: number,
    limit: number,
    studentId: string,
    search?: string,
  ) {
    const viewName = this.getViewNameByPeriod(period);
    const offset = (page - 1) * limit;

    const query = getLeaderBoardQuery(viewName, search, limit, offset);
    const countQuery = getCountQuery(viewName, search);

    try {
      const [countResult, currentUser, result] = await Promise.all([
        this.drizzle.db.execute(countQuery),
        this.getStudentRank(studentId, period),
        this.drizzle.db.execute(query),
      ]);

      const total = parseInt(
        countResult.rows[0]?.total_count?.toString() || '0',
        10,
      );

      const data = result.rows;
      let currentUserData: CurrentUserData | null = null;

      if (currentUser.length > 0 && currentUser[0]) {
        currentUserData = {
          student_id: currentUser[0].student_id as string,
          profile_pic_url: currentUser[0].profile_pic_url as string,
          first_name: currentUser[0].first_name as string,
          last_name: currentUser[0].last_name as string,
          total_score: currentUser[0].total_score as number,
          rank: currentUser[0].rank as number,
          isCurrentUser: true,
        };

        const existingIndex = data.findIndex(
          (item) => item.student_id === currentUserData!.student_id,
        );

        if (existingIndex !== -1 && data[existingIndex]) {
          data[existingIndex].isCurrentUser = true;
        }
      }

      return {
        data,
        total,
        currentUser: currentUserData,
        userPosition: currentUserData ? currentUserData.rank : null,
      };
    } catch (error: any) {
      throw new BadRequestException(`Database query failed: ${error.message}`);
    }
  }

  async getLeaderBoardByFilterPeriod(
    period: PeriodEnumType,
    page: number,
    limit: number,
    studentId: string,
    search?: string,
  ) {
    try {
      if (search) {
        return this.fetchLeaderboardFromDB(
          period,
          page,
          limit,
          studentId,
          search,
        );
      }

      const viewName = this.getViewNameByPeriod(period);
      const cacheKeys = {
        leaderboard: this.cacheService.generateKey(
          [viewName, `page${page}`, `limit${limit}`],
          CACHE_PREFIXES.LEADERBOARD,
        ),
        userRank: this.cacheService.generateKey(
          [viewName, 'user', studentId],
          CACHE_PREFIXES.LEADERBOARD,
        ),
      };

      try {
        const [cachedLeaderboard, cachedUserRank] = await Promise.all([
          this.cacheService.get(
            cacheKeys.leaderboard,
          ) as Promise<CachedLeaderboard>,
          this.cacheService.get(cacheKeys.userRank) as Promise<CachedUserRank>,
        ]);

        if (
          cachedLeaderboard?.data &&
          cachedUserRank?.currentUser &&
          this.isCacheValid(cachedLeaderboard.timestamp)
        ) {
          this.logger.debug(`Cache hit for ${viewName} leaderboard`);
          return {
            data: cachedLeaderboard.data,
            total: cachedLeaderboard.total,
            currentUser: cachedUserRank.currentUser,
            userPosition: cachedUserRank.rank,
          };
        }
      } catch (cacheError: any) {
        this.logger.warn(`Cache error for ${viewName}: ${cacheError.message}`);
      }

      const result = await this.fetchLeaderboardFromDB(
        period,
        page,
        limit,
        studentId,
        search,
      );

      if (!search) {
        try {
          const timestamp = Date.now();
          await Promise.all([
            this.cacheService.set(
              cacheKeys.leaderboard,
              {
                data: result.data,
                total: result.total,
                timestamp,
              },
              this.LEADERBOARD_TTL,
            ),
            this.cacheService.set(
              cacheKeys.userRank,
              {
                currentUser: result.currentUser,
                rank: result.userPosition,
                timestamp,
              },
              this.LEADERBOARD_TTL,
            ),
          ]);
        } catch (cacheError: any) {
          this.logger.warn(
            `Failed to cache ${viewName}: ${cacheError.message}`,
          );
        }
      }

      return result;
    } catch (error: any) {
      throw new BadRequestException(
        `Failed to get ${period} leaderboard: ${error.message}`,
      );
    }
  }

  async getStudentRank(studentId: string, period: PeriodEnumType) {
    try {
      const cacheKey = this.cacheService.generateKey(
        ['rank', studentId, period],
        CACHE_PREFIXES.LEADERBOARD,
      );

      // Try to get from cache first
      const cachedRank = await this.cacheService.get(cacheKey);
      if (cachedRank) {
        this.logger.debug(`Cache hit for student rank: ${studentId}`);
        return cachedRank;
      }

      const viewName = this.getViewNameByPeriod(period);

      // Fetch the rank based on the student_id
      const rankQuery = getRankQuery(viewName, studentId);
      const rankResult = await this.drizzle.db.execute(rankQuery);

      // Cache the result using the constant TTL
      await this.cacheService.set(cacheKey, rankResult.rows, CACHE_TTL.RANK);

      return rankResult.rows;
    } catch (error: any) {
      throw new BadRequestException(
        `Failed to get student rank: ${error.message}`,
      );
    }
  }

  /**
   * Checks if there have been any changes to quiz scores or points logs
   * since the last materialized view refresh
   * @returns boolean indicating if data has changed
   */
  private async hasDataChangedSinceLastRefresh(): Promise<boolean> {
    try {
      // First check if the database connection is healthy
      try {
        await this.drizzle.db.execute('SELECT 1');
      } catch (connectionError: any) {
        this.logger.error(
          'Database connection error in hasDataChangedSinceLastRefresh:',
          {
            message: connectionError.message,
            stack: connectionError.stack,
          },
        );
        return true;
      }

      // Get the last update time from quiz_score and points_logs tables
      const lastUpdateQuery = `
        SELECT MAX(updated_at) as last_update
        FROM (
          SELECT MAX(updated_at) as updated_at FROM quiz_score
          UNION ALL
          SELECT MAX(updated_at) as updated_at FROM points_logs
        ) as updates
      `;

      // Get the last refresh time of a representative materialized view
      // Using pg_stat_user_tables to get the last_vacuum time which is updated on REFRESH
      const lastRefreshQuery = `
        SELECT last_vacuum as last_refresh
        FROM pg_stat_user_tables
        WHERE relname = 'leaderboard_day'
      `;

      const [updateResult, refreshResult] = await Promise.all([
        this.drizzle.db.execute(lastUpdateQuery),
        this.drizzle.db.execute(lastRefreshQuery),
      ]);

      const lastUpdate = (updateResult.rows[0]?.last_update as string) || null;
      const lastRefresh =
        (refreshResult.rows[0]?.last_refresh as string) || null;

      // If we don't have refresh data, assume we need to refresh
      if (!lastRefresh) {
        this.logger.debug('No last refresh time found, will refresh views');
        return true;
      }

      // If we don't have update data, assume no changes
      if (!lastUpdate) {
        this.logger.debug('No data updates found, skipping refresh');
        return false;
      }

      const lastUpdateTime = new Date(lastUpdate);
      const lastRefreshTime = new Date(lastRefresh);

      // Check if data has been updated since last refresh
      const hasChanged = lastUpdateTime > lastRefreshTime;

      this.logger.debug(
        `Data check: Last update: ${lastUpdateTime.toISOString()}, Last refresh: ${lastRefreshTime.toISOString()}, Has changed: ${hasChanged}`,
      );

      return hasChanged;
    } catch (error: any) {
      this.logger.error('Error checking for data changes:', {
        message: error.message,
        stack: error.stack,
      });
      return true; // Assume data has changed if check fails
    }
  }

  /**
   * Refreshes all materialized views for leaderboards
   * Only refreshes if data has changed since last refresh
   */
  async refreshMaterializedViews(): Promise<void> {
    try {
      // First check if the database connection is healthy
      try {
        await this.drizzle.db.execute('SELECT 1');
      } catch (connectionError: any) {
        this.logger.error(
          'Database connection error in refreshMaterializedViews:',
          {
            message: connectionError.message,
            stack: connectionError.stack,
          },
        );
        return;
      }

      // Check if data has changed since last refresh
      const hasDataChanged = await this.hasDataChangedSinceLastRefresh();

      if (!hasDataChanged) {
        this.logger.debug(
          'Skipping materialized view refresh - no new data detected',
        );
        return;
      }

      this.logger.debug('Data changes detected, refreshing materialized views');

      // Perform the actual refresh
      await this.forceRefreshMaterializedViews();
    } catch (error: any) {
      this.logger.error('Failed to refresh materialized views:', {
        message: error.message,
        stack: error.stack,
      });
      // Don't throw in production to prevent scheduler crashes
      if (process.env.NODE_ENV !== 'production') {
        throw error;
      }
    }
  }

  /**
   * Forces a refresh of all materialized views regardless of data changes
   * Useful for manual refreshes or when we need to ensure views are up-to-date
   */
  async forceRefreshMaterializedViews(): Promise<void> {
    try {
      // First check if the database connection is healthy
      try {
        await this.drizzle.db.execute('SELECT 1');
      } catch (connectionError: any) {
        this.logger.error(
          'Database connection error in forceRefreshMaterializedViews:',
          {
            message: connectionError.message,
            stack: connectionError.stack,
          },
        );
        // Throw error since this is a forced refresh
        throw connectionError;
      }

      const views = [
        'leaderboard_day',
        'leaderboard_week',
        'leaderboard_month',
        'leaderboard_current_quarter',
        'leaderboard_first_quarter',
        'leaderboard_second_quarter',
        'leaderboard_third_quarter',
        'leaderboard_fourth_quarter',
        'leaderboard_year',
        'leaderboard_all_time',
      ];

      // Use individual transactions for each view to prevent cascading failures
      const refreshResults = [];

      for (const view of views) {
        try {
          await this.drizzle.db.transaction(async (tx) => {
            // Set a statement timeout for the refresh operation
            await tx.execute('SET statement_timeout = 30000'); // 30 seconds

            this.logger.debug(`Starting refresh of materialized view: ${view}`);
            const startTime = Date.now();

            await tx.execute(`REFRESH MATERIALIZED VIEW ${view}`);

            const duration = Date.now() - startTime;
            this.logger.debug(
              `Successfully refreshed ${view} in ${duration}ms`,
            );

            // Clear cache for this specific view
            await this.clearViewCache(view);

            refreshResults.push({ view, status: 'success', duration });
          });
        } catch (viewError: any) {
          // Log detailed error but continue with other views
          this.logger.error(`Failed to refresh view ${view}:`, {
            message: viewError.message,
            stack: viewError.stack,
            view,
            timestamp: new Date().toISOString(),
          });

          refreshResults.push({
            view,
            status: 'failed',
            error: viewError.message,
          });

          // If this is the critical leaderboard_week view, try once more
          if (view === 'leaderboard_week') {
            try {
              this.logger.warn(
                'Retrying leaderboard_week refresh after failure',
              );
              await this.drizzle.db.execute(
                `REFRESH MATERIALIZED VIEW ${view}`,
              );
              this.logger.log(
                'Successfully refreshed leaderboard_week on retry',
              );

              // Safely update the last result if it exists
              const lastResultIndex = refreshResults.length - 1;
              if (lastResultIndex >= 0 && refreshResults[lastResultIndex]) {
                refreshResults[lastResultIndex].status = 'success_retry';
              }
            } catch (retryError: any) {
              this.logger.error('Retry failed for leaderboard_week:', {
                message: retryError.message,
                stack: retryError.stack,
              });
            }
          }
        }
      }

      // Log summary of refresh operations
      const successCount = refreshResults.filter((r) =>
        r.status.includes('success'),
      ).length;
      const failureCount = refreshResults.filter(
        (r) => r.status === 'failed',
      ).length;

      this.logger.log(
        `Materialized view refresh summary: ${successCount} successful, ${failureCount} failed`,
      );

      // Invalidate all leaderboard caches as a safety measure (outside transaction)
      try {
        await this.cacheService.del(`${CACHE_PREFIXES.LEADERBOARD}:*`);
      } catch (cacheError: any) {
        this.logger.warn('Failed to clear leaderboard cache:', {
          message: cacheError.message,
        });
      }

      this.logger.debug('Successfully refreshed all materialized views');
    } catch (error: any) {
      this.logger.error('Failed to force refresh materialized views:', {
        message: error.message,
        stack: error.stack,
      });
      // Always throw for forced refreshes
      throw error;
    }
  }

  async getLeaderBoardByRank(rank: number, period: PeriodEnumType) {
    try {
      const viewName = this.getViewNameByPeriod(period);
      const query = getLeaderBoardByRankQuery(viewName, rank);
      const result = await this.drizzle.db.execute(query);
      return result.rows;
    } catch (error: any) {
      throw new BadRequestException(
        `Failed to get leaderboard by rank: ${error.message}`,
      );
    }
  }

  async getLeaderBoardFilterPeriodWeb(
    period: PeriodEnumType,
    page: number,
    limit: number,
    search?: string,
    level?: string,
    institutionId?: string,
    degree?: string,
    programme?: string,
    _?: string,
    sortKey: 'ASC' | 'DESC' = 'ASC',
  ) {
    try {
      // Don't use cache for filtered/search requests
      if (search || level || institutionId || degree || programme) {
        return this.fetchLeaderboardFilterPeriodWeb(
          period,
          page,
          limit,
          search,
          level,
          institutionId,
          degree,
          programme,
          sortKey,
        );
      }

      const viewName = this.getViewNameByPeriod(period);
      const cacheKey = this.cacheService.generateKey(
        [viewName, 'web', `page${page}`, `limit${limit}`, `sort${sortKey}`],
        CACHE_PREFIXES.LEADERBOARD,
      );

      try {
        const cachedData = await this.cacheService.get(cacheKey);
        if (cachedData && this.isCacheValid(cachedData.timestamp)) {
          this.logger.debug(`Cache hit for ${viewName} web leaderboard`);
          return cachedData;
        }
      } catch (cacheError: any) {
        this.logger.warn(`Cache error for ${viewName}: ${cacheError.message}`);
      }

      const result = await this.fetchLeaderboardFilterPeriodWeb(
        period,
        page,
        limit,
        search,
        level,
        institutionId,
        degree,
        programme,
        sortKey,
      );

      try {
        await this.cacheService.set(
          cacheKey,
          {
            ...result,
            timestamp: Date.now(),
          },
          this.LEADERBOARD_TTL,
        );
      } catch (cacheError: any) {
        this.logger.warn(
          `Failed to cache ${viewName} web leaderboard: ${cacheError.message}`,
        );
      }

      return result;
    } catch (error: any) {
      throw new BadRequestException(
        `Failed to get leaderboard for period ${period}: ${error.message}`,
      );
    }
  }

  private async fetchLeaderboardFilterPeriodWeb(
    period: PeriodEnumType,
    page: number,
    limit: number,
    search?: string,
    level?: string,
    institutionId?: string,
    degree?: string,
    programme?: string,
    sortKey: 'ASC' | 'DESC' = 'ASC',
  ) {
    const offset = (page - 1) * limit;
    const viewName = this.getViewNameByPeriod(period);

    const filters = [];
    if (search) {
      filters.push(
        `(first_name ILIKE '%${search}%' OR last_name ILIKE '%${search}%')`,
      );
    }
    if (level) {
      filters.push(`(graduation_date - enrollment_date) = '${level}'`);
    }
    if (institutionId) {
      filters.push(`institution_id = '${institutionId}'`);
    }
    if (degree) {
      filters.push(`degree = '${degree}'`);
    }
    if (programme) {
      filters.push(`programme = '${programme}'`);
    }

    const query = getLeaderBoardFilterPeriodWebQuery(
      viewName,
      filters,
      limit,
      offset,
      sortKey,
    );
    const countQuery = getCountFilterPeriodWebQuery(viewName, filters);

    const countResult = await this.drizzle.db.execute(countQuery);
    const total = parseInt(
      countResult.rows[0]?.total_count?.toString() || '0',
      10,
    );

    const result = await this.drizzle.db.execute(query);
    const data = result.rows;

    return {
      data,
      total,
    };
  }

  private getViewNameByPeriod(period: PeriodEnumType): string {
    const viewNameMap: Record<PeriodEnumType, string> = {
      [PeriodEnumType.DAILY]: 'leaderboard_day',
      [PeriodEnumType.WEEKLY]: 'leaderboard_week',
      [PeriodEnumType.LAST_WEEK]: 'leaderboard_last_week',
      [PeriodEnumType.MONTHLY]: 'leaderboard_month',
      [PeriodEnumType.LAST_MONTH]: 'leaderboard_last_month',
      [PeriodEnumType.QUARTERLY]: 'leaderboard_current_quarter',
      [PeriodEnumType.CURRENT_QUARTER]: 'leaderboard_current_quarter',
      [PeriodEnumType.FIRST_QUARTER]: 'leaderboard_first_quarter',
      [PeriodEnumType.SECOND_QUARTER]: 'leaderboard_second_quarter',
      [PeriodEnumType.THIRD_QUARTER]: 'leaderboard_third_quarter',
      [PeriodEnumType.FOURTH_QUARTER]: 'leaderboard_fourth_quarter',
      [PeriodEnumType.YEARLY]: 'leaderboard_year',
      [PeriodEnumType.ALL_TIME]: 'leaderboard_all_time',
    };

    const viewName = viewNameMap[period];
    if (!viewName) {
      throw new Error(`Invalid period: ${period}`);
    }
    return viewName;
  }

  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.LEADERBOARD_TTL * 1000;
  }

  /**
   * Clears cache for a specific view
   * @param viewName Name of the view to clear cache for
   */
  private async clearViewCache(viewName: string) {
    try {
      const pattern = this.cacheService.generateKey(
        `${viewName}:*`,
        CACHE_PREFIXES.LEADERBOARD,
      );

      await this.cacheService.del(pattern);
      this.logger.debug(`Cleared cache for ${viewName}`);
    } catch (error: any) {
      this.logger.error(
        `Failed to clear cache for ${viewName}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Invalidates leaderboard cache for a specific user
   * @param userId The user ID to invalidate cache for
   */
  async invalidateUserCache(userId: string): Promise<void> {
    try {
      // Get all period types
      const periods = Object.values(PeriodEnumType);

      // Invalidate cache for each period
      for (const period of periods) {
        const viewName = this.getViewNameByPeriod(period);

        // Generate the user rank cache key
        const userRankKey = this.cacheService.generateKey(
          [viewName, 'user', userId],
          CACHE_PREFIXES.LEADERBOARD,
        );

        // Delete the user rank cache
        await this.cacheService.del(userRankKey);

        this.logger.debug(
          `Invalidated ${period} leaderboard cache for user ${userId}`,
        );
      }
    } catch (error: any) {
      this.logger.error(`Failed to invalidate user cache: ${error.message}`);
    }
  }
}
