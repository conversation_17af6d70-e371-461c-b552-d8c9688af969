import {
  Body,
  Controller,
  Delete,
  Get,
  InternalServerErrorException,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { UseRoles } from 'nest-access-control';
import {
  ApiCreatedResponse,
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiParam,
  ApiQuery,
  ApiHeader,
  ApiBody,
  ApiConsumes,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { QuestionBankService } from './question-banks.service';
import {
  createQuestionBankDto,
  updateQuestionBankDto,
} from '../dto/question-bank.dto';
import { User } from '@/guards/user.decorator';
import { QuestionBank, Questions, type User as IUser } from '@/db/schema';
import { CustomMaxFileSizeValidator } from '@/validators/custom-max-file-size.validator';
import { CustomFileUploadValidator } from '@/validators/custom-file-type.validator';
import {
  MCQ_QuestionBankRoutes,
  QuestionBankRoutes,
  Two_MB,
} from '@app/shared/constants/mcq.constants';
import { UploadService } from '@/upload/upload.service';
import { RoleGuard } from '@/guards/role.guard';
import { queryParamsDto } from '@/common/dto/query-params.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import {
  bulkUpdateQuestionsDto,
  questionQueryParamsDto,
} from '../dto/question.dto';
import { QuestionsService } from '../questions/questions.service';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@ApiTags('Question Bank')
@Controller({ version: '1', path: 'question-bank' })
export class QuestionBanksController {
  constructor(
    private readonly questionBankService: QuestionBankService,
    private readonly questionService: QuestionsService,
    private uploadService: UploadService,
  ) {}
  @Post()
  @ApiOperation({
    summary: 'Create question bank',
    description:
      'Create a new question bank with an image upload. Requires name, stack, framework, and image.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Question bank data with image upload',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          minLength: 2,
          example: 'JavaScript Fundamentals',
        },
        stack: { type: 'string', minLength: 2, example: 'Frontend' },
        framework: { type: 'string', minLength: 2, example: 'React' },
        image: {
          type: 'string',
          format: 'binary',
          description: 'Question bank image (max 2MB)',
        },
      },
      required: ['name', 'stack', 'framework', 'image'],
    },
  })
  @ApiCreatedResponse({
    description: 'Question bank created successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string', example: 'JavaScript Fundamentals' },
        stack: { type: 'string', example: 'Frontend' },
        framework: { type: 'string', example: 'React' },
        imageUrl: {
          type: 'string',
          format: 'url',
          example: 'https://s3.amazonaws.com/bucket/image.jpg',
        },
        createdBy: { type: 'string', format: 'uuid' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or file upload failed',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Invalid file type or size' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'question-bank',
    action: 'create',
    possession: 'any',
  })
  @UseInterceptors(FileInterceptor('image'))
  @ApiBearerAuth()
  async createQuestionBank(
    @User() user: IUser,
    @Body() data: createQuestionBankDto,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: true,
        validators: [
          new CustomMaxFileSizeValidator({
            maxSize: Two_MB,
          }),
          new CustomFileUploadValidator({ isRequired: true }),
        ],
      }),
    )
    image: Express.Multer.File,
  ) {
    const { imageUrl } = await this.uploadService.uploadFileToS3(image);
    if (!imageUrl) {
      throw new InternalServerErrorException('Image upload failed');
    }

    return this.questionBankService.createQuestionBank(data, imageUrl!, user);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all question banks',
    description:
      'Retrieve paginated list of question banks with optional filtering and sorting.',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term for question bank name',
    required: false,
    type: String,
    example: 'JavaScript',
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field',
    required: false,
    type: String,
    example: 'created_at',
  })
  @ApiQuery({
    name: 'order',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
    example: 'desc',
  })
  @ApiOkResponse({
    description: 'Question banks retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              name: { type: 'string', example: 'JavaScript Fundamentals' },
              stack: { type: 'string', example: 'Frontend' },
              framework: { type: 'string', example: 'React' },
              imageUrl: { type: 'string', format: 'url' },
              createdBy: { type: 'string', format: 'uuid' },
              created_at: { type: 'string', format: 'date-time' },
              updated_at: { type: 'string', format: 'date-time' },
              created_by: {
                type: 'object',
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  email: { type: 'string', format: 'email' },
                  name: { type: 'string' },
                },
              },
            },
          },
        },
        total: { type: 'number', example: 25 },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'question-bank',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuestionBanks(@Query() query: queryParamsDto) {
    return this.questionBankService.getQuestionBanks(
      query as queryParamsDto & {
        sort: keyof QuestionBank;
      },
    );
  }

  @Get(QuestionBankRoutes.GET_QUESTION_BANK_QUESTIONS)
  @ApiOperation({
    summary: 'Get questions for a question bank',
    description:
      'Retrieve paginated list of questions for a specific question bank.',
  })
  @ApiParam({
    name: 'questionBankId',
    description: 'Question Bank ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term for question text',
    required: false,
    type: String,
    example: 'JavaScript',
  })
  @ApiQuery({
    name: 'type',
    description: 'Filter by question type',
    required: false,
    enum: ['is_golden'],
    example: 'is_golden',
  })
  @ApiOkResponse({
    description: 'Questions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              question: { type: 'string' },
              option_a: { type: 'string' },
              option_b: { type: 'string' },
              option_c: { type: 'string' },
              option_d: { type: 'string' },
              answers: { type: 'string' },
              is_golden: { type: 'boolean' },
              questionBank_id: { type: 'string', format: 'uuid' },
              created_by: { type: 'string', format: 'uuid' },
              created_at: { type: 'string', format: 'date-time' },
              updated_at: { type: 'string', format: 'date-time' },
            },
          },
        },
        total: { type: 'number', example: 15 },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'question-bank',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuestionBankQuestions(
    @Param('questionBankId', new CustomParseUUIDPipe()) questionBankId: string,
    @Query() query: questionQueryParamsDto,
  ) {
    try {
      return await this.questionService.getQuestionsByBankId(
        questionBankId,
        query as questionQueryParamsDto & {
          sort: keyof Questions;
        },
      );
    } catch (error: any) {
      throw error;
    }
  }

  @Get(MCQ_QuestionBankRoutes.GET_BY_ID)
  @ApiOperation({
    summary: 'Get single question bank',
    description: 'Retrieve a specific question bank by its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'Question Bank ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Question bank retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string', example: 'JavaScript Fundamentals' },
        stack: { type: 'string', example: 'Frontend' },
        framework: { type: 'string', example: 'React' },
        imageUrl: { type: 'string', format: 'url' },
        createdBy: { type: 'string', format: 'uuid' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'question-bank',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuestionBank(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    // @User() user: IUser,
  ) {
    return this.questionBankService.getQuestionBank(id);
  }

  @Put(QuestionBankRoutes.UPDATE_QUESTION_BANK_QUESTIONS)
  @ApiOperation({
    summary: 'Bulk update questions in question bank',
    description:
      'Update multiple questions within a question bank. Can create new questions or update existing ones.',
  })
  @ApiParam({
    name: 'questionBankId',
    description: 'Question Bank ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiBody({
    description: 'Array of questions to update or create',
    schema: {
      type: 'object',
      properties: {
        questions: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                format: 'uuid',
                description:
                  'Optional - if provided, updates existing question',
              },
              question: {
                type: 'string',
                minLength: 3,
                maxLength: 255,
                example: 'What is JavaScript?',
              },
              option_a: {
                type: 'string',
                minLength: 1,
                maxLength: 255,
                example: 'A programming language',
              },
              option_b: {
                type: 'string',
                minLength: 1,
                maxLength: 255,
                example: 'A markup language',
              },
              option_c: {
                type: 'string',
                maxLength: 255,
                example: 'A database',
              },
              option_d: {
                type: 'string',
                maxLength: 255,
                example: 'An operating system',
              },
              answers: {
                type: 'array',
                items: { type: 'string' },
                example: ['A'],
              },
              is_golden: { type: 'boolean', example: false },
            },
            required: ['question', 'option_a', 'option_b', 'answers'],
          },
          minItems: 1,
        },
      },
      required: ['questions'],
    },
  })
  @ApiOkResponse({
    description: 'Questions updated successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Questions updated successfully' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid question data',
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'question-bank',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async updateQuestionBankQuestions(
    @Param('questionBankId', new CustomParseUUIDPipe()) questionBankId: string,
    @Body() data: bulkUpdateQuestionsDto,
    @User() user: IUser,
  ) {
    try {
      await this.questionService.bulkUpdateQuestions(
        questionBankId,
        data,
        user,
      );
      return { message: 'Questions updated successfully' };
    } catch (error: any) {
      throw error;
    }
  }

  @Put(MCQ_QuestionBankRoutes.UPDATE_BY_ID)
  @ApiOperation({
    summary: 'Update question bank',
    description:
      'Update an existing question bank. Optionally update the image.',
  })
  @ApiParam({
    name: 'id',
    description: 'Question Bank ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Updated question bank data with optional image',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 2, example: 'Advanced JavaScript' },
        stack: { type: 'string', minLength: 2, example: 'Full Stack' },
        framework: { type: 'string', minLength: 2, example: 'Node.js' },
        imageUrl: {
          type: 'string',
          format: 'url',
          description: 'Existing image URL if not uploading new image',
        },
        image: {
          type: 'string',
          format: 'binary',
          description: 'New image file (max 2MB) - optional',
        },
      },
    },
  })
  @ApiOkResponse({
    description: 'Question bank updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string', example: 'Advanced JavaScript' },
        stack: { type: 'string', example: 'Full Stack' },
        framework: { type: 'string', example: 'Node.js' },
        imageUrl: { type: 'string', format: 'url' },
        createdBy: { type: 'string', format: 'uuid' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or file upload failed',
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'question-bank',
    action: 'update',
    possession: 'any',
  })
  @UseInterceptors(FileInterceptor('image'))
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async updateQuestionBank(
    @Body() data: updateQuestionBankDto,
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: false,
        validators: [
          new CustomMaxFileSizeValidator({
            maxSize: Two_MB,
          }),
          new CustomFileUploadValidator({ isRequired: false }),
        ],
      }),
    )
    image: Express.Multer.File,
    @User() user: IUser,
  ) {
    let fileUrl;
    if (image) {
      const { imageUrl } = await this.uploadService.uploadFileToS3(image);
      fileUrl = imageUrl;
    }

    return this.questionBankService.updateQuestionBank(
      { ...data, imageUrl: fileUrl ?? data.imageUrl },
      id,
      user,
    );
  }

  @Delete(MCQ_QuestionBankRoutes.DELETE_BY_ID)
  @ApiOperation({
    summary: 'Delete question bank',
    description: 'Delete a question bank and all its associated questions.',
  })
  @ApiParam({
    name: 'id',
    description: 'Question Bank ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Question bank deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Question bank deleted successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'question-bank',
    action: 'delete',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async deleteQuestionBank(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User() user: IUser,
  ) {
    try {
      await this.questionBankService.deleteQuestionBank(id, user);
      return { message: 'Question bank deleted successfully' };
    } catch (error: any) {
      throw error;
    }
  }
}
