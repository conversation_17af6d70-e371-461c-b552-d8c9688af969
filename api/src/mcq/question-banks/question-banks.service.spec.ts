import { QuestionBankService } from './question-banks.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { McqRepository } from '../repository/mcq.repository';
import { RepositoryService } from '@/repositories/repository.service';
import {
  ConflictException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

// Create a simple mock for the Logger

// Mock the Logger constructor
jest.mock('@nestjs/common', () => {
  const original = jest.requireActual('@nestjs/common');
  return {
    ...original,
    Logger: jest.fn().mockImplementation(() => ({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    })),
  };
});
import {
  createQuestionBankDto,
  updateQuestionBankDto,
} from '../dto/question-bank.dto';
import { User, user_roles } from '@/db/schema';
import { MCQ_Messages } from '@app/shared/constants/mcq.constants';

describe('QuestionBankService', () => {
  let service: QuestionBankService;
  let mcqRepository: McqRepository;
  let repositoryService: RepositoryService;

  const mockDrizzleService = {
    db: {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      $count: jest.fn().mockResolvedValue(10),
    },
  };

  const mockMcqRepository = {
    createQuestionBank: jest.fn(),
    updateQuestionBank: jest.fn(),
    deleteQuestionBank: jest.fn(),
    getQuestionBankById: jest.fn(),
  };

  const mockRepositoryService = {
    getModelByKey: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Create the service directly with mocks
    service = new QuestionBankService(
      mockDrizzleService as unknown as DrizzleService,
      mockMcqRepository as unknown as McqRepository,
      mockRepositoryService as unknown as RepositoryService,
    );

    // Assign the mocks to the test variables
    mcqRepository = mockMcqRepository as unknown as McqRepository;
    repositoryService = mockRepositoryService as unknown as RepositoryService;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createQuestionBank', () => {
    it('should create a question bank successfully', async () => {
      // Arrange
      const createDto: createQuestionBankDto = {
        name: 'Test Bank',
        stack: 'JavaScript',
        framework: 'React',
      };
      const imageUrl = 'http://example.com/image.jpg';
      const user = { id: 'user-id' } as User;
      const expectedResult = [
        { id: 'bank-id', ...createDto, createdBy: user.id },
      ];

      mockMcqRepository.createQuestionBank.mockResolvedValue(expectedResult);

      // Act
      const result = await service.createQuestionBank(
        createDto,
        imageUrl,
        user,
      );

      // Assert
      expect(mcqRepository.createQuestionBank).toHaveBeenCalledWith({
        ...createDto,
        createdBy: user.id,
        imageUrl,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should throw an error if creation fails', async () => {
      // Arrange
      const createDto: createQuestionBankDto = {
        name: 'Test Bank',
        stack: 'JavaScript',
        framework: 'React',
      };
      const imageUrl = 'http://example.com/image.jpg';
      const user = { id: 'user-id' } as User;
      const error = new Error('Database error');

      mockMcqRepository.createQuestionBank.mockRejectedValue(error);

      // Act & Assert
      await expect(
        service.createQuestionBank(createDto, imageUrl, user),
      ).rejects.toThrow(error);
    });
  });

  describe('getQuestionBanks', () => {
    it('should return question banks with pagination', async () => {
      // Arrange
      const paginationParams = {
        sort: 'id' as const,
        order: 'asc',
        limit: 10,
        search: '',
        page: 1,
      };
      const mockQuestionBanks = [
        { id: 'bank-1', name: 'Bank 1' },
        { id: 'bank-2', name: 'Bank 2' },
      ];

      mockDrizzleService.db
        .select()
        .from()
        .where()
        .orderBy()
        .leftJoin()
        .leftJoin()
        .limit().offset = jest.fn().mockResolvedValue(mockQuestionBanks);

      // Act
      const result = await service.getQuestionBanks(paginationParams);

      // Assert
      expect(result).toEqual({
        data: mockQuestionBanks,
        total: 10,
      });
    });
  });

  describe('updateQuestionBank', () => {
    it('should update a question bank successfully', async () => {
      // Arrange
      const updateDto: updateQuestionBankDto = {
        name: 'Updated Bank',
        stack: 'JavaScript',
        framework: 'React',
      };
      const bankId = 'bank-id';
      const user = { id: 'user-id', role: user_roles.SUPER_ADMIN } as User;
      const existingBank = { id: bankId, createdBy: user.id };
      const updatedBank = [{ ...existingBank, ...updateDto }];

      mockRepositoryService.getModelByKey.mockResolvedValue(existingBank);
      mockDrizzleService.db.select().from().where = jest
        .fn()
        .mockResolvedValue([]);
      mockMcqRepository.updateQuestionBank.mockResolvedValue(updatedBank);

      // Act
      const result = await service.updateQuestionBank(updateDto, bankId, user);

      // Assert
      expect(repositoryService.getModelByKey).toHaveBeenCalledWith(
        expect.anything(),
        'id',
        bankId,
      );
      expect(mcqRepository.updateQuestionBank).toHaveBeenCalledWith(
        updateDto,
        bankId,
      );
      expect(result).toEqual(updatedBank);
    });

    it('should throw NotFoundException if question bank not found', async () => {
      // Arrange
      const updateDto: updateQuestionBankDto = { name: 'Updated Bank' };
      const bankId = 'non-existent-id';
      const user = { id: 'user-id' } as User;

      mockRepositoryService.getModelByKey.mockResolvedValue(null);

      // Act & Assert
      try {
        await service.updateQuestionBank(updateDto, bankId, user);
        // If we reach here, the test should fail
        fail('Expected NotFoundException was not thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        if (error instanceof NotFoundException) {
          expect(error.message).toBe('Question bank not found');
        }
      }
    });

    it('should throw ConflictException if question bank with same name exists', async () => {
      // Arrange
      const updateDto: updateQuestionBankDto = { name: 'Existing Bank' };
      const bankId = 'bank-id';
      const user = { id: 'user-id' } as User;
      const existingBank = { id: bankId, createdBy: user.id };

      mockRepositoryService.getModelByKey.mockResolvedValue(existingBank);
      mockDrizzleService.db.select().from().where = jest
        .fn()
        .mockResolvedValue([{ id: 'other-bank' }]);

      // Act & Assert
      try {
        await service.updateQuestionBank(updateDto, bankId, user);
        // If we reach here, the test should fail
        fail('Expected ConflictException was not thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(ConflictException);
        if (error instanceof ConflictException) {
          expect(error.message).toBe(MCQ_Messages.Question_Bank_Already_Exists);
        }
        expect(mcqRepository.updateQuestionBank).not.toHaveBeenCalled();
      }
    });

    it('should throw UnauthorizedException if user is not authorized', async () => {
      // Arrange
      const updateDto: updateQuestionBankDto = { name: 'Updated Bank' };
      const bankId = 'bank-id';
      const user = {
        id: 'different-user-id',
        role: user_roles.STUDENT,
      } as User;
      const existingBank = { id: bankId, createdBy: 'original-creator-id' };

      mockRepositoryService.getModelByKey.mockResolvedValue(existingBank);
      mockDrizzleService.db.select().from().where = jest
        .fn()
        .mockResolvedValue([]);

      // Act & Assert
      try {
        await service.updateQuestionBank(updateDto, bankId, user);
        // If we reach here, the test should fail
        fail('Expected UnauthorizedException was not thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(UnauthorizedException);
        if (error instanceof UnauthorizedException) {
          expect(error.message).toBe(
            'You are not authorized to update this question bank',
          );
        }
        expect(mcqRepository.updateQuestionBank).not.toHaveBeenCalled();
      }
    });
  });

  describe('deleteQuestionBank', () => {
    it('should delete a question bank successfully', async () => {
      // Arrange
      const bankId = 'bank-id';
      const user = { id: 'user-id', role: user_roles.SUPER_ADMIN } as User;
      const existingBank = { id: bankId, createdBy: user.id };

      mockRepositoryService.getModelByKey.mockResolvedValue(existingBank);
      mockMcqRepository.deleteQuestionBank.mockResolvedValue({
        message: 'Question bank deleted successfully',
        statusCode: 200,
      });

      // Act
      await service.deleteQuestionBank(bankId, user);

      // Assert
      expect(repositoryService.getModelByKey).toHaveBeenCalledWith(
        expect.anything(),
        'id',
        bankId,
      );
      expect(mcqRepository.deleteQuestionBank).toHaveBeenCalledWith(bankId);
    });

    it('should throw NotFoundException if question bank not found', async () => {
      // Arrange
      const bankId = 'non-existent-id';
      const user = { id: 'user-id' } as User;

      mockRepositoryService.getModelByKey.mockResolvedValue(null);

      // Act & Assert
      try {
        await service.deleteQuestionBank(bankId, user);
        // If we reach here, the test should fail
        fail('Expected NotFoundException was not thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        if (error instanceof NotFoundException) {
          expect(error.message).toBe('Question bank not found');
        }
        expect(mcqRepository.deleteQuestionBank).not.toHaveBeenCalled();
      }
    });

    it('should throw UnauthorizedException if user is not authorized', async () => {
      // Arrange
      const bankId = 'bank-id';
      const user = {
        id: 'different-user-id',
        role: user_roles.STUDENT,
      } as User;
      const existingBank = { id: bankId, createdBy: 'original-creator-id' };

      mockRepositoryService.getModelByKey.mockResolvedValue(existingBank);

      // Act & Assert
      try {
        await service.deleteQuestionBank(bankId, user);
        // If we reach here, the test should fail
        fail('Expected UnauthorizedException was not thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(UnauthorizedException);
        if (error instanceof UnauthorizedException) {
          expect(error.message).toBe(
            'You are not authorized to delete this question bank',
          );
        }
        expect(mcqRepository.deleteQuestionBank).not.toHaveBeenCalled();
      }
    });
  });
});
