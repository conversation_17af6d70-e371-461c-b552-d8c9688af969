import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  quizQueryParamsDto,
  QuizScoreT,
  QuizScore,
  UpdateQuizDto,
} from '../dto/quiz.dto';
import type { QuizParams } from '../dto/quiz.dto';
import { QuizRepository } from '../repository/quiz.repository';
import { QuizMessages } from '@app/shared/constants/quiz.constant';
import {
  questionBank,
  questionsSchema,
  Quiz,
  quizSchema,
  quizScoreSchema,
  student_profiles,
} from '@/db/schema';
import {
  and,
  eq,
  ilike,
  not,
  inArray,
  sql,
  gte,
  desc,
  or,
  getTableColumns,
} from 'drizzle-orm';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { periodQueryDto, queryParamsDto } from '@/common/dto/query-params.dto';
import { EnhancedNotificationService } from '@app/shared/enhanced-notification/enhanced-notification.service';
import { notification_types } from '@/db/schema/notification_system';
import { CacheService } from '@app/shared/cache/cache.service';
import { CacheConfigService } from '@app/shared/cache/cache-config.service';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { CacheInvalidate } from '@app/shared/cache/decorators/cache-invalidate.decorator';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';

@Injectable()
export class QuizService {
  private readonly logger = new Logger(QuizService.name);

  constructor(
    private readonly quizRepository: QuizRepository,
    private drizzle: DrizzleService,
    private readonly enhancedNotificationService: EnhancedNotificationService,
    private readonly cacheService: CacheService,
    private readonly cacheConfigService: CacheConfigService,
  ) {}

  /**
   * Creates a new quiz using the provided data.
   *
   * @param {QuizParams} data - The parameters required to create a quiz.
   * @returns {Promise<Quiz>} A promise that resolves to the created quiz.
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.QUIZ,
    keys: ['available'],
    afterExecution: true,
  })
  async createQuiz(data: QuizParams): Promise<any> {
    const now = new Date();
    const startTime = data.start_time ? new Date(data.start_time) : null;

    // Determine if quiz should be active immediately or scheduled
    const shouldActivateNow = startTime && startTime <= now;

    // Set appropriate status based on start time
    const quizData = {
      ...data,
      status: shouldActivateNow ? 'active' : 'inactive',
    };

    const quiz = await this.quizRepository.createQuiz(quizData);

    // Handle notifications based on timing
    if (quiz[0] && quiz[0].start_time) {
      try {
        // Get notification type for new quiz
        const notificationTypes = await this.drizzle.db
          .select()
          .from(notification_types)
          .where(eq(notification_types.code, 'new_quiz'));

        const quizNotificationType = notificationTypes[0];

        if (quizNotificationType) {
          if (shouldActivateNow) {
            // Send notification immediately for active quiz
            await this.enhancedNotificationService.sendNotificationToUsers({
              notificationTypeId: quizNotificationType.id,
              data: {
                quizId: quiz[0].id,
                title: `New Quiz: ${quiz[0].title}`,
                body: `A new quiz "${quiz[0].title}" is now available. Test your knowledge!`,
              },
              targetAudience: { roles: ['student', 'student_admin'] },
              channels: ['email', 'push'],
            });

            this.logger.log(
              `Quiz ${quiz[0].id} activated immediately and notification sent with channels: ${JSON.stringify(['email', 'push'])}`,
            );
          } else {
            // Schedule notification for future start time
            await this.enhancedNotificationService.scheduleNotification({
              notificationTypeId: quizNotificationType.id,
              title: `New Quiz: ${quiz[0].title}`,
              body: `A new quiz "${quiz[0].title}" is now available. Test your knowledge!`,
              data: { quizId: quiz[0].id },
              targetAudience: { roles: ['student', 'student_admin'] },
              scheduledFor: new Date(quiz[0].start_time),
              channels: ['email', 'push', 'in_app'],
              createdBy: data.created_by,
            });

            this.logger.log(
              `Quiz ${quiz[0].id} scheduled for ${quiz[0].start_time} with channels: ${JSON.stringify(['email', 'push', 'in_app'])}`,
            );
          }
        }
      } catch (error: any) {
        // Log error but don't fail quiz creation if notification scheduling fails
        this.logger.error('Failed to handle quiz notification:', error?.stack);
      }
    }

    return quiz;
  }

  /**
   * Retrieves all quizzes from the repository.
   *
   * @returns {Promise<any>} A promise that resolves to the list of all quizzes.
   * @throws {Error} If there is an error while fetching the quizzes.
   */
  async getAllQuiz(
    query: quizQueryParamsDto & {
      sort: keyof Quiz;
    },
    userId?: string,
  ): Promise<any> {
    const { search, status } = query;
    const whereConditions = [];
    if (search) {
      whereConditions.push(ilike(quizSchema.title, `%${search}%`));
    }
    if (status) {
      whereConditions.push(eq(quizSchema.status, status));
    }
    if (userId) {
      const studentProfiles = await this.drizzle.db
        .select({ student_id: student_profiles.id })
        .from(student_profiles)
        .where(eq(student_profiles.user_id, userId))
        .execute();
      const studentId = studentProfiles[0]?.student_id;
      if (!studentId) {
        throw new NotFoundException('Student ID not found for the given user');
      }
      const takenQuizScores: QuizScore[] = await this.drizzle.db
        .select()
        .from(quizScoreSchema)
        .where(eq(quizScoreSchema.user_id, studentId))
        .execute();
      const takenQuizIds = takenQuizScores.map((score) => score.quiz_id);
      // Check to exclude quizzes that have been taken by the student
      if (takenQuizIds.length > 0) {
        whereConditions.push(not(inArray(quizSchema.id, takenQuizIds)));
      }
    }
    const data = await this.quizRepository.getAllQuizzes({
      query,
      whereConditions,
    });
    const total = await this.drizzle.db.$count(
      quizSchema,
      and(...whereConditions),
    );
    return {
      data,
      total,
    };
  }

  async getQuizzesByQuestionBankId(
    questionBankId: string,
    query: quizQueryParamsDto & {
      sort: keyof Quiz;
    },
  ) {
    const { search, status } = query;
    const whereConditions = [eq(questionsSchema.question, questionBankId)];
    if (search) whereConditions.push(ilike(quizSchema.title, `%${search}%`));

    if (status) whereConditions.push(eq(quizSchema.status, status));

    const data = await this.quizRepository.getAllQuizzes({
      query,
      whereConditions,
    });

    const total = await this.drizzle.db.$count(quizSchema);
    return {
      data,
      total,
    };
  }

  @Cacheable({
    prefix: CACHE_PREFIXES.QUIZ,
    ttl: CACHE_TTL.ONE_DAY,
    keyGenerator: (args) => [args[0]],
    condition: (args) => !!args[0],
  })
  async getQuizById(id: string): Promise<any> {
    this.logger.debug(`Fetching quiz ${id} from database (not cache)`);
    const quiz = await this.quizRepository.getQuizById(id);
    if (!quiz) {
      throw new NotFoundException(`Quiz with ID ${id} not found`);
    }
    return quiz;
  }

  /**
   * Updates a quiz with the given data and ID.
   *
   * @param data - The data to update the quiz with.
   * @param id - The ID of the quiz to update.
   * @returns A promise that resolves to the updated quiz.
   * @throws {NotFoundException} If the quiz with the given ID is not found.
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.QUIZ,
    keys: (args) => ['available', args[0]],
    afterExecution: true,
  })
  async updateQuiz(data: UpdateQuizDto, id: string): Promise<any> {
    const updatedQuiz = await this.quizRepository.updateQuiz(data, id);
    if (!updatedQuiz) {
      throw new NotFoundException(`Quiz with ID ${id} not found`);
    }
    return updatedQuiz;
  }

  /**
   * Deletes a quiz by its ID.
   *
   * @param {string} id - The ID of the quiz to delete.
   * @returns {Promise<any>} A promise that resolves to the result of the deletion operation.
   * @throws {NotFoundException} If the quiz with the specified ID is not found.
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.QUIZ,
    keys: (args) => ['available', args[0]],
    afterExecution: true,
  })
  async deleteQuiz(id: string): Promise<any> {
    const result = await this.quizRepository.deleteQuiz(id);
    if (!result) {
      throw new NotFoundException(`Quiz with ID ${id} not found`);
    }
    return result;
  }
  /**
   * Retrieves the questions for a given quiz.
   *
   * @param {string} quizId - The unique identifier of the quiz.
   * @returns {Promise<QuestionDto>} A promise that resolves to the questions of the quiz.
   */
  async getQuizQuestions(quizId: string, userId: string): Promise<any[]> {
    return await this.quizRepository.getQuestionsForQuiz(quizId, userId);
  }
  /**
   * Retrieves the quiz score for a specific quiz and user.
   *
   * @param quizId - The unique identifier of the quiz.
   * @param userId - The unique identifier of the user.
   * @returns A promise that resolves to an array of QuizScoreT objects.
   */
  async getQuizScore(quizId: string, userId: string): Promise<QuizScoreT[]> {
    return await this.quizRepository.getQuizScore(quizId, userId);
  }

  async createQuizScore(data: QuizScore): Promise<QuizScore[]> {
    try {
      return await this.quizRepository.createQuizScore(data);
    } catch (error: any) {
      if (error instanceof NotFoundException) {
        throw new ConflictException(QuizMessages.QUIZ_SCORE_EXISTS);
      }
      if (error.message.includes('violates foreign key constraint')) {
        throw new BadRequestException(
          'The user ID provided does not exist in the student profiles.',
        );
      }
      throw error;
    }
  }

  async updateQuizScore(data: QuizScore): Promise<QuizScore[]> {
    return await this.quizRepository.updateQuizScore(data);
  }

  async getAllQuizScores(): Promise<QuizScore[]> {
    return await this.quizRepository.getAllQuizScores();
  }
  async getQuizByBankId(
    questionBankId: string,
    query: quizQueryParamsDto & {
      sort: keyof Quiz;
    },
  ) {
    const { status } = query;
    const whereConditions = [eq(quizSchema.question_bank_id, questionBankId)];
    if (status) whereConditions.push(eq(quizSchema.status, status));

    return await this.quizRepository.getAllQuizzes({
      whereConditions,
      query,
    });
  }
  async checkAndActivateQuiz(): Promise<any> {
    return await this.quizRepository.checkAndActivateQuiz();
  }
  async getScheduledQuizzes(): Promise<any> {
    return await this.quizRepository.getScheduledQuizzes();
  }

  @Cacheable({
    prefix: CACHE_PREFIXES.QUIZ,
    ttl: CACHE_TTL.FIVE_MINUTES,
    keyGenerator: () => ['available'],
  })
  async getAvailableQuizzes(): Promise<any> {
    return await this.quizRepository.getAvailableQuizzes();
  }

  @Cacheable({
    prefix: CACHE_PREFIXES.QUESTION_BANK,
    ttl: CACHE_TTL.ONE_DAY,
    keyGenerator: (args) => [`count:${args[0]}`],
    condition: (args) => !!args[0],
  })
  async questionCountByBankId(questionBankId: string): Promise<number> {
    return await this.quizRepository.questionCountByBankId(questionBankId);
  }

  async getStudentQuizActivities(
    studentId: string,
    { period }: periodQueryDto,
  ): Promise<any> {
    const filters = [];
    switch (period) {
      case 'daily':
        filters.push(
          gte(quizScoreSchema.updated_at, sql`NOW() - INTERVAL '1 DAY'`),
        );
        break;
      case 'weekly':
        filters.push(
          gte(quizScoreSchema.updated_at, sql`NOW() - INTERVAL '1 WEEK'`),
        );
        break;
      case 'monthly':
        filters.push(
          gte(quizScoreSchema.updated_at, sql`NOW() - INTERVAL '1 MONTH'`),
        );
        break;
      case 'yearly':
        filters.push(
          gte(quizScoreSchema.updated_at, sql`NOW() - INTERVAL '1 YEAR'`),
        );
        break;
    }
    const [data] = await this.drizzle.db
      .select({
        total_questions:
          sql`COALESCE(SUM(${quizSchema.total_questions}), 0)`.as(
            'total_questions',
          ),
        correct_answers: sql`COALESCE(SUM(${quizScoreSchema.score}), 0)`.as(
          'correct_answers',
        ),
        wrong_answers:
          sql`COALESCE(SUM(${quizSchema.total_questions}) - SUM(${quizScoreSchema.score}), 0)`.as(
            'wrong_answers',
          ),
      })
      .from(quizScoreSchema)
      .leftJoin(quizSchema, eq(quizSchema.id, quizScoreSchema.quiz_id))
      .where(and(eq(quizScoreSchema.user_id, studentId), ...filters));

    return data;
  }

  async getStudentQuizHistory(
    studentId: string,
    { search }: queryParamsDto,
  ): Promise<any> {
    const filters = [];
    if (search) {
      filters.push(
        or(
          ilike(student_profiles.first_name, `%${search}%`),
          ilike(student_profiles.last_name, `%${search}%`),
          ilike(student_profiles.other_name, `%${search}%`),
        ),
      );
    }

    const data = await this.drizzle.db
      .select({
        ...getTableColumns(quizScoreSchema),
        student_profile: {
          id: student_profiles.id,
        },
        quiz: {
          title: quizSchema.title,
          total_questions: quizSchema.total_questions,
          imageUrl: questionBank.imageUrl,
        },
      })
      .from(quizScoreSchema)
      .leftJoin(quizSchema, eq(quizSchema.id, quizScoreSchema.quiz_id))
      .leftJoin(
        student_profiles,
        eq(student_profiles.id, quizScoreSchema.user_id),
      )
      .leftJoin(questionBank, eq(quizSchema.question_bank_id, questionBank.id))
      .where(and(eq(quizScoreSchema.user_id, studentId), ...filters))
      .orderBy(desc(quizScoreSchema.updated_at));

    return data;
  }
}
