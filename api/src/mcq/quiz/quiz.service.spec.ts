import { Test, TestingModule } from '@nestjs/testing';
import { QuizService } from './quiz.service';
import { QuizRepository } from '../repository/quiz.repository';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { QuizScore, UpdateQuizDto } from '../dto/quiz.dto';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EnhancedNotificationService } from '@app/shared/enhanced-notification/enhanced-notification.service';
import { CacheService } from '@app/shared/cache/cache.service';
import { CacheConfigService } from '@app/shared/cache/cache-config.service';

describe('QuizService', () => {
  let service: QuizService;
  let repository: QuizRepository;

  beforeEach(async () => {
    const mockDrizzleService = {
      db: {
        $count: jest.fn().mockResolvedValue(2),
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      },
    };

    const mockRepository = {
      createQuiz: jest.fn(),
      getAllQuizzes: jest.fn(),
      getQuizById: jest.fn(),
      updateQuiz: jest.fn(),
      deleteQuiz: jest.fn(),
      getQuestionsForQuiz: jest.fn(),
      getQuizScore: jest.fn(),
      createQuizScore: jest.fn(),
      getAllQuizScores: jest.fn(),
      checkAndActivateQuiz: jest.fn(),
      getScheduledQuizzes: jest.fn(),
      getAvailableQuizzes: jest.fn(),
      questionCountByBankId: jest.fn(),
    };

    const mockEnhancedNotificationService = {
      scheduleNotification: jest.fn().mockResolvedValue({}),
      sendNotification: jest.fn().mockResolvedValue({}),
      sendBulkNotifications: jest.fn().mockResolvedValue({}),
      sendNotificationToUsers: jest.fn().mockResolvedValue('job-id-123'),
    };

    const mockCacheService = {
      get: jest.fn().mockImplementation((key, callback) => {
        // Always return null to force the callback to be executed
        return callback();
      }),
      set: jest.fn(),
      del: jest.fn(),
      reset: jest.fn(),
      generateKey: jest.fn().mockReturnValue('test-key'),
    };

    const mockCacheConfigService = {
      getCacheConfig: jest.fn().mockReturnValue({
        ttl: 300,
        prefix: 'test',
      }),
      isCacheEnabled: jest.fn().mockReturnValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuizService,
        {
          provide: QuizRepository,
          useValue: mockRepository,
        },
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: EnhancedNotificationService,
          useValue: mockEnhancedNotificationService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: CacheConfigService,
          useValue: mockCacheConfigService,
        },
      ],
    }).compile();

    service = module.get<QuizService>(QuizService);
    repository = module.get<QuizRepository>(QuizRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createQuiz', () => {
    beforeEach(() => {
      // Mock the notification types query
      const mockDrizzleService = service['drizzle'];
      const mockQueryChain = {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest
          .fn()
          .mockResolvedValue([
            { id: 'notification-type-1', code: 'new_quiz', name: 'New Quiz' },
          ]),
      };
      mockDrizzleService.db.select = jest.fn().mockReturnValue(mockQueryChain);
    });

    it('should create a quiz with immediate activation when start_time is in the past', async () => {
      const pastTime = new Date(Date.now() - 60000); // 1 minute ago
      const futureTime = new Date(Date.now() + 3600000); // 1 hour from now

      const quizData = {
        title: 'New Quiz',
        question_bank_id: 'bank1',
        created_by: 'user1',
        total_questions: 10,
        time_per_question: 30,
        start_time: pastTime.toISOString(),
        end_at: futureTime.toISOString(),
      };

      const mockCreatedQuiz = [
        {
          id: 'quiz1',
          ...quizData,
          status: 'active', // Should be active since start_time is in the past
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      jest.spyOn(repository, 'createQuiz').mockResolvedValue(mockCreatedQuiz);

      const result = await service.createQuiz(quizData);

      expect(repository.createQuiz).toHaveBeenCalledWith({
        ...quizData,
        status: 'active',
      });
      expect(result).toEqual(mockCreatedQuiz);
    });

    it('should create a quiz with inactive status when start_time is in the future', async () => {
      const futureStartTime = new Date(Date.now() + 3600000); // 1 hour from now
      const futureEndTime = new Date(Date.now() + 7200000); // 2 hours from now

      const quizData = {
        title: 'Future Quiz',
        question_bank_id: 'bank1',
        created_by: 'user1',
        total_questions: 10,
        time_per_question: 30,
        start_time: futureStartTime.toISOString(),
        end_at: futureEndTime.toISOString(),
      };

      const mockCreatedQuiz = [
        {
          id: 'quiz2',
          ...quizData,
          status: 'inactive', // Should be inactive since start_time is in the future
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      jest.spyOn(repository, 'createQuiz').mockResolvedValue(mockCreatedQuiz);

      const result = await service.createQuiz(quizData);

      expect(repository.createQuiz).toHaveBeenCalledWith({
        ...quizData,
        status: 'inactive',
      });
      expect(result).toEqual(mockCreatedQuiz);
    });

    it('should handle quiz creation without start_time', async () => {
      const quizData = {
        title: 'No Start Time Quiz',
        question_bank_id: 'bank1',
        created_by: 'user1',
        total_questions: 10,
        time_per_question: 30,
        end_at: new Date(Date.now() + 3600000).toISOString(),
      };

      const mockCreatedQuiz = [
        {
          id: 'quiz3',
          ...quizData,
          start_time: null,
          status: 'inactive',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      jest.spyOn(repository, 'createQuiz').mockResolvedValue(mockCreatedQuiz);

      const result = await service.createQuiz(quizData);

      expect(repository.createQuiz).toHaveBeenCalledWith({
        ...quizData,
        status: 'inactive',
      });
      expect(result).toEqual(mockCreatedQuiz);
    });
  });

  describe('getAllQuiz', () => {
    it('should return all quizzes', async () => {
      const mockQuizzes = {
        data: [
          {
            id: 'quiz1',
            title: 'Quiz 1',
            question_bank_id: 'bank1',
            created_by: {
              id: 'user1',
              email: '<EMAIL>',
              name: 'User One',
            },
            question_bank: {
              imageUrl: 'image.jpg',
              stack: 'JavaScript',
              framework: 'React',
            },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            total_questions: 10,
            time_per_question: 30,
            start_time: new Date().toISOString(),
            end_at: new Date().toISOString(),
            status: 'active',
          },
          {
            id: 'quiz2',
            title: 'Quiz 2',
            question_bank_id: 'bank2',
            created_by: {
              id: 'user2',
              email: '<EMAIL>',
              name: 'User Two',
            },
            question_bank: {
              imageUrl: 'image2.jpg',
              stack: 'TypeScript',
              framework: 'Angular',
            },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            total_questions: 15,
            time_per_question: 45,
            start_time: new Date().toISOString(),
            end_at: new Date().toISOString(),
            status: 'active',
          },
        ],
        total: 2,
      };

      const expectedResponse = {
        data: mockQuizzes,
        total: 2,
      };

      jest.spyOn(repository, 'getAllQuizzes').mockResolvedValue(mockQuizzes);

      const queryParams = {
        sort: 'id' as const,
        order: 'asc' as const,
        limit: 10,
        page: 1,
        search: '',
        status: undefined,
        all: false,
      };

      const result = await service.getAllQuiz(queryParams);
      expect(result).toEqual(expectedResponse);
    });

    it('should throw an error if fetching fails', async () => {
      jest
        .spyOn(repository, 'getAllQuizzes')
        .mockRejectedValue(
          new InternalServerErrorException('Failed to fetch quizzes'),
        );

      const queryParams = {
        sort: 'id' as const,
        order: 'asc' as const,
        limit: 10,
        page: 1,
        search: '',
        all: false,
      };

      await expect(service.getAllQuiz(queryParams)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('getQuizById', () => {
    it('should return a quiz by ID', async () => {
      const mockQuiz = {
        id: 'quiz1',
        title: 'Sample Quiz',
        question_bank_id: 'bank1',
        created_by: 'user1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        total_questions: 10,
        start_time: new Date().toISOString(),
        end_at: new Date().toISOString(),
        status: 'active',
        time_per_question: 30,
      };

      jest.spyOn(repository, 'getQuizById').mockResolvedValue(mockQuiz);
      const result = await service.getQuizById('1');
      expect(result).toEqual(mockQuiz);
    });

    it('should throw NotFoundException if quiz not found', async () => {
      jest
        .spyOn(repository, 'getQuizById')
        .mockRejectedValue(new NotFoundException(`Quiz with ID 1 not found`));
      await expect(service.getQuizById('1')).rejects.toThrow(NotFoundException);
    });

    it('should throw an error if fetching fails', async () => {
      jest
        .spyOn(repository, 'getQuizById')
        .mockRejectedValue(
          new InternalServerErrorException('Failed to fetch quiz'),
        );
      await expect(service.getQuizById('1')).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('updateQuiz', () => {
    it('should update a quiz', async () => {
      const quizData: UpdateQuizDto = {
        title: 'Updated Quiz',
        question_bank_id: 'bank1',
        created_by: 'user1',
        total_questions: 10,
        time_per_question: 30,
      };

      const mockUpdateResult = {
        message: 'Quiz updated successfully',
        statusCode: 200,
      };

      jest.spyOn(repository, 'updateQuiz').mockResolvedValue(mockUpdateResult);

      const result = await service.updateQuiz(quizData, 'quiz1');
      expect(result).toEqual(mockUpdateResult);
    });

    it('should throw NotFoundException if quiz not found', async () => {
      const quizData: UpdateQuizDto = {
        title: 'Updated Quiz',
        question_bank_id: 'bank1',
        created_by: 'user1',
        total_questions: 10,
        time_per_question: 30,
      };

      jest
        .spyOn(repository, 'updateQuiz')
        .mockRejectedValue(new NotFoundException(`Quiz with ID 1 not found`));

      await expect(service.updateQuiz(quizData, '1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw an error if updating fails', async () => {
      const quizData: UpdateQuizDto = {
        title: 'Updated Quiz',
        question_bank_id: 'bank1',
        created_by: 'user1',
        total_questions: 10,
        time_per_question: 30,
      };

      jest
        .spyOn(repository, 'updateQuiz')
        .mockRejectedValue(
          new InternalServerErrorException('Failed to update quiz'),
        );

      await expect(service.updateQuiz(quizData, '1')).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('deleteQuiz', () => {
    it('should delete a quiz', async () => {
      const mockDeleteResult = {
        message: 'Quiz deleted successfully',
        statusCode: 200,
      };

      jest.spyOn(repository, 'deleteQuiz').mockResolvedValue(mockDeleteResult);

      const result = await service.deleteQuiz('1');
      expect(result).toEqual(mockDeleteResult);
    });

    it('should throw NotFoundException if quiz not found', async () => {
      jest
        .spyOn(repository, 'deleteQuiz')
        .mockRejectedValue(new NotFoundException(`Quiz with ID 1 not found`));

      await expect(service.deleteQuiz('1')).rejects.toThrow(NotFoundException);
    });

    it('should throw an error if deletion fails', async () => {
      jest
        .spyOn(repository, 'deleteQuiz')
        .mockRejectedValue(
          new InternalServerErrorException('Failed to delete quiz'),
        );

      await expect(service.deleteQuiz('1')).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('getQuizQuestions', () => {
    it('should return quiz questions', async () => {
      const mockQuestions = [
        {
          id: 'question1',
          question: 'Sample Question 1',
          option_a: 'Option A1',
          options_b: 'Option B1',
          options_c: 'Option C1',
          options_d: 'Option D1',
          answers: 'A',
        },
        {
          id: 'question2',
          question: 'Sample Question 2',
          option_a: 'Option A2',
          options_b: 'Option B2',
          options_c: 'Option C2',
          options_d: 'Option D2',
          answers: 'B',
        },
      ];

      jest
        .spyOn(repository, 'getQuestionsForQuiz')
        .mockResolvedValue(mockQuestions);

      const result = await service.getQuizQuestions('1', 'user1');
      expect(result).toEqual(mockQuestions);
    });

    it('should throw an error if fetching fails', async () => {
      jest
        .spyOn(repository, 'getQuestionsForQuiz')
        .mockRejectedValue(
          new InternalServerErrorException('Failed to fetch quiz questions'),
        );

      await expect(service.getQuizQuestions('1', 'user1')).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('getQuizScore', () => {
    it('should return quiz scores', async () => {
      const mockQuizScores = [
        {
          quizScoreId: 'score1',
          quizId: 'quiz1',
          studentId: 'student1',
          quizScore: 90,
          dateTaken: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          quizTitle: 'Quiz 1',
        },
        {
          quizScoreId: 'score2',
          quizId: 'quiz2',
          studentId: 'student2',
          quizScore: 85,
          dateTaken: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          quizTitle: 'Quiz 2',
        },
      ];

      jest.spyOn(repository, 'getQuizScore').mockResolvedValue(mockQuizScores);

      const result = await service.getQuizScore('quiz1', 'student1');
      expect(result).toEqual(mockQuizScores);
    });

    it('should throw an error if fetching fails', async () => {
      jest
        .spyOn(repository, 'getQuizScore')
        .mockRejectedValue(
          new InternalServerErrorException('Failed to fetch quiz score'),
        );

      await expect(service.getQuizScore('quiz1', 'student1')).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('createQuizScore', () => {
    it('should create a quiz score', async () => {
      const quizScore: QuizScore = {
        quiz_id: 'quiz1',
        user_id: 'student1',
        score: 90,
      };

      const mockCreatedScore = [
        {
          id: 'score1',
          quiz_id: 'quiz1',
          user_id: 'student1',
          score: 90,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      jest
        .spyOn(repository, 'createQuizScore')
        .mockResolvedValue(mockCreatedScore);

      const result = await service.createQuizScore(quizScore);
      expect(result).toEqual(mockCreatedScore); // Expect array instead of single object
    });

    it('should throw BadRequestException if duplicate score exists', async () => {
      const quizScore: QuizScore = {
        quiz_id: 'quiz1',
        user_id: 'student1',
        score: 90,
      };

      jest
        .spyOn(repository, 'createQuizScore')
        .mockRejectedValue(
          new BadRequestException(
            'Quiz score already exists for this user and quiz',
          ),
        );

      await expect(service.createQuizScore(quizScore)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw an error if creation fails', async () => {
      const quizScore: QuizScore = {
        quiz_id: 'quiz1',
        user_id: 'student1',
        score: 90,
      };

      jest
        .spyOn(repository, 'createQuizScore')
        .mockRejectedValue(
          new InternalServerErrorException('Failed to create quiz score'),
        );

      await expect(service.createQuizScore(quizScore)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('getAllQuizScores', () => {
    it('should return all quiz scores', async () => {
      const mockQuizScores = [
        {
          quiz_id: 'quiz1',
          user_id: 'student1',
          score: 90,
        },
        {
          quiz_id: 'quiz2',
          user_id: 'student2',
          score: 85,
        },
      ];

      jest
        .spyOn(repository, 'getAllQuizScores')
        .mockResolvedValue(mockQuizScores);

      const result = await service.getAllQuizScores();
      expect(result).toEqual(mockQuizScores);
    });

    it('should throw an error if fetching fails', async () => {
      jest
        .spyOn(repository, 'getAllQuizScores')
        .mockRejectedValue(
          new InternalServerErrorException('Failed to fetch quiz scores'),
        );

      await expect(service.getAllQuizScores()).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });
});
