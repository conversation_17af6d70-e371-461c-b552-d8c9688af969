import { Modu<PERSON> } from '@nestjs/common';
import { QuestionBanksController } from './question-banks/question-banks.controller';
import { QuestionBankService } from './question-banks/question-banks.service';
import { McqRepository } from './repository/mcq.repository';
import { RepositoriesModule } from '@/repositories/repositories.module';
import { UploadModule } from '@/upload/upload.module';
import { QuestionRepository } from './repository/questions.repository';
import { QuestionsController } from './questions/questions.controller';
import { QuestionsService } from './questions/questions.service';
import { QuizController } from './quiz/quiz.controller';
import { QuizService } from './quiz/quiz.service';
import { QuizRepository } from './repository/quiz.repository';
import { QuizScheduler } from './quiz/quiz.scheduler';
import { LeaderBoardController } from './leader-board/leader-board.controller';
import { LeaderBoardService } from './leader-board/leader-board.service';
import { LeaderBoardScheduler } from './leader-board/leader-board.scheduler';
import { EnhancedNotificationModule } from '@app/shared/enhanced-notification/enhanced-notification.module';
import { NotificationModule } from '@app/shared/notification/notification.module';

import { PointSystemModule } from '@/point-system/point_system.module';

@Module({
  imports: [
    RepositoriesModule,
    UploadModule,
    EnhancedNotificationModule,
    NotificationModule,
    PointSystemModule,
  ],
  controllers: [
    QuestionBanksController,
    QuestionsController,
    QuizController,
    LeaderBoardController,
  ],
  providers: [
    QuestionBankService,
    McqRepository,
    QuestionRepository,
    QuestionsService,
    QuizService,
    QuizRepository,
    QuizScheduler,
    LeaderBoardService,
    LeaderBoardScheduler,
  ],
  exports: [
    QuestionBankService,
    QuestionsService,
    QuizService,
    LeaderBoardService,
  ],
})
export class McqModule {}
