import { User } from '@/guards/user.decorator';
import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  ParseFilePipe,
  Post,
  Put,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Delete,
  Query,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiParam,
  ApiQuery,
  ApiHeader,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Questions, type User as IUser } from '@/db/schema';
import { QuestionsService } from './questions.service';
import { CustomMaxFileSizeValidator } from '@/validators/custom-max-file-size.validator';
import { CustomFileUploadValidator } from '@/validators/custom-file-type.validator';

import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { FIVE_MB } from '@app/shared/constants/questions.constant';
import { QuestionRoutes } from '@app/shared/constants/question.constant';
import { ZodSerializerDto } from 'nestjs-zod';
import * as questionDto from '../dto/question.dto';
import { McqRepository } from '../repository/mcq.repository';
import { MultiQuestionDTO } from '../dto/question.dto';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { CustomParseIntPipe } from '@/common/pipes/custom-parse-int';

@ApiTags('Questions')
@Controller({ version: '1', path: 'questions' })
export class QuestionsController {
  private readonly logger = new Logger(QuestionsController.name);
  constructor(
    private readonly questionService: QuestionsService,
    private questionBankRepository: McqRepository,
  ) {}

  @Post(QuestionRoutes.UPLOAD_CSV)
  @ApiOperation({
    summary: 'Upload questions via CSV file',
    description:
      'Upload multiple questions to a question bank using a CSV file. Validates questions and prevents duplicates.',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'CSV file containing questions and question bank ID',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'CSV file with questions (max 5MB)',
        },
        questionBankId: {
          type: 'string',
          format: 'uuid',
          description: 'ID of the question bank to upload questions to',
        },
      },
      required: ['file', 'questionBankId'],
    },
  })
  @ApiCreatedResponse({
    description: 'Questions uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Questions processed successfully.',
        },
        totalInserted: { type: 'number', example: 5 },
        totalDuplicates: { type: 'number', example: 2 },
        duplicates: {
          type: 'array',
          items: { type: 'string' },
          example: ['What is JavaScript?', 'What is TypeScript?'],
        },
        inserted: {
          type: 'array',
          items: { type: 'string' },
          example: ['What is React?', 'What is Node.js?'],
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid file type or malformed CSV',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'string',
          example: 'Invalid file type. Only CSV files are allowed.',
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Question bank not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiConflictResponse({
    description: 'No new questions to upload (all duplicates)',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        message: { type: 'string', example: 'No new questions uploaded.' },
        error: { type: 'string', example: 'Conflict' },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseInterceptors(FileInterceptor('file'))
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'questions', action: 'create', possession: 'any' })
  @ZodSerializerDto(questionDto.UpdateQuestionDto)
  @CLIENT_TYPE(AppClients.WEB)
  @ApiBearerAuth()
  async uploadQuestions(
    @User() user: IUser,
    @Body()
    data: questionDto.BulkCreationQuestionDto,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: true,
        validators: [
          new CustomMaxFileSizeValidator({ maxSize: FIVE_MB }),
          new CustomFileUploadValidator({ isRequired: true }),
        ],
      }),
    )
    file: Express.Multer.File,
  ): Promise<questionDto.UploadQuestionsResponse> {
    try {
      if (file.mimetype !== 'text/csv') {
        throw new BadRequestException(
          'Invalid file type. Only CSV files are allowed.',
        );
      }
      const bank = await this.questionBankRepository.getQuestionBankById(
        data.questionBankId,
      );

      if (!bank || Object.keys(bank).length === 0) {
        throw new NotFoundException('Question bank not found');
      }
      const questions = await this.questionService.parseCSV(
        file.buffer.toString('utf-8'),
      );

      const existingQuestions =
        await this.questionService.checkQuestionsByBankId(data.questionBankId);

      const existingQuestionsSet = new Set(
        existingQuestions.map((q) => q.question),
      );

      const uniqueQuestions = questions.filter(
        (newQuestion) => !existingQuestionsSet.has(newQuestion.question),
      );

      const duplicateQuestions = questions.filter((newQuestion) =>
        existingQuestionsSet.has(newQuestion.question),
      );

      if (uniqueQuestions.length > 0) {
        await this.questionService.uploadQuestions(
          data.questionBankId,
          uniqueQuestions,
          user,
        );
      }

      if (
        uniqueQuestions.length === 0 ||
        uniqueQuestions.length === duplicateQuestions.length
      ) {
        throw new ConflictException('No new questions uploaded.');
      }

      return {
        message: 'Questions processed successfully.',
        totalInserted: uniqueQuestions.length,
        totalDuplicates: duplicateQuestions.length,
        duplicates: duplicateQuestions.map((q) => q.question),
        inserted: uniqueQuestions.map((q) => q.question),
      };
    } catch (error: any) {
      this.logger.error('Error uploading questions:', error);
      throw error;
    }
  }

  @Post()
  @ApiOperation({
    summary: 'Create question(s)',
    description:
      'Create one or multiple questions. Supports both single question and bulk creation.',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiBody({
    description: 'Question data - can be single question or array of questions',
    schema: {
      oneOf: [
        {
          type: 'object',
          properties: {
            question: {
              type: 'string',
              minLength: 3,
              maxLength: 255,
              example: 'What is JavaScript?',
            },
            option_a: {
              type: 'string',
              minLength: 1,
              maxLength: 255,
              example: 'A programming language',
            },
            option_b: {
              type: 'string',
              minLength: 1,
              maxLength: 255,
              example: 'A markup language',
            },
            option_c: { type: 'string', maxLength: 255, example: 'A database' },
            option_d: {
              type: 'string',
              maxLength: 255,
              example: 'An operating system',
            },
            answers: {
              type: 'string',
              minLength: 1,
              maxLength: 255,
              example: 'A',
            },
            questionBank_id: {
              type: 'string',
              format: 'uuid',
              example: '123e4567-e89b-12d3-a456-************',
            },
            is_golden: { type: 'boolean', example: false },
          },
          required: ['question', 'option_a', 'option_b', 'answers'],
        },
        {
          type: 'object',
          properties: {
            questions: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  question: { type: 'string', example: 'What is TypeScript?' },
                  option_a: { type: 'string', example: 'JavaScript superset' },
                  option_b: { type: 'string', example: 'Database' },
                  option_c: { type: 'string', example: 'Framework' },
                  option_d: { type: 'string', example: 'Library' },
                  answers: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['A'],
                  },
                  is_golden: { type: 'boolean', example: true },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiCreatedResponse({
    description: 'Question(s) created successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        question: { type: 'string' },
        option_a: { type: 'string' },
        option_b: { type: 'string' },
        option_c: { type: 'string' },
        option_d: { type: 'string' },
        answers: { type: 'string' },
        is_golden: { type: 'boolean' },
        questionBank_id: { type: 'string', format: 'uuid' },
        created_by: { type: 'string', format: 'uuid' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid question data',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'questions',
    action: 'create',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async createQuestion(
    @Body() data: MultiQuestionDTO | questionDto.QuestionsDto,
    @User() user: IUser,
  ) {
    try {
      if (Array.isArray(data)) {
        return await this.questionService.createQuestion(data, user.id);
      }
      return await this.questionService.createQuestion(data, user.id);
    } catch (error: any) {
      this.logger.error('Failed to create question', error.stack);
      throw error;
    }
  }

  @Get(QuestionRoutes.GET_QUESTION)
  @ApiOperation({
    summary: 'Get single question',
    description: 'Retrieve a specific question by its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'Question ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Question retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        question: { type: 'string', example: 'What is JavaScript?' },
        option_a: { type: 'string', example: 'A programming language' },
        option_b: { type: 'string', example: 'A markup language' },
        option_c: { type: 'string', example: 'A database' },
        option_d: { type: 'string', example: 'An operating system' },
        answers: { type: 'string', example: 'A' },
        is_golden: { type: 'boolean', example: false },
        questionBank_id: { type: 'string', format: 'uuid' },
        created_by: { type: 'string', format: 'uuid' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Question not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Question not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'questions',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuestion(
    @Param('id', new CustomParseUUIDPipe()) questionId: string,
  ) {
    try {
      return await this.questionService.getQuestion(questionId);
    } catch (error: any) {
      this.logger.error('Failed to get question', error.stack);
      throw error;
    }
  }
  @Get()
  @ApiOperation({
    summary: 'Get all questions',
    description:
      'Retrieve paginated list of questions with optional filtering and sorting.',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term for question text',
    required: false,
    type: String,
    example: 'JavaScript',
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field',
    required: false,
    type: String,
    example: 'created_at',
  })
  @ApiQuery({
    name: 'order',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
    example: 'desc',
  })
  @ApiQuery({
    name: 'type',
    description: 'Filter by question type',
    required: false,
    enum: ['is_golden'],
    example: 'is_golden',
  })
  @ApiOkResponse({
    description: 'Questions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              question: { type: 'string' },
              option_a: { type: 'string' },
              option_b: { type: 'string' },
              option_c: { type: 'string' },
              option_d: { type: 'string' },
              answers: { type: 'string' },
              is_golden: { type: 'boolean' },
              questionBank_id: { type: 'string', format: 'uuid' },
              created_by: { type: 'string', format: 'uuid' },
              created_at: { type: 'string', format: 'date-time' },
              updated_at: { type: 'string', format: 'date-time' },
            },
          },
        },
        total: { type: 'number', example: 50 },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'questions',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuestions(@Query() query: questionDto.questionQueryParamsDto) {
    try {
      return await this.questionService.getAllQuestions(
        query as questionDto.questionQueryParamsDto & {
          sort: keyof Questions;
        },
      );
    } catch (error: any) {
      this.logger.error('Failed to get questions', error.stack);
      throw error;
    }
  }

  @Get(QuestionRoutes.QUESTION_BANK_ID)
  @ApiOperation({
    summary: 'Get questions by question bank ID',
    description:
      'Retrieve paginated list of questions for a specific question bank.',
  })
  @ApiParam({
    name: 'questionBankId',
    description: 'Question Bank ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term for question text',
    required: false,
    type: String,
    example: 'JavaScript',
  })
  @ApiQuery({
    name: 'type',
    description: 'Filter by question type',
    required: false,
    enum: ['is_golden'],
    example: 'is_golden',
  })
  @ApiOkResponse({
    description: 'Questions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              question: { type: 'string' },
              option_a: { type: 'string' },
              option_b: { type: 'string' },
              option_c: { type: 'string' },
              option_d: { type: 'string' },
              answers: { type: 'string' },
              is_golden: { type: 'boolean' },
              questionBank_id: { type: 'string', format: 'uuid' },
              created_by: { type: 'string', format: 'uuid' },
              created_at: { type: 'string', format: 'date-time' },
              updated_at: { type: 'string', format: 'date-time' },
            },
          },
        },
        total: { type: 'number', example: 25 },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found or no questions found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'questions',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuestionsByBankId(
    @Param('questionBankId') questionBankId: string,
    @Query() query: questionDto.questionQueryParamsDto,
  ) {
    try {
      return await this.questionService.getQuestionsByBankId(
        questionBankId,
        query as questionDto.questionQueryParamsDto & {
          sort: keyof Questions;
        },
      );
    } catch (error: any) {
      this.logger.error('Failed to get questions by bank id', error.stack);
      throw error;
    }
  }

  @Put(QuestionRoutes.UPDATE_QUESTION)
  @ApiOperation({
    summary: 'Update question',
    description: 'Update an existing question by its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'Question ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiBody({
    description: 'Updated question data',
    schema: {
      type: 'object',
      properties: {
        question: {
          type: 'string',
          minLength: 3,
          maxLength: 255,
          example: 'What is TypeScript?',
        },
        option_a: {
          type: 'string',
          minLength: 1,
          maxLength: 255,
          example: 'A superset of JavaScript',
        },
        option_b: {
          type: 'string',
          minLength: 1,
          maxLength: 255,
          example: 'A database',
        },
        option_c: { type: 'string', maxLength: 255, example: 'A framework' },
        option_d: { type: 'string', maxLength: 255, example: 'A library' },
        answers: { type: 'string', minLength: 1, maxLength: 255, example: 'A' },
        is_golden: { type: 'boolean', example: true },
      },
    },
  })
  @ApiOkResponse({
    description: 'Question updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        question: { type: 'string' },
        option_a: { type: 'string' },
        option_b: { type: 'string' },
        option_c: { type: 'string' },
        option_d: { type: 'string' },
        answers: { type: 'string' },
        is_golden: { type: 'boolean' },
        questionBank_id: { type: 'string', format: 'uuid' },
        created_by: { type: 'string', format: 'uuid' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid question data',
  })
  @ApiNotFoundResponse({
    description: 'Question not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'questions',
    action: 'update',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async updateQuestion(
    @User() user: IUser,
    @Param('id') id: string,
    @Body() data: questionDto.UpdateQuestionDto,
  ) {
    try {
      return this.questionService.updateQuestion(id, data, user);
    } catch (error: any) {
      this.logger.error('Failed to update question', error.stack);
      throw error;
    }
  }

  @Delete(QuestionRoutes.DELETE_QUESTION)
  @ApiOperation({
    summary: 'Delete question',
    description: 'Delete a question by its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'Question ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Question deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Question deleted successfully' },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Question not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'questions',
    action: 'delete',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async deleteQuestion(@Param('id') id: string) {
    try {
      return this.questionService.deleteQuestion(id);
    } catch (error: any) {
      this.logger.error('Failed to delete question', error.stack);
      throw error;
    }
  }

  @Get(QuestionRoutes.GET_GOLDEN_QUESTIONS)
  @ApiOperation({
    summary: 'Get golden questions for quiz',
    description:
      'Retrieve a specified number of golden questions from a question bank for quiz generation.',
  })
  @ApiParam({
    name: 'bankId',
    description: 'Question Bank ID',
    type: 'string',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'total',
    description: 'Number of golden questions to retrieve',
    required: true,
    type: Number,
    example: 10,
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be mobile',
    enum: ['mobile'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Golden questions retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          question: { type: 'string', example: 'What is JavaScript?' },
          option_a: { type: 'string', example: 'A programming language' },
          option_b: { type: 'string', example: 'A markup language' },
          option_c: { type: 'string', example: 'A database' },
          option_d: { type: 'string', example: 'An operating system' },
          answers: { type: 'string', example: 'A' },
          is_golden: { type: 'boolean', example: true },
          questionBank_id: { type: 'string', format: 'uuid' },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid parameters or insufficient golden questions',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'string',
          example: 'Not enough golden questions available',
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'questions',
    action: 'read',
    possession: 'any',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async getGoldenQuestions(
    @Param('bankId', new CustomParseUUIDPipe()) bankId: string,
    @Query('total', CustomParseIntPipe)
    total: number,
  ): Promise<any> {
    try {
      return this.questionService.getGoldenQuestions(bankId, total);
    } catch (error: any) {
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      }
      this.logger.error('Failed to get golden questions', error.stack);
      throw error;
    }
  }
}
