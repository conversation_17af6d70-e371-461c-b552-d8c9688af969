import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { questionBank, Questions, questionsSchema, User } from '@/db/schema';
import * as fastcsv from '@fast-csv/parse';
import { Readable } from 'stream';
import { QuestionRepository } from '../repository/questions.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  bulkUpdateQuestionsDto,
  QuestionDto,
  questionQueryParamsDto,
  QuestionSchema,
} from '../dto/question.dto';
import { REQUIRED_HEADERS } from '@app/shared/constants/questions.constant';
import { and, eq, ilike, or, SQL } from 'drizzle-orm';

@Injectable()
export class QuestionsService {
  private readonly logger = new Logger(QuestionsService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private questionRepository: QuestionRepository,
  ) {}

  async validateCSV(data: string): Promise<boolean> {
    let isValid = true;
    const stream = Readable.from(data);

    await new Promise((resolve, reject) => {
      stream
        .pipe(fastcsv.parse({ headers: true }))
        .on('headers', (headers) => {
          for (const header of REQUIRED_HEADERS) {
            if (!headers.includes(header)) {
              isValid = false;
              break;
            }
          }
          resolve(null);
        })
        .on('error', (error) => {
          isValid = false;
          reject(error);
        });
    });

    return isValid;
  }

  async parseCSV(data: string): Promise<QuestionDto[]> {
    const questions: QuestionDto[] = [];
    const stream = Readable.from(data);

    return new Promise((resolve, reject) => {
      stream
        .pipe(fastcsv.parse({ headers: true }))
        .on('data', (row) => {
          const question = {
            question: row['question'],
            option_a: row['option_a'],
            option_b: row['option_b'],
            option_c: row['option_c'],
            option_d: row['option_d'],
            answers: row['answers']
              .split(',')
              .map((answer: string) => answer.trim()),
            is_golden: row['is_golden'] === true,
          };
          const parsedQuestion = QuestionSchema.safeParse(question);
          if (parsedQuestion.success) {
            questions.push(parsedQuestion.data);
          } else {
            this.logger.error(
              `Invalid question data: ${JSON.stringify(parsedQuestion.error)}`,
            );
          }
        })
        .on('end', () => resolve(questions))
        .on('error', (error) => reject(error));
    });
  }

  async uploadQuestions(
    questionBank_id: string,
    questions: QuestionDto[],

    user: User,
  ): Promise<void> {
    //Check if question bank exist in question_bank table
    for (const question of questions) {
      await this.questionRepository.createQuestion({
        ...question,
        questionBank_id,
        created_by: user.id,
        answers: question.answers.join(','),
      });
    }
  }

  async getQuestion(id: string) {
    return this.questionRepository.getQuestionById(id);
  }
  async getQuestionsByBankId(
    questionBankId: string,
    query: questionQueryParamsDto & {
      sort: keyof Questions;
    },
  ) {
    const { search, type } = query;
    const whereConditions: (SQL<unknown> | undefined)[] = [
      eq(questionsSchema.questionBank_id, questionBankId),
    ];
    if (search) {
      const searchReg = `%${search}%`;
      whereConditions.push(or(ilike(questionsSchema.question, searchReg)));
    }
    if (type) {
      if (type === 'is_golden')
        whereConditions.push(eq(questionsSchema.is_golden, true));
    }
    const data = await this.questionRepository.getAllQuestions({
      whereConditions,
      query,
    });

    const total = await this.drizzle.db.$count(
      questionsSchema,
      and(...whereConditions),
    );

    return {
      data,
      total,
    };
  }
  async checkQuestionsByBankId(questionBankId: string) {
    return this.questionRepository.checkQuestion(questionBankId);
  }
  async getAllQuestions(
    query: questionQueryParamsDto & {
      sort: keyof Questions;
    },
  ) {
    const data = await this.questionRepository.getAllQuestions({ query });
    const total = await this.drizzle.db.$count(questionsSchema);
    return {
      data,
      total,
    };
  }

  async createQuestion(data: any, created: string) {
    if (Array.isArray(data.questions)) {
      const insertPromises = data.questions.map((question: any) => {
        return this.questionRepository.createQuestion({
          ...question,
          answers: question.answers.join(','),
          created_by: created,
        });
      });

      const result = await Promise.all(insertPromises);
      return result;
    } else {
      return this.questionRepository.createQuestion({
        ...data,
        answers: data.answers.join(','),
        created_by: created,
      });
    }
  }
  async updateQuestion(id: string, data: any, user: User) {
    const result = await this.questionRepository.updateQuestions(
      data,
      id,
      user.id,
      this.drizzle.db,
    );
    return result;
  }

  async bulkUpdateQuestions(
    questionBankId: string,
    data: bulkUpdateQuestionsDto,
    user: User,
  ) {
    const { questions } = data;
    const [questionBankExists] = await this.drizzle.db
      .select({ id: questionBank.id })
      .from(questionBank)
      .where(eq(questionBank.id, questionBankId));
    if (!questionBankExists)
      throw new NotFoundException('Question bank does not exist');

    for (const question of questions) {
      const {
        id,
        answers,
        option_a,
        option_b,
        option_c,
        option_d,
        is_golden,
        question: questionText,
      } = question;
      if (id) {
        await this.updateQuestion(
          id,
          {
            answers: answers.join(','),
            option_a,
            option_b,
            option_c,
            option_d,
            is_golden,
          },
          user,
        );
      } else {
        await this.createQuestion(
          {
            question: questionText,
            option_a,
            option_b,
            option_c,
            option_d,
            answers,
            is_golden,
            questionBank_id: questionBankId,
          },
          user.id,
        );
      }
    }
  }

  async deleteQuestion(id: string) {
    return this.questionRepository.deleteQuestion(id);
  }
  async getQuiz(questionBankId: string, totalQuestions: number) {
    return this.questionRepository.getQuiz(
      questionBankId,
      totalQuestions,
      this.drizzle.db,
    );
  }
  async getGoldenQuestions(questionBankId: string, total: number) {
    return this.questionRepository.getGoldenQuestions(questionBankId, total);
  }
}
