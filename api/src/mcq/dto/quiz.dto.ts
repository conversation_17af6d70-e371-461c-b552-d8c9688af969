import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';
import {
  insertQuizSchema,
  insertQuizScoreSchema,
  quizKeys,
  quizSchema,
  QuizStatus,
} from '@/db/schema/quiz';
import { createSelectSchema } from 'drizzle-zod';
import { querySchema } from '@/common/dto/query-params.dto';

const insertQuizSchemaWithOriginalNaming = insertQuizSchema.extend({
  title: z.string().min(3).max(255).trim(),
  question_bank_id: z.string().uuid('Invalid question bank ID'),
  start_time: z.string().datetime().optional(),
  end_at: z.string().datetime().optional(),
  total_questions: z.number().int(),
  time_per_question: z.number().int(),
});
const updateQuizSchemaWithOriginalNaming = insertQuizSchema.extend({
  title: z.string().min(3).max(255).trim().optional(),
  question_bank_id: z.string().uuid('Invalid question bank ID').optional(),
  start_time: z.string().datetime().nullable().optional(),
  end_at: z.string().datetime().nullable().optional(),
  total_questions: z.number().int().optional(),
  time_per_question: z.number().int().optional(),
  status: z.string().optional(),
});
const insertQuizScoreSchemaWithOriginalNaming = insertQuizScoreSchema.extend({
  quiz_id: z.string().uuid('Invalid quiz ID'),
  user_id: z.string(),
  score: z.number().int(),
});
export type QuizParams = z.infer<typeof insertQuizSchemaWithOriginalNaming>;
export type QuizScore = z.infer<typeof insertQuizScoreSchemaWithOriginalNaming>;

export class QuizScoreDto extends createZodDto(
  insertQuizScoreSchemaWithOriginalNaming,
) {}
export const selectQuizSchema = createSelectSchema(quizSchema);
export const quizQueryParamsSchema = querySchema.extend({
  sort: z.enum(quizKeys).optional().default('id'),
  status: z.nativeEnum(QuizStatus).optional(),
});
export class QuizDto extends createZodDto(insertQuizSchemaWithOriginalNaming) {}
export class UpdateQuizDto extends createZodDto(
  updateQuizSchemaWithOriginalNaming,
) {}
export class quizQueryParamsDto extends createZodDto(quizQueryParamsSchema) {}

const quizScoreSchema = z.object({
  quizScoreId: z.string().uuid('Invalid quiz score ID').nullable(),
  quizId: z.string().uuid('Invalid quiz ID'),
  studentId: z.string().uuid('Invalid student ID'),
  quizScore: z.number().int(),
  dateTaken: z.string().date(),
  updatedAt: z.string().date(),
  quizTitle: z.string().min(1).max(255).trim(),
});

export type QuizScoreT = z.infer<typeof quizScoreSchema>;
export class updateQuizDto extends createZodDto(
  updateQuizSchemaWithOriginalNaming,
) {}
