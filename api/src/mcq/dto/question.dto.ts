import { querySchema } from '@/common/dto/query-params.dto';
import {
  insertQuestionsSchema,
  questionKeys,
  questionsSchema,
} from '@/db/schema/questions';
import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const questionStatus = {
  // active: 'active',
  // inactive: 'inactive',
  is_golden: 'is_golden',
} as const;

// Extend the insertQuestionsSchema with additional fields
const insertQuestionsSchemaWithOriginalNaming = insertQuestionsSchema.extend({
  question: z.string().min(3).max(255).trim(),
  option_a: z.string().min(1).max(255).trim(),
  option_b: z.string().min(1).max(255).trim(),
  option_c: z.string().max(255).trim(),
  option_d: z.string().max(255).trim(),
  answers: z.string().min(1).max(255).trim(),
  questionBank_id: z.string().uuid('Invalid question bank ID').optional(),
  is_golden: z.boolean().optional(),
});

const bulkUpdateQuestionsSchema = z.object({
  questions: z
    .array(
      insertQuestionsSchemaWithOriginalNaming
        .extend({
          id: z.string().uuid('Invalid ID').optional(),
          answers: z.array(z.string().trim()).min(1),
        })
        .omit({
          questionBank_id: true,
          created_by: true,
        }),
    )
    .min(1),
});

// Type inferred from the schema
export type QuestionsParams = z.infer<
  typeof insertQuestionsSchemaWithOriginalNaming
>;

// Schema for selecting questions
export const selectQuestionsSchema = createSelectSchema(questionsSchema);

// DTO for updating questions
export class UpdateQuestionDto extends createZodDto(
  insertQuestionsSchemaWithOriginalNaming.omit({
    created_by: true,
  }),
) {}

export class bulkUpdateQuestionsDto extends createZodDto(
  bulkUpdateQuestionsSchema,
) {}
// DTO for creating questions
export class QuestionsDto extends createZodDto(
  insertQuestionsSchemaWithOriginalNaming,
) {}

// Schema for CSV question validation
export const QuestionSchema = z.object({
  question: z.string().trim(),
  option_a: z.string().trim(),
  option_b: z.string().trim(),
  option_c: z.string().trim(),
  option_d: z.string().trim(),
  answers: z.array(z.string().trim()),
  is_golden: z.boolean(),
});

// Type inferred from the CSV question schema
export type QuestionDto = z.infer<typeof QuestionSchema>;

// Schema for UploadQuestionsResponse
export const uploadQuestionsResponseSchema = z.object({
  message: z.string(),
  totalInserted: z.number(),
  totalDuplicates: z.number(),
  duplicates: z.array(z.string()),
  inserted: z.array(z.string()),
});

// Type inferred from the UploadQuestionsResponse schema
export type UploadQuestionsResponse = z.infer<
  typeof uploadQuestionsResponseSchema
>;

const UploadQuestionDto = z.object({
  questionBankId: z
    .string({ message: 'Question bank id required' })
    .uuid('Invalid question bank id'),
});

// Schema for validating query params when getting questions
const questionQueryParamsSchema = querySchema.extend({
  sort: z.enum(questionKeys).optional().default('id'),
  type: z.nativeEnum(questionStatus).optional(),
});

export class BulkCreationQuestionDto extends createZodDto(UploadQuestionDto) {}
export class questionQueryParamsDto extends createZodDto(
  questionQueryParamsSchema,
) {}
export type MultiQuestionDTO = {
  questions: QuestionDto[];
};
