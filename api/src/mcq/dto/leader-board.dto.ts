import { z } from 'zod';
import { periodEnum, querySchema } from '@/common/dto/query-params.dto';
import { createZodDto } from 'nestjs-zod';

export type PeriodEnumType = z.infer<typeof periodEnum>;

// Current User Data Schema
export const CurrentUserDataSchema = z.object({
  student_id: z.string(),
  profile_pic_url: z.string(),
  first_name: z.string(),
  last_name: z.string(),
  total_score: z.number(),
  rank: z.number(),
  isCurrentUser: z.boolean(),
});

// Cached User Rank Schema
export const CachedUserRankSchema = z.object({
  currentUser: z.any(),
  rank: z.number(),
  timestamp: z.number(),
});

// Cached Leaderboard Schema
export const CachedLeaderboardSchema = z.object({
  data: z.array(z.any()),
  total: z.number(),
  timestamp: z.number(),
});

// Extend the base query schema and add leaderboard-specific fields
export const LeaderBoardQueryWebSchema = querySchema.extend({
  period: periodEnum,
  degree: z.string().optional(),
  institutionId: z.string().optional(),
  program: z.string().optional(),
  level: z.string().optional(),
});

// Create DTO classes
export class queryParamsLeaderBoardDto extends createZodDto(
  LeaderBoardQueryWebSchema,
) {}
export class CurrentUserDataDto extends createZodDto(CurrentUserDataSchema) {}
export class CachedUserRankDto extends createZodDto(CachedUserRankSchema) {}
export class CachedLeaderboardDto extends createZodDto(
  CachedLeaderboardSchema,
) {}

// Export type interfaces
export type CurrentUserData = z.infer<typeof CurrentUserDataSchema>;
export type CachedUserRank = z.infer<typeof CachedUserRankSchema>;
export type CachedLeaderboard = z.infer<typeof CachedLeaderboardSchema>;
