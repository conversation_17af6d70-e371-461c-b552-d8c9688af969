import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const insertQuestionBankSchema = z.object({
  name: z.string().trim().min(2),
  stack: z.string().trim().min(2),
  framework: z.string().trim().min(2),
});

export const updateQuestionBankSchema = insertQuestionBankSchema.extend({
  name: z.string().trim().min(2).optional(),
  stack: z.string().trim().min(2).optional(),
  framework: z.string().trim().min(2).optional(),
  imageUrl: z.string().url().optional(),
});

export class createQuestionBankDto extends createZodDto(
  insertQuestionBankSchema,
) {}
export class updateQuestionBankDto extends createZodDto(
  updateQuestionBankSchema,
) {}
