import {
  questionsSchema,
  QuestionsInput,
  Questions,
} from '@/db/schema/questions';
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  DatabaseType,
  DrizzleService,
} from '@app/shared/drizzle/drizzle.service';
import { eq, sql, and, SQL, asc, desc, getTableColumns } from 'drizzle-orm';
import {
  Foreign_Key_Violation_Error_Code,
  Invalid_Input_Error_Code,
  Unique_Constraint_Error_Code,
} from '@app/shared/constants/database.constants';
import { Question_Messages } from '@app/shared/constants/question.constant';
import { questionQueryParamsDto } from '../dto/question.dto';
import { organisations, users } from '@/db/schema';

@Injectable()
export class QuestionRepository {
  constructor(private drizzle: DrizzleService) {}

  async createQuestion(data: any, tx = this.drizzle.db): Promise<any> {
    try {
      let result;

      if (Array.isArray(data)) {
        result = await tx.insert(questionsSchema).values(data).returning();
      } else {
        result = await tx.insert(questionsSchema).values(data).returning();
      }
      return result;
    } catch (error: any) {
      if (
        error.code === Foreign_Key_Violation_Error_Code ||
        error.code === Invalid_Input_Error_Code ||
        error.code === Unique_Constraint_Error_Code
      ) {
        if (error.message.includes('duplicate key value violates')) {
          throw new ConflictException(Question_Messages.QUESTION_EXISTS);
        }
        throw new NotFoundException(Question_Messages.INVALID_REFERENCE);
      }
      throw error;
    }
  }

  async updateQuestions(
    data: Partial<QuestionsInput>,
    id: string,
    user_id: string,
    tx = this.drizzle.db,
  ) {
    try {
      const result = await tx
        .update(questionsSchema)
        .set(Object.assign({}, data, { created_by: user_id }))
        .where(eq(questionsSchema.id, id))
        .returning();

      if (result.length === 0) {
        throw new NotFoundException({
          message: `Question not found`,
          statusCode: 404,
        });
      }
      return result;
    } catch (error: any) {
      if (
        error.code === Foreign_Key_Violation_Error_Code ||
        error.code === Invalid_Input_Error_Code
      ) {
        throw new ConflictException();
      }
      throw error;
    }
  }

  async getQuestionsByBankId(
    questionBankId: string,
    tx = this.drizzle.db,
  ): Promise<Questions[]> {
    const result = await tx
      .select()
      .from(questionsSchema)
      .where(eq(questionsSchema.questionBank_id, questionBankId));

    return result;
  }

  async checkQuestion(bankId: string, tx = this.drizzle.db) {
    return tx
      .select()
      .from(questionsSchema)
      .where(and(eq(questionsSchema.questionBank_id, bankId)))
      .execute();
  }

  /**
   * Retrieves all questions based on the provided query parameters.
   *
   * @param tx - The database transaction to use. Defaults to the default database connection.
   * @param whereConditions - Additional conditions to apply to the query.
   * @param query - The query parameters, including sorting options.
   * @returns A promise that resolves to an array of Questions.
   */
  async getAllQuestions({
    tx = this.drizzle.db,
    whereConditions = [],
    query,
  }: {
    tx?: DatabaseType;
    whereConditions?: (SQL<unknown> | undefined)[];
    query: Omit<questionQueryParamsDto, 'search'> & {
      sort: keyof Questions;
    };
  }): Promise<Questions[]> {
    const { limit, order, page, sort, all } = query;
    const result = tx
      .select({
        ...getTableColumns(questionsSchema),
        creator: {
          id: users.id,
          email: users.email,
          name: organisations.name,
        },
      })
      .from(questionsSchema)
      .where(and(...whereConditions))
      .leftJoin(users, eq(users.id, questionsSchema.created_by))
      .leftJoin(organisations, eq(organisations.user_id, users.id))
      .orderBy(
        order == 'asc'
          ? asc(questionsSchema[sort])
          : desc(questionsSchema[sort]),
      );

    return all
      ? await result
      : await result.limit(limit).offset((page - 1) * limit);
  }

  async getQuestionById(
    id: string,
    tx = this.drizzle.db,
  ): Promise<Questions[]> {
    const result = await tx
      .select()
      .from(questionsSchema)
      .where(eq(questionsSchema.id, id));
    if (result.length === 0) {
      throw new NotFoundException({
        message: `Question  not found`,
        statusCode: 404,
      });
    }
    return result;
  }

  async deleteQuestion(
    id: string,
    tx = this.drizzle.db,
  ): Promise<{ message: string; statusCode: number }> {
    const result = await tx
      .delete(questionsSchema)
      .where(eq(questionsSchema.id, id))
      .returning();
    if (!result.length) {
      throw new NotFoundException({
        message: 'Question not found',
        statusCode: 404,
      });
    }
    return { message: 'Question deleted successfully', statusCode: 200 };
  }
  async getQuiz(
    questionBankId: string,
    totalQuestions: number,
    tx = this.drizzle.db,
  ) {
    const result = await tx
      .select({
        id: questionsSchema.id,
        question: questionsSchema.question,
        option_a: questionsSchema.option_a,
        option_b: questionsSchema.option_b,
        option_c: questionsSchema.option_c,
        option_d: questionsSchema.option_d,
        answers: questionsSchema.answers,
      })
      .from(questionsSchema)
      .where(
        and(
          eq(questionsSchema.questionBank_id, questionBankId),
          eq(questionsSchema.is_golden, false),
        ),
      )
      .orderBy(sql`RANDOM()`)
      .limit(totalQuestions);

    if (result.length === 0) {
      throw new NotFoundException({
        message: 'No questions available for this question bank',
        statusCode: 404,
      });
    }
    return result;
  }
  async getGoldenQuestions(id: string, total: number, tx = this.drizzle.db) {
    const result = await tx
      .select({
        id: questionsSchema.id,
        question: questionsSchema.question,
        option_a: questionsSchema.option_a,
        option_b: questionsSchema.option_b,
        option_c: questionsSchema.option_c,
        option_d: questionsSchema.option_d,
        answers: questionsSchema.answers,
      })
      .from(questionsSchema)
      .where(
        and(
          eq(questionsSchema.questionBank_id, id),
          eq(questionsSchema.is_golden, true),
        ),
      )
      .orderBy(sql`RANDOM()`)
      .limit(total);

    if (result.length === 0) {
      throw new NotFoundException({
        message: 'No golden questions available for this question bank',
        statusCode: 404,
      });
    }
    return result;
  }
}
