import { questionBank, QuestionBankInput } from '@/db/schema/mcq';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { eq } from 'drizzle-orm';

@Injectable()
export class McqRepository {
  constructor(private drizzle: DrizzleService) {}
  private logger = new Logger(McqRepository.name);
  async createQuestionBank(data: QuestionBankInput, tx = this.drizzle.db) {
    return tx.insert(questionBank).values(data).returning();
  }

  async updateQuestionBank(
    data: Partial<QuestionBankInput>,
    id: string,
    tx = this.drizzle.db,
  ) {
    return tx
      .update(questionBank)
      .set(data)
      .where(eq(questionBank.id, id))
      .returning();
  }

  async deleteQuestionBank(
    id: string,
    tx = this.drizzle.db,
  ): Promise<{ message: string; statusCode: number }> {
    try {
      const result = await tx
        .delete(questionBank)
        .where(eq(questionBank.id, id))
        .returning();
      if (!result.length) {
        throw new NotFoundException(`Question bank with ID ${id} not found`);
      }
      return { message: 'Question bank deleted successfully', statusCode: 200 };
    } catch (error: any) {
      throw error;
    }
  }
  async getQuestionBankById(id: string, tx = this.drizzle.db) {
    const data = await tx
      .select()
      .from(questionBank)
      .where(eq(questionBank.id, id))
      .execute();

    return data;
  }
}
