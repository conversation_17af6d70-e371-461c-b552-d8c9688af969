import {
  DatabaseType,
  DrizzleService,
} from '@app/shared/drizzle/drizzle.service';
import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import {
  eq,
  and,
  lt,
  gt,
  SQL,
  asc,
  desc,
  getTableColumns,
  count,
  gte,
} from 'drizzle-orm';
import { quizQueryParamsDto, QuizScore, QuizScoreT } from '../dto/quiz.dto';
import { Quiz, quizSchema, quizScoreSchema } from '@/db/schema/quiz';
import { QuestionRepository } from './questions.repository';
import {
  organisations,
  questionBank,
  questionsSchema,
  student_profiles,
  users,
} from '@/db/schema';
import { QuizMessages } from '@app/shared/constants/quiz.constant';
import { ZodError } from 'zod';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { PointConstant } from '@app/shared/constants/points-system.constant';

@Injectable()
export class QuizRepository {
  constructor(
    private drizzle: DrizzleService,
    private readonly questionRepository: QuestionRepository,
    private readonly pointSystemRepository: PointSystemRepository,
  ) {}
  private logger = new Logger(QuizRepository.name);

  async createQuiz(data: any, tx = this.drizzle.db): Promise<Quiz[]> {
    try {
      return await tx
        .insert(quizSchema)
        .values({ ...data })
        .returning();
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Retrieves all quizzes based on the provided query parameters.
   *
   * @param options - The options for retrieving quizzes.
   * @param options.tx - The database transaction to use. Defaults to `this.drizzle.db`.
   * @param options.whereConditions - The array of SQL conditions to filter the quizzes.
   * @param options.query - The query parameters for sorting and pagination.
   * @param options.query.sort - The field to sort the quizzes by.
   * @returns A Promise that resolves to the array of quizzes.
   */
  async getAllQuizzes({
    tx = this.drizzle.db,
    whereConditions = [],
    query,
  }: {
    tx?: DatabaseType;
    whereConditions?: (SQL<unknown> | undefined)[];
    query: quizQueryParamsDto & {
      sort: keyof Quiz;
    };
  }) {
    const { limit, order, sort, page, all } = query;
    const result = tx
      .select({
        ...getTableColumns(quizSchema),
        created_by: {
          id: users.id,
          email: users.email,
          name: organisations.name,
        },
        question_bank: {
          imageUrl: questionBank.imageUrl,
          stack: questionBank.stack,
          framework: questionBank.framework,
        },
      })
      .from(quizSchema)
      .where(and(...whereConditions))
      .leftJoin(users, eq(users.id, quizSchema.created_by))
      .leftJoin(organisations, eq(organisations.user_id, users.id))
      .leftJoin(questionBank, eq(questionBank.id, quizSchema.question_bank_id))
      .orderBy(
        order === 'asc' ? asc(quizSchema[sort]) : desc(quizSchema[sort]),
      );

    if (!all) {
      const data = await result.limit(limit).offset((page - 1) * limit);
      const total = await this.drizzle.db.$count(
        quizSchema,
        and(...whereConditions),
      );

      return { data, total };
    }

    return await result;
  }

  async getQuizById(id: string, tx = this.drizzle.db): Promise<Quiz> {
    try {
      const result = await tx
        .select()
        .from(quizSchema)
        .where(eq(quizSchema.id, id));

      if (!result.length) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }
      if (!result[0]) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }
      return result[0];
    } catch (error: any) {
      throw error;
    }
  }

  async getStudentScore(quizId: string, userId: string, tx = this.drizzle.db) {
    try {
      const result = await tx
        .select()
        .from(quizScoreSchema)
        .where(
          and(
            eq(quizScoreSchema.quiz_id, quizId),
            eq(quizScoreSchema.user_id, userId),
          ),
        );
      if (!result.length) {
        throw new NotFoundException({
          message: 'Student score not found',
          statusCode: 404,
        });
      }
      return result;
    } catch (error: any) {
      throw error;
    }
  }

  async createStudentScore(
    data: QuizScore,
    tx = this.drizzle.db,
  ): Promise<QuizScore[]> {
    try {
      const result = await tx.insert(quizScoreSchema).values(data).returning();
      return result;
    } catch (error: any) {
      throw error;
    }
  }

  async updateQuiz(
    data: any,
    id: string,
    tx = this.drizzle.db,
  ): Promise<{ message: string; statusCode: number }> {
    const {
      title,
      question_bank_id,
      start_time,
      end_at,
      total_questions,
      time_per_question,
      status,
      created_by,
    } = data;
    try {
      const result = await tx
        .update(quizSchema)
        .set({
          title: title,
          question_bank_id: question_bank_id!,
          created_by: created_by!,
          start_time: start_time!,
          end_at: end_at!,
          total_questions: total_questions!,
          time_per_question: time_per_question!,
          status: status!,
        })
        .where(eq(quizSchema.id, id))
        .returning();

      if (!result.length) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }

      return { message: 'Quiz updated successfully', statusCode: 200 };
    } catch (error: any) {
      if (error instanceof ZodError) {
        throw new ZodError(error.errors);
      }
      throw error;
    }
  }

  async deleteQuiz(id: string, tx = this.drizzle.db): Promise<any> {
    try {
      const result = await tx
        .delete(quizSchema)
        .where(eq(quizSchema.id, id))
        .returning();
      if (!result.length) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }
      if (result.length === 0) {
        throw new NotFoundException({
          message: 'Quiz do not exist',
          statusCode: 404,
        });
      }
      return { message: 'Quiz deleted successfully', statusCode: 200 };
    } catch (error: any) {
      throw error;
    }
  }
  async getQuestionsForQuiz(quizId: string, userId: string): Promise<any> {
    try {
      // Check if the student exists in the student_profiles table
      const studentId = await this.drizzle.db
        .select({ id: student_profiles.id })
        .from(student_profiles)
        .where(eq(student_profiles.user_id, userId))
        .execute();
      const { id } = studentId[0] ?? {};

      if (!id) {
        throw new Error('Student not verified');
      }

      // Check if the quiz exists
      const quiz = await this.getQuizById(quizId);
      if (!quiz) {
        throw new NotFoundException(`Quiz with ID ${quizId} not found`);
      }

      const { total_questions, question_bank_id } = quiz;

      // Fetch quiz questions
      const questions = await this.questionRepository.getQuiz(
        question_bank_id,
        total_questions,
      );

      // Insert quiz score if questions are found
      if (questions.length !== 0) {
        try {
          await this.drizzle.db
            .insert(quizScoreSchema)
            .values({
              quiz_id: quizId,
              user_id: id!,
              score: 0,
            })
            .returning();

          // Award points to student for answering quiz
          if (id) {
            await this.pointSystemRepository.awardPointsToStudent(
              PointConstant.MODULES.QUIZ,
              PointConstant.ACTIONS.ANSWER_MCQ,
              id,
            );
          }
        } catch (insertError: any) {
          this.logger.error('Failed to insert quiz score', insertError.stack);
          if (insertError.message.includes('violates foreign key constraint')) {
            throw new Error(
              'Failed to insert quiz score: User not found in student profiles or quiz not found',
            );
          }
          throw new Error(
            `Failed to insert quiz score: ${insertError.message}`,
          );
        }
      }
      return questions;
    } catch (error: any) {
      this.logger.error('Failed to get quiz questions', error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(error.message);
    }
  }
  async getQuizScore(quizId: string, userId: string): Promise<QuizScoreT[]> {
    try {
      const result = await this.drizzle.db
        .select({
          quizScoreId: quizScoreSchema.id,
          quizId: quizScoreSchema.quiz_id,
          studentId: quizScoreSchema.user_id,
          quizScore: quizScoreSchema.score,
          dateTaken: quizScoreSchema.created_at,
          updatedAt: quizScoreSchema.updated_at,
          quizTitle: quizSchema.title,
        })
        .from(quizScoreSchema)
        .innerJoin(quizSchema, eq(quizScoreSchema.quiz_id, quizSchema.id))
        .where(
          and(
            eq(quizScoreSchema.quiz_id, quizId),
            eq(quizScoreSchema.user_id, userId),
          ),
        );

      if (result.length === 0) {
        throw new NotFoundException({
          message: 'No quiz score found',
          statusCode: 404,
        });
      }
      return result;
    } catch (error: any) {
      throw error;
    }
  }

  async createQuizScore(data: QuizScore): Promise<QuizScore[]> {
    try {
      const result = await this.drizzle.db
        .insert(quizScoreSchema)
        .values(data)
        .returning();

      return result;
    } catch (error: any) {
      throw error;
    }
  }
  async getAllQuizScores(): Promise<QuizScore[]> {
    try {
      const results = await this.drizzle.db
        .select({
          quizScoreId: quizScoreSchema.id,
          quizId: quizSchema.id,
          userId: student_profiles.id,
          quizScoreQuizId: quizScoreSchema.quiz_id,
          quizScoreUserId: quizScoreSchema.user_id,
          score: quizScoreSchema.score,
          createdAt: quizScoreSchema.created_at,
          updatedAt: quizScoreSchema.updated_at,
          quizTitle: quizSchema.title,
          questionBankId: quizSchema.question_bank_id,
          totalQuestions: quizSchema.total_questions,
          timePerQuestion: quizSchema.time_per_question,
          userFirstName: student_profiles.first_name,
          userLastName: student_profiles.last_name,
          userOtherName: student_profiles.other_name,
        })
        .from(quizScoreSchema)
        .leftJoin(quizSchema, eq(quizSchema.id, quizScoreSchema.quiz_id))
        .leftJoin(
          student_profiles,
          eq(student_profiles.id, quizScoreSchema.user_id),
        )
        .execute();
      if (results.length === 0) {
        throw new NotFoundException({
          message: 'No quiz scores found',
          statusCode: 404,
        });
      }

      return results.map((row: any) => ({
        id: row.quizScoreId,
        quiz_id: row.quizScoreQuizId,
        user_id: row.quizScoreUserId,
        score: row.score,
        created_at: row.createdAt,
        updated_at: row.updatedAt,
        quiz: {
          id: row.quizId,
          title: row.quizTitle,
          question_bank_id: row.questionBankId,
          total_questions: row.totalQuestions,
          time_per_question: row.timePerQuestion,
        },
        user: {
          id: row.userId,
          first_name: row.userFirstName,
          last_name: row.userLastName,
          other_name: row.userOtherName,
        },
      }));
    } catch (error: any) {
      throw error;
    }
  }

  async getScheduledQuizzes(tx = this.drizzle.db) {
    try {
      const result = await tx.select().from(quizSchema);
      if (result.length === 0) {
        throw new NotFoundException({
          message: 'No scheduled quizzes found',
          statusCode: 404,
        });
      }
      return result;
    } catch (error: any) {
      this.logger.error(`Failed to get scheduled quizzes: ${error.message}`);
      throw error;
    }
  }
  async checkAndActivateQuiz() {
    try {
      const now = new Date().toISOString();
      // Only select quizzes that are currently inactive and should be activated
      const result = await this.drizzle.db
        .select()
        .from(quizSchema)
        .where(
          and(
            lt(quizSchema.start_time, now),
            gte(quizSchema.end_at, now),
            eq(quizSchema.status, 'inactive'),
          ),
        );

      if (result.length === 0) {
        this.logger.debug(QuizMessages.QUIZ_NOT_DUE);
        return { message: QuizMessages.QUIZ_NOT_DUE, result: [] };
      }

      // Update all inactive quizzes to active
      const updatePromises = result.map(async (quiz: any) => {
        await this.drizzle.db
          .update(quizSchema)
          .set({ status: 'active' })
          .where(eq(quizSchema.id, quiz.id));
      });

      await Promise.all(updatePromises);

      this.logger.debug(
        `${QuizMessages.QUIZ_UPDATE_STATUS} - ${result.length} quizzes activated`,
      );
      return { message: QuizMessages.QUIZ_UPDATE_STATUS, result };
    } catch (error: any) {
      this.logger.error(`Failed to check and activate quiz: ${error.message}`);
      throw error;
    }
  }
  async checkAndCompleteQuiz() {
    try {
      const now = new Date().toISOString();
      const result = await this.drizzle.db
        .select()
        .from(quizSchema)
        .where(lt(quizSchema.end_at, now));

      if (result.length === 0) {
        this.logger.debug(QuizMessages.QUIZ_NOT_FOUND);
      }

      const updatePromises = result.map(async (quiz: any) => {
        if (quiz.status !== 'completed') {
          await this.drizzle.db
            .update(quizSchema)
            .set({ status: 'expired' })
            .where(eq(quizSchema.id, quiz.id));
        }
      });

      await Promise.all(updatePromises);
      this.logger.debug(QuizMessages.QUIZ_EXPIRED_STATUS);

      return { message: QuizMessages.QUIZ_EXPIRED_STATUS, result };
    } catch (error: any) {
      throw error;
    }
  }
  async getAvailableQuizzes() {
    try {
      const now = new Date().toISOString();
      const result = await this.drizzle.db
        .select()
        .from(quizSchema)
        .where(
          and(
            lt(quizSchema.start_time, now),
            gt(quizSchema.end_at, now),
            eq(quizSchema.status, 'active'),
          ),
        )
        .orderBy(desc(quizSchema.start_time));

      if (result.length === 0) {
        throw new NotFoundException({
          message: 'No available quizzes found',
          statusCode: 404,
        });
      }
      return result;
    } catch (error: any) {
      throw error;
    }
  }
  async getStudentId(userId: string) {
    try {
      const result = await this.drizzle.db
        .select({ id: student_profiles.id })
        .from(student_profiles)
        .where(eq(student_profiles.user_id, userId.toString()));

      if (result.length === 0) {
        throw new NotFoundException({
          message: 'Student not found',
          statusCode: 404,
        });
      }
      if (result[0]) {
        return result[0].id;
      }
      throw new NotFoundException({
        message: 'Student not found',
        statusCode: 404,
      });
    } catch (error: any) {
      throw error;
    }
  }

  async updateQuizScore(data: QuizScore): Promise<QuizScore[]> {
    try {
      const { quiz_id, user_id, score } = data;
      const result = await this.drizzle.db
        .update(quizScoreSchema)
        .set({ score: score })
        .where(
          and(
            eq(quizScoreSchema.quiz_id, quiz_id),
            eq(quizScoreSchema.user_id, user_id),
          ),
        )
        .returning();

      return result;
    } catch (error) {
      throw error;
    }
  }

  async questionCountByBankId(questionBankId: string): Promise<number> {
    try {
      const result = await this.drizzle.db
        .select({ count: count() })
        .from(questionsSchema)
        .where(
          and(
            eq(questionsSchema.questionBank_id, questionBankId),
            eq(questionsSchema.is_golden, false),
          ),
        );
      const questionCount = result[0]?.count ?? 0;
      this.logger.debug(
        `Question count for bank ${questionBankId}: ${questionCount} non-golden questions`,
      );
      return questionCount;
    } catch (error) {
      this.logger.error(
        `Error counting questions for bank ${questionBankId}:`,
        error,
      );
      throw error;
    }
  }
}
