import { RolesBuilder } from 'nest-access-control';

export const roles = [
  'super_admin',
  'student',
  'student_admin',
  'admin',
  'club_admin',
  'club_member',
];
export type Role = (typeof roles)[number];

export const AppRoles: Record<Role, Role> = {
  super_admin: 'super_admin',
  student: 'student',
  student_admin: 'student_admin',
  admin: 'admin',
  club_member: 'club_member',
  club_admin: 'club_admin',
  member: 'member',
};

export const RBAC_ROLES = new RolesBuilder();

// Define student role first
RBAC_ROLES.grant(AppRoles.student)
  .readAny([
    'club',
    'post',
    'events',
    'institution',
    'country',
    'skill',
    'skill_category',
    'club_admin',
    'quiz',
    'leader-board',
    'notification',
    'quiz-question',
    'likes',
    'profile',
    'opportunity',
  ])
  .readOwn([
    'skill',
    'skill_category',
    'profile',
    'user',
    'club',
    'club_admin',
    'club_membership',
    'quiz-score',
    'notification',
  ])
  .createOwn([
    'profile',
    'club_membership',
    'quiz-score',
    'quiz-question',
    'upload',
    'point-reward',
    'likes',
    'user',
    'push-notification',
    'student_skill',
  ])
  .createAny(['club_membership', 'upload', 'likes', 'push-notification'])
  .updateOwn([
    'profile',
    'student_skill',
    'club_membership',
    'student_club',
    'quiz-score',
    'likes',
    'user',
    'notification',
  ])
  .deleteOwn(['push-notification', 'user', 'student_skill', 'skill']);

// Define super admin role and extend student
RBAC_ROLES.grant(AppRoles.super_admin)
  .readAny([
    'profile',
    'upload',
    'user',
    'club',
    'organisation',
    'country',
    'institution',
    'events',
    'post',
    'skill',
    'skill_category',
    'club_membership',
    'club_admin',
    'question-bank',
    'questions',
    'quiz-question',
    'quiz',
    'quiz-score',
    'leader-board',
    'raffle',
    'notification',
    'point-config',
    'point-reward',
    'point-rule',
    'email',
    'in-app-notification',
    'set-notification',
    'notification-web-stream',
    'user-approval',
    'opportunity',
  ])
  .createAny([
    'skill',
    'skill_category',
    'upload',
    'user',
    'club',
    'organisation',
    'institution',
    'country',
    'upload',
    'club_membership',
    'questions',
    'question-bank',
    'institution',
    'quiz',
    'raffle',
    'notification',
    'point-config',
    'point-reward',
    'point-rule',
    'opportunity',
    'post',
    'email',
    'push-notification',
    'in-app-notification',
    'set-notification',
    'notification-web-stream',
  ])
  .deleteAny([
    'profile',
    'upload',
    'user',
    'club',
    'post',
    'organisation',
    'institution',
    'events',
    'country',
    'skill',
    'skill_category',
    'questions',
    'question-bank',
    'quiz',
    'raffle',
    'point-config',
    'point-reward',
    'point-rule',
    'club_membership',
    'email',
    'in-app-notification',
    'set-notification',
    'user-approval',
    'user',
  ])
  .extend(AppRoles.student!)
  .updateAny([
    'profile',
    'student_skill',
    'club',
    'organisation',
    'country',
    'institution',
    'club_membership',
    'post',
    'questions',
    'question-bank',
    'quiz',
    'point-config',
    'quiz-score',
    'user',
    'notification',
    'in-app-notification',
    'set-notification',
    'notification-web-stream',
    'user-approval',
    'skill',
    'skill_category',
  ]);

// Define student admin role
RBAC_ROLES.grant(AppRoles.student_admin)
  .readAny([
    'student_club',
    'club',
    'events',
    'post',
    'quiz',
    'quiz-question',
    'likes',
    'user',
    'notification-web-stream',
    'user-approval',
    'profile',
    'opportunity',
  ])
  .updateAny(['post', 'user', 'likes', 'user-approval'])
  .updateOwn([
    'profile',
    'user',
    'club',
    'events',
    'post',
    'club_membership',
    'likes',
    'set-notification',
  ])
  .createAny([
    'events',
    'post',
    'upload',
    'likes',
    'user',
    'push-notification',
    'set-notification',
    'student_skill',
  ])
  .deleteAny(['club', 'club_admin', 'likes', 'post'])
  .deleteOwn([
    'club_membership',
    'events',
    'post',
    'likes',
    'users',
    'user',
    'push-notification',
    'student_skill',
    'skill',
  ])
  .extend(AppRoles.student!);

// Define organisation admin role and extend student admin
RBAC_ROLES.grant(AppRoles.admin)
  .readAny([
    'club_membership',
    'institution',
    'student_club',
    'questions',
    'question-bank',
    'quiz-question',
    'profile',
    'quiz',
    'quiz-score',
    'leader-board',
    'raffle',
    'point-config',
    'notification',
    'push-notification',
    'in-app-notification',
    'set-notification',
    'user-approval',
    'notification-web-stream',
    'opportunity',
  ])
  .readOwn([
    'profile',
    'users',
    'club',
    'events',
    'post',
    'institution',
    'question-bank',
    'in-app-notification',
    'notification-web-stream',
  ])
  .createAny([
    'events',
    'post',
    'events',
    'upload',
    'club',
    'organisation',
    'club_membership',
    'opportunity',
    'question-bank',
    'questions',
    'quiz',
    'raffle',
    'notification',
    'point-config',
    'set-notification',
    'notification-web-stream',
  ])
  .updateAny([
    'events',
    'club',
    'post',
    'organisation',
    'opportunity',
    'club_membership',
    'question-bank',
    'questions',
    'quiz',
    'raffle',
    'point-config',
    'user',
    'notification',
    'in-app-notification',
    'set-notification',
    'user-approval',
  ])
  .deleteAny([
    'events',
    'post',
    'events',
    'club',
    'question-bank',
    'questions',
    'club_membership',
    'quiz',
    'raffle',
    'point-config',
    'notification',
    'set-notification',
    'user-approval',
    'notification-web-stream',
    'user',
  ])
  .extend(AppRoles.student_admin!)
  .lock();
