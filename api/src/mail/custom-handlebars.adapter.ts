import * as fs from 'fs';
import * as path from 'path';
import * as handlebars from 'handlebars';
import { inline } from '@css-inline/css-inline';
import * as glob from 'glob';
import { get } from 'lodash';
import { registerHandlebarsHelpers } from './handlebars-helpers';

/**
 * Custom Handlebars adapter that ensures helpers are registered
 * This is a modified version of the original HandlebarsAdapter from @nestjs-modules/mailer
 */
export class CustomHandlebarsAdapter {
  private precompiledTemplates: Record<string, handlebars.TemplateDelegate> =
    {};
  private config: any = {
    inlineCssOptions: {},
    inlineCssEnabled: true,
  };

  constructor() {
    // Register our custom helpers
    registerHandlebarsHelpers();

    // Register the concat helper that's used by the original adapter
    handlebars.registerHelper('concat', (...args: any[]) => {
      args.pop();
      return args.join('');
    });

    // Register the partial block helper for base template
    handlebars.registerHelper('partial-block', function (this: any, options) {
      return options.fn(this);
    });
  }

  compile(mail: any, callback: any, mailerOptions: any): void {
    try {
      const templateBaseDir = get(mailerOptions, 'template.dir', '');
      const templateExt = '.hbs';
      const templateName = path.basename(
        mail.data.template,
        path.extname(mail.data.template),
      );
      const templateDir = path.join(
        templateBaseDir,
        path.dirname(mail.data.template),
      );
      const templatePath = path.join(templateDir, templateName + templateExt);

      const templateKey = path
        .relative(templateBaseDir, templatePath)
        .replace(templateExt, '');

      if (!this.precompiledTemplates[templateKey]) {
        try {
          const template = fs.readFileSync(templatePath, 'utf-8');
          this.precompiledTemplates[templateKey] = handlebars.compile(
            template,
            get(mailerOptions, 'template.options', {}),
          );
        } catch (err) {
          return callback(err);
        }
      }

      const runtimeOptions = get(mailerOptions, 'options', {
        partials: false,
        data: {},
      });

      // Always register the base template as a partial
      try {
        const baseTemplatePath = path.join(templateBaseDir, 'base.hbs');
        if (fs.existsSync(baseTemplatePath)) {
          handlebars.registerPartial(
            'base',
            fs.readFileSync(baseTemplatePath, 'utf-8'),
          );
        }
      } catch (err) {
        console.warn('Could not register base template as partial:', err);
      }

      // Register other partials if configured
      if (runtimeOptions.partials) {
        const partialPath = path
          .join(runtimeOptions.partials.dir, '**', '*.hbs')
          .replace(/\\/g, '/');
        const files = glob.sync(partialPath);
        files.forEach((file) => {
          const partialName = path.basename(file, path.extname(file));
          const partialDir = path.relative(
            runtimeOptions.partials.dir,
            path.dirname(file),
          );

          // Create the partial key - if partialDir is empty, use just the name
          // If partialDir has content, join it with the name
          let partialKey = partialDir
            ? path.join(partialDir, partialName)
            : partialName;

          // Also register with 'partials/' prefix for templates that expect it
          const partialKeyWithPrefix = `partials/${partialName}`;

          const partialContent = fs.readFileSync(file, 'utf-8');

          // Register both versions
          handlebars.registerPartial(partialKey, partialContent);
          handlebars.registerPartial(partialKeyWithPrefix, partialContent);
        });
      }

      const rendered = this.precompiledTemplates[templateKey](
        mail.data.context,
        {
          ...runtimeOptions,
          partials: this.precompiledTemplates,
        },
      );

      if (this.config.inlineCssEnabled) {
        try {
          mail.data.html = inline(rendered, this.config.inlineCssOptions);
        } catch (e) {
          callback(e);
        }
      } else {
        mail.data.html = rendered;
      }

      return callback();
    } catch (error) {
      console.error('Error in CustomHandlebarsAdapter.compile:', error);
      return callback(error);
    }
  }
}
