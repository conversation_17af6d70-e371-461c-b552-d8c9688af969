import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { QueueService } from '@app/shared/queue/queue.service';
import { MailerService } from '@nestjs-modules/mailer';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
  Optional,
} from '@nestjs/common';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly emailEnabled: boolean;
  private readonly useQueue: boolean;

  constructor(
    private readonly mailerService: MailerService,
    private readonly envConfig: EnvConfig,
    @Optional() private readonly queueService?: QueueService,
  ) {
    this.emailEnabled = this.envConfig.EMAIL_TOGGLE === 'ON';
    this.useQueue = !!this.queueService;
  }

  /**
   * Helper method to send an email either directly or via queue
   * @param to Recipient email address
   * @param subject Email subject
   * @param template Template name
   * @param context Template context data
   * @param critical Whether this is a critical email that should be sent directly
   */
  private async sendEmail(
    to: string,
    subject: string,
    template: string,
    context: Record<string, any>,
    critical: boolean = false,
  ): Promise<void> {
    if (critical || !this.useQueue) {
      try {
        await this.mailerService.sendMail({
          to,
          subject,
          template,
          context: {
            ...context,
            appName: this.envConfig.APP_NAME,
            appUrl: this.envConfig.FRONTEND_URL,
          },
        });
      } catch (error) {
        this.logger.error(
          `Failed to send email directly to ${to}: ${error instanceof Error ? error.message : String(error)}`,
        );
        throw new InternalServerErrorException(
          error instanceof Error ? error.message : String(error),
        );
      }
    } else {
      try {
        const emailJobId = `email-${to.replace('@', '-at-')}-${Date.now()}`;

        await this.queueService!.addSingleEmailJob(
          {
            to,
            subject,
            template,
            context: {
              ...context,
              appName: this.envConfig.APP_NAME,
              appUrl: this.envConfig.FRONTEND_URL,
            },
          },
          emailJobId,
        );
      } catch (error) {
        this.logger.error(
          `Failed to queue email: ${error instanceof Error ? error.message : String(error)}`,
        );

        await this.mailerService.sendMail({
          to,
          subject,
          template,
          context: {
            ...context,
            appName: this.envConfig.APP_NAME,
            appUrl: this.envConfig.FRONTEND_URL,
          },
        });
      }
    }
  }

  async sendOtp(email: string, otp: string) {
    try {
      await this.sendEmail(
        email,
        'OTP Verification',
        'otp-code',
        { otpCode: otp },
        true,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send OTP email: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new InternalServerErrorException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }
  async newOrganisation(
    email: string,
    organizationName: string,
    loginLink: string,
  ) {
    try {
      await this.sendEmail(
        email,
        'New Organisation',
        'new-organisation',
        {
          organizationName,
          signInLink: loginLink,
        },
        false,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send new organisation email: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new InternalServerErrorException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  async sendEmailSignInLink(email: string, link: string) {
    try {
      await this.sendEmail(
        email,
        'Sign In Link',
        'email-signin',
        {
          signInLink: link,
        },
        true,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send sign-in link email: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new InternalServerErrorException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  async sendStudentAdminPromotion({
    email,
    clubName,
    studentName,
  }: {
    email: string;
    clubName: string;
    studentName: string;
  }) {
    try {
      await this.sendEmail(
        email,
        'Club Role Change Notification',
        'add-student-admin',
        {
          clubName,
          studentName,
          signInLink: this.envConfig.FRONTEND_URL,
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to send student admin promotion email: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new InternalServerErrorException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  async waitingListNotification({ email }: { email: string }) {
    try {
      await this.sendEmail(
        email,
        'Account Activation Notification',
        'waiting-list',
        {
          message: `Go to the App and logon again`,
        },
        true,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send waiting list notification: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new InternalServerErrorException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  async sendCustomEmail({
    email,
    subject,
    template,
    context,
    critical = false,
  }: {
    email: string;
    subject: string;
    template: string;
    context: Record<string, any>;
    critical?: boolean;
  }) {
    try {
      if (!email || !email.includes('@')) {
        return;
      }

      let finalTemplate = template;
      if (!finalTemplate || finalTemplate === '') {
        if (
          context.type === 'new_post' ||
          context.type === 'new_event' ||
          context.type === 'new_opportunity' ||
          context.module === 'post'
        ) {
          finalTemplate = 'post';
        } else {
          finalTemplate = 'notification';
        }
      }

      const updatedContext = { ...context };
      if (updatedContext.date && typeof updatedContext.date === 'string') {
        try {
          const date = new Date(updatedContext.date);
          updatedContext.date = date.toUTCString();
        } catch (e) {
          // Silently continue with original date
        }
      }

      if (!updatedContext.userName) {
        updatedContext.userName = 'Reach User';
      }

      const isCriticalTemplate =
        finalTemplate === 'raffle-winner' ||
        finalTemplate === 'waiting-list-approval';

      try {
        await this.sendEmail(
          email,
          subject,
          finalTemplate,
          updatedContext,
          critical || isCriticalTemplate,
        );
      } catch (mailError) {
        if (
          mailError instanceof Error &&
          mailError.message &&
          mailError.message.includes('template not found')
        ) {
          await this.sendEmail(
            email,
            subject,
            'notification',
            updatedContext,
            critical || isCriticalTemplate,
          );
        } else {
          throw mailError;
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to send email to ${email}: ${error instanceof Error ? error.message : String(error)}`,
      );

      throw new InternalServerErrorException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  async disApproveWaitingListNotification({ email }: { email: string }) {
    try {
      await this.sendEmail(
        email,
        'Application Status Update',
        'waiting-list-disapproval',
        {
          message: `We regret to inform you that your application has not been approved at this time.`,
        },
        false,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send disapproval notification: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw new InternalServerErrorException(
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * Send notification to admin users when a student is added to the waiting list
   * @param studentEmail The email of the student who joined the waiting list
   * @param adminEmails Array of admin email addresses to notify
   */
  async sendWaitingListAdminNotification({
    studentEmail,
    adminEmails,
  }: {
    studentEmail: string;
    adminEmails: string[];
  }) {
    try {
      if (!this.emailEnabled) {
        return;
      }
      if (this.useQueue && adminEmails.length > 1) {
        try {
          const emails = adminEmails.map((adminEmail) => ({
            to: adminEmail,
            subject: 'New Student Waiting List Notification',
            template: 'admin-waiting-list-notification',
            context: {
              appName: this.envConfig.APP_NAME,
              studentEmail: studentEmail,
              adminDashboardUrl: `${this.envConfig.FRONTEND_URL}/admin/waiting-list`,
            },
          }));

          await this.queueService!.addBulkEmailJob({ emails });
          return;
        } catch (queueError) {
          // Continue with individual sending
        }
      }

      for (const adminEmail of adminEmails) {
        await this.sendEmail(
          adminEmail,
          'New Student Waiting List Notification',
          'admin-waiting-list-notification',
          {
            studentEmail: studentEmail,
            adminDashboardUrl: `${this.envConfig.FRONTEND_URL}/admin/waiting-list`,
          },
        );
      }
    } catch (error) {
      // Log but don't throw an error for notifications
      this.logger.error(
        `Error sending admin notifications: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Send bulk emails efficiently using the queue system
   * @param emails Array of email data objects
   * @returns Promise that resolves when all emails are queued
   */
  async sendBulkEmails(
    emails: Array<{
      to: string;
      subject: string;
      template: string;
      context: Record<string, any>;
    }>,
  ): Promise<boolean> {
    try {
      if (!this.emailEnabled) {
        return true;
      }
      if (this.useQueue) {
        await this.queueService!.addBulkEmailJob({ emails });
        return true;
      }
      const batchSize = 10;
      const batches = [];

      for (let i = 0; i < emails.length; i += batchSize) {
        batches.push(emails.slice(i, i + batchSize));
      }

      for (const batch of batches) {
        await Promise.all(
          batch.map(({ to, subject, template, context }) =>
            this.sendEmail(to, subject, template, context, false),
          ),
        );

        if (batches.length > 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send bulk emails: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }
}
