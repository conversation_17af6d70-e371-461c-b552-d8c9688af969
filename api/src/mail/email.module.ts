import { MailerModule } from '@nestjs-modules/mailer';
import { Global, Module } from '@nestjs/common';
import { EmailService } from './email.service';
import { join } from 'path';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { TypedConfigModule } from 'nest-typed-config';
import { registerHandlebarsHelpers } from './handlebars-helpers';
import { CustomHandlebarsAdapter } from './custom-handlebars.adapter';
import { QueueModule } from '@app/shared/queue/queue.module';

@Global()
@Module({
  imports: [
    // Import QueueModule to make QueueService available
    QueueModule.register(),
    MailerModule.forRootAsync({
      imports: [TypedConfigModule],
      inject: [EnvConfig],
      useFactory: async (envConfig: EnvConfig) => {
        // Register Handlebars helpers first to ensure they're available for templates
        registerHandlebarsHelpers();

        // Calculate proper path for templates
        // Use process.cwd() to ensure we get the correct path in both development and production
        const templatesDir = join(process.cwd(), 'src', 'mail', 'templates');

        // Always use AWS SES SMTP settings for testing
        const transport = {
          // Production email settings
          pool: true,
          transactionLog: true,
          debug: true,
          host: envConfig.SMTP_ENDPOINT,
          port: envConfig.SMTP_PORT,
          secure: true,
          auth: {
            user: envConfig.SMTP_USERNAME,
            pass: envConfig.SMTP_PASSWORD,
          },
        };

        return {
          transport,
          defaults: {
            sender: {
              name: envConfig.APP_NAME,
              address: envConfig.SENDER_EMAIL,
            },
            from: `${envConfig.APP_NAME} <${envConfig.SENDER_EMAIL}>`,
          },
          template: {
            dir: templatesDir,
            adapter: new CustomHandlebarsAdapter(),
            options: {
              strict: false,
            },
          },
          options: {
            partials: {
              dir: join(process.cwd(), 'src', 'mail', 'templates', 'partials'),
              options: {
                strict: false,
              },
            },
          },
        };
      },
    }),
  ],
  providers: [EmailService],
  exports: [EmailService],
})
export class EmailModule {}
