import * as Handlebars from 'handlebars';

/**
 * Register custom helpers for Handlebars templates
 */
export function registerHandlebarsHelpers() {
  // Register the partial block helper for base template
  Handlebars.registerHelper('eq', function (a, b) {
    return a === b;
  });

  Handlebars.registerHelper('gt', function (a, b) {
    return a > b;
  });

  Handlebars.registerHelper('lt', function (a, b) {
    return a < b;
  });

  Handlebars.registerHelper('formatDate', function (date) {
    return new Date(date).toLocaleString();
  });

  Handlebars.registerHelper('currentYear', function () {
    return new Date().getFullYear().toString();
  });
}
