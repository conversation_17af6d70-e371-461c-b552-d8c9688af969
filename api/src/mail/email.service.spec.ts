import { Test, TestingModule } from '@nestjs/testing';
import { EmailService } from './email.service';
import { MailerService } from '@nestjs-modules/mailer';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { InternalServerErrorException, Logger } from '@nestjs/common';

describe('EmailService', () => {
  let emailService: EmailService;

  const mockMailerService = {
    sendMail: jest.fn(),
  };

  // Using 'as EnvConfig' to bypass TypeScript type checking for the mock
  const mockEnvConfig = {
    APP_NAME: 'TestApp',
    FRONTEND_URL: 'http://localhost:3000',
  } as EnvConfig;

  // Mock the Logger to prevent error logs in test output
  const originalLoggerError = Logger.prototype.error;
  beforeAll(() => {
    // Replace the error method with a no-op function
    Logger.prototype.error = jest.fn();
  });

  afterAll(() => {
    // Restore the original error method
    Logger.prototype.error = originalLoggerError;
  });

  beforeEach(async () => {
    jest.clearAllMocks(); // Reset all mocks before each test

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        { provide: MailerService, useValue: mockMailerService },
        { provide: EnvConfig, useValue: mockEnvConfig },
      ],
    }).compile();

    emailService = module.get<EmailService>(EmailService);
  });

  it('should be defined', () => {
    expect(emailService).toBeDefined();
  });

  describe('sendOtp', () => {
    it('should send OTP email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.sendOtp('<EMAIL>', '123456'),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'OTP Verification',
        template: 'otp-code',
        context: {
          otpCode: '123456',
          appName: 'TestApp',
          appUrl: 'http://localhost:3000',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.sendOtp('<EMAIL>', '123456'),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('newOrganisation', () => {
    it('should send new organisation email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.newOrganisation(
          '<EMAIL>',
          'TestOrg',
          'http://login-link.com',
        ),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'New Organisation',
        template: 'new-organisation',
        context: {
          organizationName: 'TestOrg',
          signInLink: 'http://login-link.com',
          appName: 'TestApp',
          appUrl: 'http://localhost:3000',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.newOrganisation(
          '<EMAIL>',
          'TestOrg',
          'http://login-link.com',
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('sendEmailSignInLink', () => {
    it('should send sign-in link email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.sendEmailSignInLink(
          '<EMAIL>',
          'http://signin-link.com',
        ),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Sign In Link',
        template: 'email-signin',
        context: {
          signInLink: 'http://signin-link.com',
          appName: 'TestApp',
          appUrl: 'http://localhost:3000',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.sendEmailSignInLink(
          '<EMAIL>',
          'http://signin-link.com',
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('sendStudentAdminPromotion', () => {
    it('should send student admin promotion email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.sendStudentAdminPromotion({
          email: '<EMAIL>',
          clubName: 'TestClub',
          studentName: 'John Doe',
        }),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Club Role Change Notification',
        template: 'add-student-admin',
        context: {
          clubName: 'TestClub',
          studentName: 'John Doe',
          appName: 'TestApp',
          appUrl: 'http://localhost:3000',
          signInLink: 'http://localhost:3000',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.sendStudentAdminPromotion({
          email: '<EMAIL>',
          clubName: 'TestClub',
          studentName: 'John Doe',
        }),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('waitingListNotification', () => {
    it('should send waiting list notification email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.waitingListNotification({ email: '<EMAIL>' }),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Account Activation Notification',
        template: 'waiting-list',
        context: {
          appName: 'TestApp',
          appUrl: 'http://localhost:3000',
          message: 'Go to the App and logon again',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.waitingListNotification({ email: '<EMAIL>' }),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });
});
