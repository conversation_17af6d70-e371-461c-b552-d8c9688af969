import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { PointSystemService } from './services/point_system.service';
import { RealtimeLeaderboardService } from './services/realtime-leaderboard.service';

import {
  PointRewardController,
  PointRulesController,
  PointSystemController,
} from './point_system.controller';
import { PointSystemRepository } from './repository/point_system.repository';
import { QueueName } from '@app/shared/queue/queue.constants';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.GENERAL,
    }),
  ],
  providers: [
    PointSystemService,
    PointSystemRepository,
    RealtimeLeaderboardService,
  ],
  controllers: [
    PointSystemController,
    PointRulesController,
    PointRewardController,
  ],
  exports: [
    PointSystemService,
    PointSystemRepository,
    RealtimeLeaderboardService,
  ],
})
export class PointSystemModule {}
