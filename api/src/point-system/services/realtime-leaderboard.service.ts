import { Injectable, Logger } from '@nestjs/common';
import { CacheService } from '@app/shared/cache/cache.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { student_profiles } from '@/db/schema/student_profile';
import { eq, sql } from 'drizzle-orm';
import { PeriodEnumType } from '@/mcq/leader-board/leader-board.types';

export interface LeaderboardEntry {
  student_id: string;
  score: number;
  rank: number;
  first_name?: string;
  last_name?: string;
  profile_pic_url?: string;
  institution_name?: string;
}

export interface PointsUpdateEvent {
  student_id: string;
  points: number;
  module: string;
  action: string;
  timestamp: Date;
}

@Injectable()
export class RealtimeLeaderboardService {
  private readonly logger = new Logger(RealtimeLeaderboardService.name);

  // Redis keys for leaderboards
  private readonly LEADERBOARD_KEY_PREFIX = 'leaderboard:realtime';
  private readonly USER_DETAILS_PREFIX = 'user:details';
  private readonly MATERIALIZED_VIEW_REFRESH_JOB = 'refresh-materialized-views';

  constructor(
    private readonly cacheService: CacheService,
    private readonly redisService: RedisService,
    private readonly drizzle: DrizzleService,
    @InjectQueue('general') private readonly generalQueue: Queue,
  ) {}

  /**
   * Update student's points in real-time leaderboard
   */
  async updateStudentPoints(event: PointsUpdateEvent): Promise<void> {
    try {
      const { student_id, points } = event;

      // Get current period keys
      const periods = this.getCurrentPeriods();

      // Update points in Redis sorted sets for each period
      await Promise.all(
        periods.map(async (period) => {
          const leaderboardKey = this.getLeaderboardKey(period);

          // Increment the student's score in the sorted set
          await this.redisService.client.zincrby(
            leaderboardKey,
            points,
            student_id,
          );

          // Better approach: Only set TTL if key doesn't exist or has no expiration
          const ttl = await this.redisService.client.ttl(leaderboardKey);

          // TTL = -1 means no expiration set, TTL = -2 means key doesn't exist
          // Only set expiration if there's no TTL or if TTL is less than 10% of original
          if (ttl === -1 || ttl === -2 || this.shouldRefreshTTL(period, ttl)) {
            const newTtl = this.getPeriodTTL(period);
            await this.redisService.client.expire(leaderboardKey, newTtl);

            this.logger.debug(
              `Set TTL for leaderboard key ${leaderboardKey}: ${newTtl}s (previous TTL: ${ttl})`,
            );
          }
        }),
      );

      // Cache user details if not already cached
      await this.cacheUserDetails(student_id);

      // Queue a materialized view refresh (batched)
      await this.queueMaterializedViewRefresh();

      this.logger.debug(
        `Updated real-time leaderboard for student ${student_id} with ${points} points`,
      );
    } catch (error) {
      this.logger.error('Failed to update real-time leaderboard', error);
      throw error;
    }
  }

  /**
   * Get leaderboard for a specific period
   */
  async getLeaderboard(
    period: PeriodEnumType,
    page = 1,
    limit = 20,
  ): Promise<LeaderboardEntry[]> {
    try {
      const leaderboardKey = this.getLeaderboardKey(
        this.mapPeriodEnumToString(period),
      );
      const start = (page - 1) * limit;
      const end = start + limit - 1;

      // Get top students with scores from Redis sorted set
      const results = await this.redisService.client.zrevrange(
        leaderboardKey,
        start,
        end,
        'WITHSCORES',
      );

      if (!results.length) {
        return [];
      }

      // Parse results (Redis returns [member, score, member, score, ...])
      const leaderboard: LeaderboardEntry[] = [];

      for (let i = 0; i < results.length; i += 2) {
        const student_id = results[i] as string;
        const score = parseInt(results[i + 1] as string);
        const rank = start + Math.floor(i / 2) + 1;

        // Get cached user details
        const userDetails = await this.getCachedUserDetails(student_id);

        leaderboard.push({
          student_id,
          score,
          rank,
          ...userDetails,
        });
      }

      return leaderboard;
    } catch (error) {
      this.logger.error(
        `Failed to get leaderboard for period ${period}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get student's rank and score for a specific period
   */
  async getStudentRank(
    student_id: string,
    period: PeriodEnumType,
  ): Promise<{ rank: number; score: number } | null> {
    try {
      const leaderboardKey = this.getLeaderboardKey(
        this.mapPeriodEnumToString(period),
      );

      // Get student's score
      const score = await this.redisService.client.zscore(
        leaderboardKey,
        student_id,
      );

      if (score === null) {
        return null;
      }

      // Get student's rank (1-based)
      const rank = await this.redisService.client.zrevrank(
        leaderboardKey,
        student_id,
      );

      return {
        rank: rank !== null ? rank + 1 : 0,
        score: parseInt(score),
      };
    } catch (error) {
      this.logger.error(
        `Failed to get rank for student ${student_id} in period ${period}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Initialize leaderboards from database (run on startup or periodically)
   */
  async initializeLeaderboards(): Promise<void> {
    try {
      this.logger.log('Initializing real-time leaderboards from database...');

      // This should be run as a background job to avoid blocking startup
      await this.queueMaterializedViewRefresh(true);

      this.logger.log('Leaderboard initialization queued');
    } catch (error) {
      this.logger.error('Failed to initialize leaderboards', error);
      throw error;
    }
  }

  /**
   * Sync Redis leaderboards with materialized views
   */
  async syncWithMaterializedViews(): Promise<void> {
    try {
      const periods = Object.values(PeriodEnumType);

      for (const period of periods) {
        await this.syncPeriodLeaderboard(period);
      }

      this.logger.log(
        'Successfully synced all leaderboards with materialized views',
      );
    } catch (error) {
      this.logger.error('Failed to sync with materialized views', error);
      throw error;
    }
  }

  /**
   * Queue a materialized view refresh job (batched to avoid frequent updates)
   */
  private async queueMaterializedViewRefresh(immediate = false): Promise<void> {
    const delay = immediate ? 0 : 30000; // 30 seconds delay for batching

    await this.generalQueue.add(
      this.MATERIALIZED_VIEW_REFRESH_JOB,
      {},
      {
        delay,
        removeOnComplete: 10,
        removeOnFail: 5,
        // Prevent duplicate jobs
        jobId: `materialized-view-refresh-${Date.now()}`,
      },
    );
  }

  /**
   * Cache user details for leaderboard display
   */
  private async cacheUserDetails(student_id: string): Promise<void> {
    const cacheKey = `${this.USER_DETAILS_PREFIX}:${student_id}`;

    // Check if already cached
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return;
    }

    try {
      // Fetch from database
      const studentProfile =
        await this.drizzle.db.query.student_profiles.findFirst({
          where: eq(student_profiles.id, student_id),
          with: {
            user: {
              columns: {
                profile_pic_url: true,
              },
            },
            institution: {
              columns: {
                name: true,
              },
            },
          },
          columns: {
            first_name: true,
            last_name: true,
          },
        });

      if (studentProfile) {
        const userDetails = {
          first_name: studentProfile.first_name,
          last_name: studentProfile.last_name,
          profile_pic_url: studentProfile.user?.profile_pic_url,
          institution_name: studentProfile.institution?.name,
        };

        // Cache for 1 hour
        await this.cacheService.set(cacheKey, userDetails, 3600);
      }
    } catch (error) {
      this.logger.warn(`Failed to cache user details for ${student_id}`, error);
    }
  }

  /**
   * Get cached user details
   */
  private async getCachedUserDetails(
    student_id: string,
  ): Promise<Partial<LeaderboardEntry>> {
    const cacheKey = `${this.USER_DETAILS_PREFIX}:${student_id}`;
    const cached = await this.cacheService.get(cacheKey);

    return cached || {};
  }

  /**
   * Sync a specific period's leaderboard with its materialized view
   */
  private async syncPeriodLeaderboard(period: PeriodEnumType): Promise<void> {
    const viewName = this.getViewNameByPeriod(period);
    const leaderboardKey = this.getLeaderboardKey(
      this.mapPeriodEnumToString(period),
    );

    // Clear existing Redis data
    await this.redisService.client.del(leaderboardKey);

    // Fetch from materialized view
    const result = await this.drizzle.db.execute(sql`
      SELECT student_id, total_score
      FROM ${sql.identifier(viewName)}
      ORDER BY total_score DESC
      LIMIT 1000
    `);

    const results = result.rows as Array<{
      student_id: string;
      total_score: number;
    }>;

    if (results.length > 0) {
      // Bulk insert into Redis sorted set
      const members: Array<number | string> = [];

      results.forEach((row) => {
        members.push(row.total_score, row.student_id);
      });

      await this.redisService.client.zadd(leaderboardKey, ...members);

      // Set TTL
      const ttl = this.getPeriodTTL(this.mapPeriodEnumToString(period));
      await this.redisService.client.expire(leaderboardKey, ttl);
    }

    this.logger.debug(
      `Synced ${results.length} entries for ${period} leaderboard`,
    );
  }

  /**
   * Get current period identifiers
   */
  private getCurrentPeriods(): string[] {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const week = this.getWeekOfYear(now);

    return [
      `day:${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
      `week:${year}-${week}`,
      `month:${year}-${month}`,
      `year:${year}`,
    ];
  }

  /**
   * Get leaderboard Redis key for a period
   */
  private getLeaderboardKey(period: string): string {
    return `${this.LEADERBOARD_KEY_PREFIX}:${period}`;
  }

  /**
   * Get TTL in seconds for different periods
   */
  private getPeriodTTL(period: string): number {
    if (period.startsWith('day:')) return 86400; // 24 hours
    if (period.startsWith('week:')) return 604800; // 7 days
    if (period.startsWith('month:')) return 2592000; // 30 days
    if (period.startsWith('year:')) return 31536000; // 365 days
    return 86400; // Default 24 hours
  }

  /**
   * Determine if TTL should be refreshed based on remaining time
   * Only refresh if TTL has dropped below 10% of original duration
   * @param period The period string (e.g., 'day:2024-06-16')
   * @param currentTtl Current TTL in seconds
   * @returns true if TTL should be refreshed
   */
  private shouldRefreshTTL(period: string, currentTtl: number): boolean {
    const originalTtl = this.getPeriodTTL(period);
    const refreshThreshold = originalTtl * 0.1; // 10% threshold

    // Refresh if current TTL is less than 10% of original
    // This prevents constant TTL resets while ensuring data doesn't expire prematurely
    return currentTtl > 0 && currentTtl < refreshThreshold;
  }

  /**
   * Map PeriodEnumType to string for Redis keys
   */
  private mapPeriodEnumToString(period: PeriodEnumType): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const week = this.getWeekOfYear(now);

    switch (period) {
      case PeriodEnumType.DAILY:
        return `day:${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      case PeriodEnumType.WEEKLY:
        return `week:${year}-${week}`;
      case PeriodEnumType.MONTHLY:
        return `month:${year}-${month}`;
      case PeriodEnumType.YEARLY:
        return `year:${year}`;
      default:
        return `day:${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Get materialized view name by period enum
   */
  private getViewNameByPeriod(period: PeriodEnumType): string {
    switch (period) {
      case PeriodEnumType.DAILY:
        return 'leaderboard_day';
      case PeriodEnumType.WEEKLY:
        return 'leaderboard_week';
      case PeriodEnumType.MONTHLY:
        return 'leaderboard_month';
      case PeriodEnumType.YEARLY:
        return 'leaderboard_year';
      case PeriodEnumType.ALL_TIME:
        return 'leaderboard_all_time';
      case PeriodEnumType.QUARTERLY:
        return 'leaderboard_current_quarter';
      case PeriodEnumType.CURRENT_QUARTER:
        return 'leaderboard_current_quarter';
      case PeriodEnumType.FIRST_QUARTER:
        return 'leaderboard_first_quarter';
      case PeriodEnumType.SECOND_QUARTER:
        return 'leaderboard_second_quarter';
      case PeriodEnumType.THIRD_QUARTER:
        return 'leaderboard_third_quarter';
      case PeriodEnumType.FOURTH_QUARTER:
        return 'leaderboard_fourth_quarter';
      default:
        return 'leaderboard_day';
    }
  }

  /**
   * Get week of year
   */
  private getWeekOfYear(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear =
      (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  /**
   * Queue a leaderboard update (public interface for external calls)
   */
  async queueLeaderboardUpdate(event: PointsUpdateEvent): Promise<void> {
    try {
      await this.generalQueue.add('update-realtime-leaderboard', event, {
        delay: 100,
        removeOnComplete: 50,
        removeOnFail: 10,
      });

      this.logger.debug(
        `Queued leaderboard update for student ${event.student_id} with ${event.points} points`,
      );
    } catch (error) {
      this.logger.error('Failed to queue leaderboard update', error);
      throw error;
    }
  }
}
