/**
 * Performance Test for Real-time Leaderboard System
 *
 * This test simulates high-frequency point awarding to verify
 * the performance characteristics of the new system.
 */

import { Test } from '@nestjs/testing';
import { RealtimeLeaderboardService } from '../services/realtime-leaderboard.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { CacheService } from '@app/shared/cache/cache.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Queue } from 'bullmq';

describe('RealtimeLeaderboard Performance', () => {
  let service: RealtimeLeaderboardService;
  let mockQueue: jest.Mocked<Queue>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        RealtimeLeaderboardService,
        {
          provide: CacheService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            invalidatePattern: jest.fn(),
          },
        },
        {
          provide: RedisService,
          useValue: {
            client: {
              pipeline: jest.fn().mockReturnValue({
                zincrby: jest.fn(),
                expire: jest.fn(),
                exec: jest.fn().mockResolvedValue([]),
              }),
              exists: jest.fn().mockResolvedValue(0),
              setex: jest.fn().mockResolvedValue('OK'),
            },
          },
        },
        {
          provide: DrizzleService,
          useValue: {
            db: {
              query: {
                student_profiles: {
                  findFirst: jest.fn().mockResolvedValue({
                    id: 'student_1',
                    first_name: 'John',
                    last_name: 'Doe',
                  }),
                },
              },
            },
          },
        },
        {
          provide: 'BullQueue_general',
          useValue: {
            add: jest.fn().mockResolvedValue({ id: 'job_id' }),
          },
        },
      ],
    }).compile();

    service = module.get<RealtimeLeaderboardService>(
      RealtimeLeaderboardService,
    );
    mockQueue = module.get('BullQueue_general');
  });

  describe('High-frequency Point Awarding', () => {
    it('should handle 1000 concurrent point awards without blocking', async () => {
      const startTime = Date.now();
      const promises = [];

      for (let i = 0; i < 1000; i++) {
        promises.push(
          service.queueLeaderboardUpdate({
            student_id: `student_${i}`,
            points: 10,
            module: 'test',
            action: 'concurrent_test',
            timestamp: new Date(),
          }),
        );
      }

      await Promise.all(promises);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(1000); // Should complete in under 1 second
      expect(mockQueue.add).toHaveBeenCalledTimes(1000);
    });

    it('should maintain consistent performance under load', async () => {
      const batchSizes = [10, 50, 100, 500];
      const results: Array<{
        batchSize: number;
        duration: number;
        avgTime: number;
      }> = [];

      for (const batchSize of batchSizes) {
        const startTime = Date.now();
        const promises = [];

        for (let i = 0; i < batchSize; i++) {
          promises.push(
            service.queueLeaderboardUpdate({
              student_id: `student_${i}`,
              points: 10,
              module: 'test',
              action: 'load_test',
              timestamp: new Date(),
            }),
          );
        }

        await Promise.all(promises);
        const duration = Date.now() - startTime;
        const avgTime = duration / batchSize;

        results.push({ batchSize, duration, avgTime });
      }

      // Performance should scale linearly
      results.forEach((result, index) => {
        expect(result.avgTime).toBeLessThan(10); // Each operation under 10ms
        if (index > 0) {
          // Average time per operation should remain consistent
          const previousAvg = results[index - 1]?.avgTime || 0;
          const maxAcceptableTime = Math.max(previousAvg * 2, 1); // At least 1ms tolerance
          expect(result.avgTime).toBeLessThan(maxAcceptableTime);
        }
      });
    });
  });

  describe('Memory Efficiency', () => {
    it('should use minimal memory for queue operations', () => {
      const beforeMemory = process.memoryUsage();

      // Queue many operations
      const promises = [];
      for (let i = 0; i < 1000; i++) {
        promises.push(
          service.queueLeaderboardUpdate({
            student_id: `student_${i}`,
            points: 10,
            module: 'test',
            action: 'memory_test',
            timestamp: new Date(),
          }),
        );
      }

      const afterMemory = process.memoryUsage();
      const memoryIncrease = afterMemory.heapUsed - beforeMemory.heapUsed;

      // Should use less than 10MB for 1000 operations
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });
  });

  describe('Error Resilience', () => {
    it('should continue processing when some operations fail', async () => {
      // Mock some queue operations to fail
      mockQueue.add
        .mockResolvedValueOnce({ id: 'job_1' } as any)
        .mockRejectedValueOnce(new Error('Queue full'))
        .mockResolvedValueOnce({ id: 'job_2' } as any)
        .mockRejectedValueOnce(new Error('Redis down'))
        .mockResolvedValueOnce({ id: 'job_3' } as any);

      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          service
            .queueLeaderboardUpdate({
              student_id: `student_${i}`,
              points: 10,
              module: 'test',
              action: 'error_test',
              timestamp: new Date(),
            })
            .catch(() => 'failed'), // Catch errors to prevent test failure
        );
      }

      const results = await Promise.all(promises);
      const successCount = results.filter((r: any) => r !== 'failed').length;
      const failCount = results.filter((r: any) => r === 'failed').length;

      expect(successCount).toBeGreaterThan(0);
      expect(failCount).toBeGreaterThan(0);
      expect(mockQueue.add).toHaveBeenCalledTimes(5);
    });
  });
});
