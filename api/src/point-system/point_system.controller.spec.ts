import { Test, TestingModule } from '@nestjs/testing';
import {
  PointSystemController,
  PointRulesController,
  PointRewardController,
} from './point_system.controller';
import { PointSystemService } from './services/point_system.service';
import {
  PointConfigDto,
  PointRuleDto,
  PointsConfigQueryParamsDto,
  PointsLogDto,
} from './dto';
import { ROLES_BUILDER_TOKEN } from 'nest-access-control';
import { Reflector } from '@nestjs/core';
import { RoleGuard } from '@/guards/role.guard';

describe('PointSystemController', () => {
  let pointSystemController: PointSystemController;
  let pointRulesController: PointRulesController;
  let pointRewardController: PointRewardController;

  const mockPointSystemService = {
    createPointConfiguration: jest.fn(),
    getPointConfiguration: jest.fn(),
    getAllPointConfiguration: jest.fn(),
    updatePointConfiguration: jest.fn(),
    removePointConfiguration: jest.fn(),
    getAllPoints: jest.fn(),
    verifyPointsAwarded: jest.fn(),
    getAllPointRules: jest.fn(),
    createPointRule: jest.fn(),
    updatePointRule: jest.fn(),
    removePointRule: jest.fn(),
    disablePointRule: jest.fn(),
    rewardPoints: jest.fn(),
    removePointsFromStudent: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [
        PointSystemController,
        PointRulesController,
        PointRewardController,
      ],
      providers: [
        {
          provide: PointSystemService,
          useValue: mockPointSystemService,
        },
        {
          provide: ROLES_BUILDER_TOKEN,
          useValue: {
            getRoles: jest.fn().mockReturnValue([]),
            getGrants: jest.fn().mockReturnValue({}),
          },
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(RoleGuard)
      .useValue({ canActivate: jest.fn().mockReturnValue(true) })
      .compile();

    pointSystemController = module.get<PointSystemController>(
      PointSystemController,
    );
    pointRulesController =
      module.get<PointRulesController>(PointRulesController);
    pointRewardController = module.get<PointRewardController>(
      PointRewardController,
    );
  });

  it('should be defined', () => {
    expect(pointSystemController).toBeDefined();
    expect(pointRulesController).toBeDefined();
    expect(pointRewardController).toBeDefined();
  });

  describe('PointSystemController', () => {
    describe('createPointConfiguration', () => {
      it('should create a point configuration', async () => {
        const pointConfigDto: PointConfigDto = {
          point_name: 'Test Points',
          point_value: 10,
          description: 'Test Description',
        };

        const mockUser = {
          id: 'user-1',
          email: '<EMAIL>',
          role: 'admin',
        };

        const expectedResult = {
          id: 'config-1',
          ...pointConfigDto,
          created_by: mockUser.id,
        };

        mockPointSystemService.createPointConfiguration.mockResolvedValue(
          expectedResult,
        );

        const result = await pointSystemController.createPointConfiguration(
          pointConfigDto,
          mockUser as any,
        );

        expect(
          mockPointSystemService.createPointConfiguration,
        ).toHaveBeenCalledWith({
          ...pointConfigDto,
          created_by: mockUser.id,
        });
        expect(result).toEqual(expectedResult);
      });
    });

    describe('getPointConfiguration', () => {
      it('should get a point configuration by id', async () => {
        const configId = 'config-1';
        const expectedResult = {
          data: [
            {
              id: configId,
              module: 'Test',
              action: 'Create',
              points_config_id: 'config-1',
              frequency: 'once_a_day',
            },
          ],
          total: 1,
        };

        mockPointSystemService.getPointConfiguration.mockResolvedValue(
          expectedResult,
        );

        const result =
          await pointSystemController.getPointConfiguration(configId);

        expect(
          mockPointSystemService.getPointConfiguration,
        ).toHaveBeenCalledWith(configId);
        expect(result).toEqual(expectedResult);
      });
    });

    describe('getAllConfiguration', () => {
      it('should get all point configurations', async () => {
        const query: PointsConfigQueryParamsDto = {
          page: 1,
          limit: 10,
          search: '',
          order: 'asc',
          all: true,
        };

        const expectedResult = {
          data: {
            id: 'config-1',
            point_name: 'Test Points',
            point_value: 10,
            description: 'Test Description',
          },
          total: 1,
        };

        mockPointSystemService.getAllPointConfiguration.mockResolvedValue(
          expectedResult,
        );

        const result = await pointSystemController.getAllConfiguration(query);

        expect(
          mockPointSystemService.getAllPointConfiguration,
        ).toHaveBeenCalledWith(query);
        expect(result).toEqual(expectedResult);
      });
    });
  });

  describe('PointRulesController', () => {
    describe('getAllPointRules', () => {
      it('should get all point rules', async () => {
        const query: PointsConfigQueryParamsDto = {
          page: 1,
          limit: 10,
          search: '',
          order: 'asc',
          all: true,
        };

        const expectedResult = {
          data: [
            {
              id: 'rule-1',
              module: 'Test',
              action: 'Create',
              points_config_id: 'config-1',
              frequency: 'once_a_day',
            },
          ],
          total: 1,
        };

        mockPointSystemService.getAllPointRules.mockResolvedValue(
          expectedResult,
        );

        const result = await pointRulesController.getAllPointRules(query);

        expect(mockPointSystemService.getAllPointRules).toHaveBeenCalledWith(
          query,
        );
        expect(result).toEqual(expectedResult);
      });
    });

    describe('createPointRules', () => {
      it('should create a point rule', async () => {
        const pointRuleDto: PointRuleDto = {
          module: 'Test',
          action: 'Create',
          points_config_id: 'config-1',
          frequency: 'once_a_day',
        };

        const expectedResult = {
          id: 'rule-1',
          ...pointRuleDto,
        };

        mockPointSystemService.createPointRule.mockResolvedValue(
          expectedResult,
        );

        const result =
          await pointRulesController.createPointRules(pointRuleDto);

        expect(mockPointSystemService.createPointRule).toHaveBeenCalledWith(
          pointRuleDto,
        );
        expect(result).toEqual(expectedResult);
      });
    });
  });

  describe('PointRewardController', () => {
    describe('rewardPoints', () => {
      it('should reward points', async () => {
        const pointsLogDto: PointsLogDto = {
          student_id: 'student-1',
          point_rule_id: 'rule-1',
          points: 10,
          description: 'Test points reward',
        };

        const expectedResult = {
          id: 'log-1',
          ...pointsLogDto,
        };

        mockPointSystemService.rewardPoints.mockResolvedValue(expectedResult);

        const result = await pointRewardController.rewardPoints(pointsLogDto);

        expect(mockPointSystemService.rewardPoints).toHaveBeenCalledWith(
          pointsLogDto,
        );
        expect(result).toEqual(expectedResult);
      });
    });
  });
});
