import { querySchema } from '@/common/dto/query-params.dto';
import {
  insertPointsLogs,
  insertPointsConfig,
  insertPointRules,
} from '@/db/schema/points_system';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

export const pointsConfigSortKeys: Record<string, any> = {
  id: 'id',
  points: 'points',
  points_name: 'points_name',
  points_value: 'points_value',
  created_at: 'created_at',
} as const;
export const pointsLogsSortKeys: Record<string, any> = {
  id: 'id',
  student_id: 'student_id',
  point_id: 'point_id',
  points: 'points',
  created_at: 'created_at',
} as const;

export const pointRulesSortKeys: Record<string, any> = {
  id: 'id',
  rule_name: 'rule_name',
  rule_value: 'rule_value',
  description: 'description',
  created_at: 'created_at',
} as const;

export const extendedPointsLogsSchema = insertPointsLogs
  .extend({
    student_id: z.string(),
    point_id: z.string(),
    points: z.number(),
    description: z.string(),
  })
  .omit({ point_id: true });
export const pointLogsQueryParamSchema = querySchema.extend({
  sort: z.nativeEnum(pointsConfigSortKeys).default('id'),
  student_id: z.number().optional(),
  point_id: z.string().optional(),
  points: z.number().optional(),
  description: z.string().optional(),
});

export const pointsConfigQueryParamSchema = querySchema.extend({
  sort: z.nativeEnum(pointsConfigSortKeys).default('id'),
  institutionId: z.string().optional(),
  points_name: z.string().optional(),
  points_value: z.number().optional(),
});
export const pointRulesQueryParamSchema = querySchema.extend({
  sort: z.nativeEnum(pointsConfigSortKeys).default('id'),
  rule_name: z.string().optional(),
  module: z.string().optional(),
  action: z.string().optional(),
  rule_value: z.number().optional(),
  description: z.string().optional(),
});

export const extendedPointsConfigSchema = insertPointsConfig.extend({
  points_name: z.string(),
  points_value: z.number(),
  description: z.string().optional(),
  created_by: z.string().optional(),
});

export class PointsLogsQueryParamsDto extends createZodDto(
  pointsConfigQueryParamSchema,
) {}
export class PointsConfigQueryParamsDto extends createZodDto(
  pointsConfigQueryParamSchema,
) {}

export class PointRulesQueryParamsDto extends createZodDto(
  pointRulesQueryParamSchema,
) {}

export type PointsLogsParams = z.infer<typeof extendedPointsLogsSchema>;
export type PointsConfigParams = z.infer<typeof extendedPointsConfigSchema>;

export class PointRuleDto extends createZodDto(insertPointRules) {}
export class PointConfigDto extends createZodDto(insertPointsConfig) {}
export class PointsLogDto extends createZodDto(insertPointsLogs) {}
