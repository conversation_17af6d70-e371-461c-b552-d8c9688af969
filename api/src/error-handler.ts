import { Logger } from '@nestjs/common';

/**
 * Sets up global error handlers for uncaught exceptions and unhandled promise rejections
 */
export function setupGlobalErrorHandlers() {
  const logger = new Logger('GlobalErrorHandler');
  const isDevelopment =
    process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local';

  // Handle uncaught exceptions
  process.on('uncaughtException', (error: Error) => {
    const formattedError = formatError(error);

    // In development, log detailed information
    if (isDevelopment) {
      logger.error('Uncaught Exception', {
        error: formattedError,
        processInfo: getProcessInfo(),
      });
    } else {
      // In production, log minimal information
      logger.error(`Uncaught Exception: ${error.message}`);
    }

    // Give the logger time to write before exiting
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason: any) => {
    const formattedError = formatError(reason);

    // In development, log detailed information
    if (isDevelopment) {
      logger.error('Unhandled Promise Rejection', {
        error: formattedError,
        processInfo: getProcessInfo(),
      });
    } else {
      // In production, log minimal information
      const message = reason instanceof Error ? reason.message : String(reason);
      logger.error(`Unhandled Promise Rejection: ${message}`);
    }

    // Don't exit the process for unhandled rejections
    // Just log them to help with debugging
  });

  // Log when the process is about to exit
  process.on('exit', (code) => {
    // Only log in development or for non-zero exit codes
    if (isDevelopment || code !== 0) {
      logger.log(`Process is about to exit with code: ${code}`);
    }
  });

  // Handle SIGTERM signal (e.g., from Docker, Kubernetes, etc.)
  process.on('SIGTERM', () => {
    logger.log('SIGTERM signal received. Graceful shutdown initiated.');

    // Give the logger time to write before exiting
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  });

  // Handle SIGINT signal (e.g., Ctrl+C)
  process.on('SIGINT', () => {
    logger.log('SIGINT signal received. Graceful shutdown initiated.');

    // Give the logger time to write before exiting
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  });

  // Only log setup in development
  if (isDevelopment) {
    logger.debug('Global error handlers have been set up');
  }
}

/**
 * Formats an error object for logging
 */
function formatError(error: any): Record<string, any> {
  const isDevelopment =
    process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local';

  if (!error) {
    return { message: 'Unknown error (null or undefined)' };
  }

  // If it's a string, convert to an error-like object
  if (typeof error === 'string') {
    return { message: error };
  }

  // In production, only include essential information
  if (!isDevelopment) {
    return {
      message: error.message || 'No error message',
      name: error.name || 'UnknownError',
      code: error.code,
      statusCode: error.statusCode,
    };
  }

  // In development, include detailed information
  // Extract common error properties
  const formattedError: Record<string, any> = {
    message: error.message || 'No error message',
    name: error.name || 'UnknownError',
    stack: error.stack,
  };

  // Add network-related properties if they exist
  if (error.code) formattedError.code = error.code;
  if (error.errno) formattedError.errno = error.errno;
  if (error.syscall) formattedError.syscall = error.syscall;
  if (error.address) formattedError.address = error.address;
  if (error.port) formattedError.port = error.port;

  // Add database-related properties if they exist
  if (error.query) formattedError.query = error.query;
  if (error.parameters) formattedError.parameters = error.parameters;
  if (error.detail) formattedError.detail = error.detail;
  if (error.schema) formattedError.schema = error.schema;
  if (error.table) formattedError.table = error.table;
  if (error.column) formattedError.column = error.column;

  // Add HTTP-related properties if they exist
  if (error.status) formattedError.status = error.status;
  if (error.statusCode) formattedError.statusCode = error.statusCode;
  if (error.response) formattedError.response = error.response;

  return formattedError;
}

/**
 * Gets information about the current process
 * Only includes essential information to reduce log size
 */
function getProcessInfo(): Record<string, any> {
  const isDevelopment =
    process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local';

  // Basic info for all environments
  const info: Record<string, any> = {
    pid: process.pid,
    nodeVersion: process.version,
  };

  // Add detailed info only in development
  if (isDevelopment) {
    info.ppid = process.ppid;
    info.platform = process.platform;
    info.arch = process.arch;

    // Only include essential memory usage metrics
    const mem = process.memoryUsage();
    info.memory = {
      heapUsed: Math.round(mem.heapUsed / 1024 / 1024) + 'MB',
      rss: Math.round(mem.rss / 1024 / 1024) + 'MB',
    };

    info.uptime = Math.round(process.uptime()) + 's';
  }

  return info;
}
