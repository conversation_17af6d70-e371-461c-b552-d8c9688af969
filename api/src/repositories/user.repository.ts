import { User, users } from '@/db/schema';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Injectable, Logger } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { normalizeEmail } from '../util/normalize-email';

type UserColumnKey = keyof Omit<User, 'student_profile'>;

@Injectable()
export class UserRepository {
  private readonly logger = new Logger(UserRepository.name);

  constructor(private drizzle: DrizzleService) {}

  async getUserByKey<T extends UserColumnKey>(
    key: T,
    value: string,
  ): Promise<User | undefined> {
    // Normalize email values for case-insensitive comparison
    const normalizedValue = key === 'email' ? normalizeEmail(value) : value;

    return this.drizzle.db.query.users.findFirst({
      where: eq(users[key], normalizedValue),
    });
  }

  /**
   * Get a user by email with case-insensitive matching
   * @param email The email address to search for
   * @returns The user if found, undefined otherwise
   */
  async getUserByEmail(email: string): Promise<User | undefined> {
    const normalizedEmail = normalizeEmail(email);

    return this.drizzle.db.query.users.findFirst({
      where: eq(users.email, normalizedEmail),
    });
  }

  /**
   * Validates that a user exists, is active, and not deleted
   * This method is commonly used across notification services and other modules
   * that need to verify user eligibility before performing operations
   *
   * @param userId The user ID to validate
   * @returns The user object with state and deleted fields if valid, null if not valid
   */
  async validateActiveUser(
    userId: string,
  ): Promise<{ state: string; deleted: boolean } | null> {
    try {
      const [user] = await this.drizzle.db
        .select({ state: users.state, deleted: users.deleted })
        .from(users)
        .where(eq(users.id, userId));

      // Check if user exists
      if (!user) {
        this.logger.warn(`User with ID ${userId} not found`);
        return null;
      }

      // Check if user is active and not deleted
      if (user.state !== 'active' || user.deleted) {
        this.logger.warn(
          `User ${userId} validation failed: user ${user.deleted ? 'deleted' : 'not active (state: ' + user.state + ')'}`,
        );
        return null;
      }

      return user;
    } catch (error: any) {
      this.logger.error(`Failed to validate user ${userId}:`, error?.stack);
      return null;
    }
  }
}
