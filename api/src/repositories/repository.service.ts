import { student_profiles, User, users } from '@/db/schema';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { eq, getTableColumns, inArray } from 'drizzle-orm';
import { PgTable } from 'drizzle-orm/pg-core';

@Injectable()
export class RepositoryService {
  constructor(private readonly drizzle: DrizzleService) {}
  private readonly logger = new Logger(RepositoryService.name);

  async getModelByKey<T>(
    table: PgTable<any>,
    key: keyof T,
    value: T[keyof T],
  ): Promise<T | null> {
    const query = this.drizzle.db
      .select()
      .from(table)
      .where(eq((table as any)[key], value));

    const result = await query.execute();
    return result.length > 0 ? (result[0] as T) : null;
  }

  async getUserByKey(key: keyof Omit<User, 'student_profile'>, value: string) {
    const [user] = await this.drizzle.db
      .select({
        ...getTableColumns(users),
        student_profile: {
          ...getTableColumns(student_profiles),
        },
      })
      .from(users)
      .where(eq(users[key], value))
      .leftJoin(student_profiles, eq(users.id, student_profiles.user_id));
    return user;
  }

  async batchDelete<T>(
    table: PgTable<any>,
    key: keyof T,
    values: (string | number)[],
    batchSize: number = 1000,
  ) {
    if (!values.length) {
      this.logger.warn('No values to delete');
      return;
    }
    // Check for non-existent IDs
    const nonExistentIds = await this.findNonExistentIds(table, values, key);

    if (nonExistentIds.length > 0) {
      this.logger.warn(
        'The following IDs do not exist in the database:',
        nonExistentIds,
      );
      throw new BadRequestException(
        `Some IDs do not exist: ${nonExistentIds.join(', ')}`,
      );
    }

    for (let i = 0; i < values.length; i += batchSize) {
      const batch = values.slice(i, i + batchSize);
      try {
        return await this.drizzle.db
          .delete(table)
          .where(eq((table as any)[key], batch))
          .execute();
      } catch (error: any) {
        this.logger.error(`Error deleting batch ${i / batchSize + 1}`, error);
        throw error;
      }
    }
  }

  async findNonExistentIds<T>(
    table: PgTable<any>,
    ids: (string | number)[],
    idColumn: keyof T = 'id' as keyof T,
  ): Promise<(string | number)[]> {
    if (!ids.length) {
      this.logger.warn('No IDs to check');
      return [];
    }

    const existingIds = await this.drizzle.db
      .select({ id: (table as any)[idColumn] })
      .from(table)
      .where(inArray((table as any)[idColumn], ids));

    const existingIdSet = new Set(existingIds.map((row) => row.id));

    const nonExistentIds = ids.filter((id) => !existingIdSet.has(id));

    return nonExistentIds;
  }
}
