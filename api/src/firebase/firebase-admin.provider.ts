import * as admin from 'firebase-admin';
import { Injectable, Logger } from '@nestjs/common';
import { EnvConfig } from '@app/shared/dto/env-config.dto';

@Injectable()
export class FirebaseAdminService {
  private firebaseAdminInstance: admin.app.App | null = null;
  private readonly logger = new Logger(FirebaseAdminService.name);

  constructor(private readonly envConfig: EnvConfig) {
    this.initializeFirebaseAdmin();
  }

  private async initializeFirebaseAdmin() {
    if (!this.firebaseAdminInstance) {
      try {
        const serviceAccountBase64 =
          this.envConfig.FIREBASE_SERVICE_ACCOUNT_BASE64;

        if (!serviceAccountBase64) {
          this.logger.warn(
            'Firebase service account credentials not available',
          );
          this.logger.warn('Firebase functionality will be disabled');
          return;
        }

        // Validate that it's not a placeholder value
        if (
          serviceAccountBase64 === 'test' ||
          serviceAccountBase64.length < 100
        ) {
          throw new Error(
            'Firebase service account contains invalid placeholder value. ' +
              'Please update FIREBASE_SERVICE_ACCOUNT_BASE64 in Parameter Store with a valid base64-encoded Firebase service account JSON.',
          );
        }

        let serviceAccountJson: string;
        try {
          serviceAccountJson = Buffer.from(
            serviceAccountBase64,
            'base64',
          ).toString('utf8');
        } catch (decodeError) {
          throw new Error(
            'Failed to decode Firebase service account base64. ' +
              'Please ensure FIREBASE_SERVICE_ACCOUNT_BASE64 contains valid base64-encoded JSON.',
          );
        }

        this.logger.log('Successfully decoded Firebase credentials');

        let serviceAccount: any;
        try {
          serviceAccount = JSON.parse(serviceAccountJson);
        } catch (parseError: any) {
          throw new Error(
            'Failed to parse Firebase service account JSON. ' +
              'Please ensure the base64-decoded content is valid JSON. ' +
              `Parse error: ${parseError?.message || 'Unknown parsing error'}`,
          );
        }

        // Validate required fields
        const requiredFields = [
          'type',
          'project_id',
          'private_key',
          'client_email',
        ];
        const missingFields = requiredFields.filter(
          (field) => !serviceAccount[field],
        );

        if (missingFields.length > 0) {
          throw new Error(
            `Firebase service account JSON is missing required fields: ${missingFields.join(', ')}. ` +
              'Please ensure you have a complete Firebase service account JSON.',
          );
        }

        if (serviceAccount.type !== 'service_account') {
          throw new Error(
            `Invalid Firebase service account type: ${serviceAccount.type}. ` +
              'Expected "service_account".',
          );
        }

        this.logger.log(`Firebase project_id: ${serviceAccount.project_id}`);
        this.logger.log(
          `Firebase client_email: ${serviceAccount.client_email}`,
        );

        this.firebaseAdminInstance = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
        });

        this.logger.log('Firebase Admin SDK initialized successfully');
      } catch (error: any) {
        this.logger.error(
          'Failed to initialize Firebase Admin SDK:',
          error.message,
        );

        // Provide helpful error context
        if (error.message.includes('placeholder')) {
          this.logger.error(
            '🔥 Firebase Configuration Error: Invalid placeholder value detected',
          );
          this.logger.error('📝 To fix this issue:');
          this.logger.error(
            '   1. Download your Firebase service account JSON from Firebase Console',
          );
          this.logger.error(
            '   2. Convert to base64: cat service-account.json | base64',
          );
          this.logger.error(
            '   3. Update Parameter Store: /reach/prod/api/FIREBASE_SERVICE_ACCOUNT_BASE64',
          );
        }

        throw new Error(
          `Firebase Admin SDK initialization failed: ${error.message}`,
        );
      }
    }
  }

  getAdminInstance(): admin.app.App | null {
    if (!this.firebaseAdminInstance) {
      this.initializeFirebaseAdmin().catch((error) => {
        this.logger.error(
          'Failed to initialize Firebase Admin SDK on demand:',
          error,
        );
      });
    }
    return this.firebaseAdminInstance;
  }

  isInitialized(): boolean {
    return this.firebaseAdminInstance !== null;
  }
}
