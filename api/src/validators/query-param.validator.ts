import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';

@Injectable()
export class queryParamsValidator implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    if (typeof value === 'string' && value.trim() === '') {
      throw new BadRequestException(
        `${metadata.data} should not be an empty string`,
      );
    }
    if (
      (metadata.type === 'query' && metadata.data === 'page') ||
      metadata.data === 'limit'
    ) {
      const parsedValue = parseInt(value, 10);
      if (isNaN(parsedValue) || parsedValue <= 0) {
        throw new BadRequestException(
          `${metadata.data} should be a positive number`,
        );
      }
      return parsedValue;
    }
    return value;
  }
}
