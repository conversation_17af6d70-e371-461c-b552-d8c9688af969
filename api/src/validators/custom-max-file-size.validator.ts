import { FileValidator } from '@nestjs/common';
import { ParseFilePipe, PipeTransform } from '@nestjs/common';

export class CustomMaxFileSizeValidator extends FileValidator<{
  maxSize: number;
}> {
  constructor(
    protected override readonly validationOptions: { maxSize: number },
  ) {
    super(validationOptions);
  }

  isValid(file: Express.Multer.File): boolean {
    return file.size <= this.validationOptions.maxSize;
  }

  buildErrorMessage(): string {
    return `File is too large. Max file size is ${this.validationOptions.maxSize / (1024 * 1024)}MB`;
  }
}

export class ParseFilesPipe
  extends ParseFilePipe
  implements PipeTransform<Express.Multer.File[]>
{
  override async transform(
    files: Express.Multer.File[] | { [key: string]: Express.Multer.File[] },
  ) {
    for (const file of Object.values(files).flat()) await super.transform(file);

    return files;
  }
}
