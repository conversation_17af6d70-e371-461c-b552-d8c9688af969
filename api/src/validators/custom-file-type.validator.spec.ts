import { CustomFileUploadValidator } from './custom-file-type.validator';
import { allowedMimeTypes } from '@app/shared/constants/mimetypes.constants';

describe('CustomFileUploadValidator', () => {
  let validator: CustomFileUploadValidator;

  beforeEach(() => {
    validator = new CustomFileUploadValidator({ isRequired: true });
  });

  it('should return false if file is required but not provided', () => {
    expect(validator.isValid(null as any)).toBe(false);
  });

  it('should return false if mimetype is not allowed', () => {
    const file = {
      mimetype: 'application/unknown',
      buffer: Buffer.from(''),
    } as Express.Multer.File;

    expect(validator.isValid(file)).toBe(false);
  });

  it('should return false if file content is invalid', () => {
    const file = {
      mimetype: 'image/png',
      buffer: Buffer.from('invalidcontent'),
    } as Express.Multer.File;

    expect(validator.isValid(file)).toBe(false);
  });

  it('should return true if file is valid', () => {
    const file = {
      mimetype: 'image/png',
      buffer: Buffer.from('89504E47', 'hex'), // Valid PNG signature
    } as Express.Multer.File;

    expect(validator.isValid(file)).toBe(true);
  });

  it('should return true for valid CSV file', () => {
    const file = {
      mimetype: 'text/csv',
      buffer: Buffer.from('494433', 'hex'), // Valid text signature
    } as Express.Multer.File;

    expect(validator.isValid(file)).toBe(true);
  });

  it('should return the correct error message', () => {
    expect(validator.buildErrorMessage()).toBe('Invalid file upload');
  });

  it('should return true for allowed mimetype', () => {
    const mimetype = allowedMimeTypes[0];
    expect((validator as any).isAllowedMimeType(mimetype)).toBe(true);
  });

  it('should return false for disallowed mimetype', () => {
    const mimetype = 'application/unknown';
    expect((validator as any).isAllowedMimeType(mimetype)).toBe(false);
  });

  it('should return true for valid file content', () => {
    const buffer = Buffer.from('89504E47', 'hex'); // Valid PNG signature
    const mimetype = 'image/png';
    expect((validator as any).hasValidContent(buffer, mimetype)).toBe(true);
  });

  it('should return false for invalid file content', () => {
    const buffer = Buffer.from('invalidcontent');
    const mimetype = 'image/png';
    expect((validator as any).hasValidContent(buffer, mimetype)).toBe(false);
  });

  it('should return true for valid text file content', () => {
    const buffer = Buffer.from('494433', 'hex'); // Valid text signature
    expect((validator as any).isTextFile(buffer)).toBe(true);
  });
});
