import { FileValidator } from '@nestjs/common';
import {
  allowedMimeTypes,
  fileSignatures,
} from '@app/shared/constants/mimetypes.constants';

export class CustomFileUploadValidator extends FileValidator<{
  isRequired: boolean;
}> {
  constructor(
    protected override readonly validationOptions: { isRequired: boolean },
  ) {
    super(validationOptions);
  }

  isValid(file: Express.Multer.File): boolean {
    if (!file && this.validationOptions.isRequired) {
      return false;
    }

    const mimetype = file.mimetype;
    if (!this.isAllowedMimeType(mimetype)) {
      return false;
    }

    if (!this.hasValidContent(file.buffer, mimetype)) {
      return false;
    }

    return true;
  }

  buildErrorMessage(): string {
    return `Invalid file upload`;
  }

  private isAllowedMimeType(mimetype: string): boolean {
    return allowedMimeTypes.includes(mimetype);
  }

  private hasValidContent(buffer: Buffer, mimetype: string): boolean {
    const fileSignature = buffer.toString('hex', 0, 4).toUpperCase();
    const validSignatures =
      fileSignatures[mimetype as keyof typeof fileSignatures];

    if (mimetype === 'text/csv') {
      // For CSV files, we can check for common text file signatures or simply return true
      return this.isTextFile(buffer);
    }

    return (
      validSignatures?.some((signature) =>
        fileSignature.startsWith(signature),
      ) ?? false
    );
  }

  private isTextFile(buffer: Buffer): boolean {
    // Check for common text file signatures or simply return true for leniency
    const textSignatures = ['494433', 'EFBBBF'];
    const fileSignature = buffer.toString('hex', 0, 4).toUpperCase();

    return (
      textSignatures.some((signature) => fileSignature.startsWith(signature)) ||
      true
    );
  }
}
