import {
  Body,
  Controller,
  Delete,
  Logger,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UseRoles } from 'nest-access-control';
import { RoleGuard } from '@/guards/role.guard';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { User } from '@/guards/user.decorator';
import { EventEngagementService } from '../services/event-engagement.service';
import { EventEngagementDto } from '../dto/event-engagement.dto';
import { ZodSerializerDto } from 'nestjs-zod';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { PostEngagementRoutes } from '@app/shared/constants/post.constants';

@Controller({ version: '1', path: 'posts' })
@ApiTags('Event Engagements')
export class EventEngagementController {
  private readonly logger = new Logger(EventEngagementController.name);

  constructor(
    private readonly eventEngagementService: EventEngagementService,
  ) {}

  /**
   * RSVP to an event with notification preferences
   */
  @Post(PostEngagementRoutes.RSVP)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'posts', action: 'update', possession: 'any' })
  @ApiOperation({ summary: 'RSVP to an event with notification preferences' })
  @ApiResponse({
    status: 200,
    description: 'RSVP successful',
  })
  @ZodSerializerDto(EventEngagementDto)
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async rsvpToEvent(
    @Param('id', new CustomParseUUIDPipe()) postId: string,
    @User() user: any,
    @Body() data: EventEngagementDto,
  ) {
    try {
      const result = await this.eventEngagementService.rsvpToEvent(
        user.id,
        user.student_profile.id,
        postId,
        data,
      );
      return {
        success: true,
        message: 'RSVP successful',
        data: result,
      };
    } catch (error) {
      this.logger.error("Error RSVP'ing to event", error);
      throw error;
    }
  }

  /**
   * Cancel RSVP to an event
   */
  @Delete(PostEngagementRoutes.RSVP)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'posts', action: 'delete', possession: 'any' })
  @ApiOperation({ summary: 'Cancel RSVP to an event' })
  @ApiResponse({
    status: 200,
    description: 'RSVP cancelled successfully',
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async cancelRsvp(
    @Param('id', new CustomParseUUIDPipe()) postId: string,
    @User() user: any,
  ) {
    try {
      await this.eventEngagementService.cancelRsvp(
        user.id,
        user.student_profile.id,
        postId,
      );
      return {
        success: true,
        message: 'RSVP cancelled successfully',
      };
    } catch (error) {
      this.logger.error('Error cancelling RSVP', error);
      throw error;
    }
  }
}
