import {
  opportunity,
  posts,
  post_types,
  postCountries,
  postInstitutions,
  post_statuses,
  postImages,
} from '@/db/schema/posts';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { PostRepository } from '@/repositories/post.repository';

import { eq } from 'drizzle-orm';
import { PostServiceMessages } from '@app/shared/constants/post.constants';
import { opportunityDto } from '../dto/opportunity.dto';
import { PostService } from '../post.service';
import { CacheService } from '@app/shared/redis/cache.service';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';
import {
  generateOpportunityKey,
  invalidateOpportunityCaches,
  invalidatePostCachesForOpportunity,
} from '../utils/unified-cache.utils';
import { type User as UserDecoratorType } from '@/db/schema';
import { user_roles } from '@/db/schema/users';

@Injectable()
export class OpportunityService {
  private readonly logger = new Logger(OpportunityService.name);
  private readonly OPPORTUNITY_CACHE_PREFIX = CACHE_PREFIXES.OPPORTUNITY;
  private readonly POST_CACHE_PREFIX = CACHE_PREFIXES.POST;
  private readonly CACHE_TTL = CACHE_TTL.ONE_DAY;

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly postRepository: PostRepository,
    private readonly postService: PostService,
    private readonly cacheService: CacheService,
  ) {}

  async createOpportunity(
    data: opportunityDto,
    userId: string,
    attachements: Express.Multer.File[] = [],
  ) {
    const { countries, institutions, notify_users, ...sanitizedData } = data;

    if (countries.length) {
      await this.postService.checkNonExistentCountries(countries);
    }

    if (institutions.length) {
      await this.postService.checkNonExistentInstitutions(institutions);
    }

    const [createdOpportunityData] = await this.drizzle.db.transaction(
      async (tx) => {
        const [createdPost] = await this.postRepository.createPost({
          ...sanitizedData,
          postedBy: userId,
          status: attachements.length ? post_statuses.PENDING : data.status,
          type: post_types.OPPORTUNITY,
          notify_users: notify_users || false,
          scheduledAt: sanitizedData.scheduledAt ?? null,
        });

        if (!createdPost) {
          throw new InternalServerErrorException(
            'Failed to create opportunity',
          );
        }

        countries.length &&
          (await tx.insert(postCountries).values(
            countries?.map((countryId: string) => ({
              countryId,
              postId: createdPost?.id,
            })),
          ));

        institutions.length &&
          (await tx.insert(postInstitutions).values(
            institutions?.map((institutionId: string) => ({
              institutionId,
              postId: createdPost?.id,
            })),
          ));
        attachements.length &&
          (await this.postService.uploadPostAttachments(
            createdPost.id,
            attachements,
            data.status,
          ));

        const createdOpportunity = await tx
          .insert(opportunity)
          .values({
            postId: createdPost?.id,
            eligibility: sanitizedData.eligibility,
            applicationUrl: sanitizedData.applicationUrl,
            startDate: sanitizedData.startDate,
            endDate: sanitizedData.endDate,
            startTime: sanitizedData.startTime,
            endTime: sanitizedData.endTime,
          })
          .returning();

        return createdOpportunity;
      },
    );
    if (!createdOpportunityData) {
      throw new InternalServerErrorException('Failed to create opportunity');
    }

    // Send notification if needed
    if (data.notificationDeliverType || notify_users) {
      try {
        // Generate a unique notification ID to prevent duplicate jobs
        const notificationId = `opportunity-notification-${createdOpportunityData.postId}-${Date.now()}`;
        this.logger.log(
          `Creating notification with ID ${notificationId} for opportunity ${createdOpportunityData.postId}`,
        );

        // Get the full post data
        const [postData] = await this.drizzle.db
          .select()
          .from(posts)
          .where(eq(posts.id, createdOpportunityData.postId));

        if (postData) {
          // Use the PostService's sendPostNotifications method with the unique ID
          await this.postService.sendPostNotifications(postData, data);
        }
      } catch (notificationError: any) {
        // Log the error but don't fail the opportunity creation
        this.logger.error(
          `Failed to send notifications for opportunity ${createdOpportunityData.postId}: ${notificationError.message}`,
          notificationError.stack,
        );
      }
    }

    try {
      // Cache the created opportunity
      await this.cacheService.set(
        generateOpportunityKey(
          this.cacheService,
          createdOpportunityData.postId,
          this.OPPORTUNITY_CACHE_PREFIX,
        ),
        createdOpportunityData,
        this.CACHE_TTL,
      );

      // Invalidate relevant caches in parallel
      await Promise.all([
        invalidateOpportunityCaches(
          this.cacheService,
          createdOpportunityData.postId,
          this.OPPORTUNITY_CACHE_PREFIX,
        ),
        invalidatePostCachesForOpportunity(
          this.cacheService,
          createdOpportunityData.postId,
          this.POST_CACHE_PREFIX,
        ),
      ]);
    } catch (error) {
      this.logger.warn('Failed to update opportunity cache', error);
    }

    return createdOpportunityData;
  }

  async updateOpportunity(
    {
      data,
      attachments,
    }: {
      data: opportunityDto;
      attachments: Express.Multer.File[];
    },
    id: string,
  ) {
    const { countries, institutions, ...opportunityData } = data;

    if (countries.length) {
      await this.postService.checkNonExistentCountries(countries);
    }

    if (institutions.length) {
      await this.postService.checkNonExistentInstitutions(institutions);
    }
    const [existingPost] = await this.drizzle.db
      .select()
      .from(posts)
      .where(eq(posts.id, id));
    if (!existingPost)
      throw new NotFoundException(PostServiceMessages.PostNotFound);

    const updatedOpportunityData = await this.drizzle.db.transaction(
      async (tx) => {
        const [updatedPost] = await this.postRepository.updatePost(
          {
            description: opportunityData.description,
            title: opportunityData.title,
            type: post_types.OPPORTUNITY,
            status: attachments.length
              ? post_statuses.PENDING
              : opportunityData.status,
            scheduledAt: opportunityData.scheduledAt ?? null,
          },
          id,
        );

        if (!updatedPost) {
          throw new InternalServerErrorException(
            'Failed to update opportunity',
          );
        }
        await tx.delete(postCountries).where(eq(postCountries.postId, id));
        await tx
          .delete(postInstitutions)
          .where(eq(postInstitutions.postId, id));
        await tx.delete(postImages).where(eq(postImages.postId, id));

        countries.length &&
          (await tx.insert(postCountries).values(
            countries?.map((countryId: string) => ({
              countryId,
              postId: id,
            })),
          ));

        institutions.length &&
          (await tx.insert(postInstitutions).values(
            institutions?.map((institutionId: string) => ({
              institutionId,
              postId: id,
            })),
          ));

        const updatedOpportunity = await tx
          .update(opportunity)
          .set({
            eligibility: opportunityData.eligibility,
            applicationUrl: opportunityData.applicationUrl,
            startDate: opportunityData.startDate,
            endDate: opportunityData.endDate,
          })
          .where(eq(opportunity.postId, id))
          .returning();

        // Upload attachments if any
        if (attachments.length) {
          await this.postService.uploadPostAttachments(
            id,
            attachments,
            opportunityData.status,
          );
        }

        return updatedOpportunity;
      },
    );
    if (!updatedOpportunityData)
      throw new InternalServerErrorException('Failed to update opportunity');

    try {
      // Invalidate relevant caches in parallel
      await Promise.all([
        invalidateOpportunityCaches(
          this.cacheService,
          id,
          this.OPPORTUNITY_CACHE_PREFIX,
        ),
        invalidatePostCachesForOpportunity(
          this.cacheService,
          id,
          this.POST_CACHE_PREFIX,
        ),
      ]);

      // Update the specific opportunity cache with new data
      if (updatedOpportunityData.length > 0) {
        await this.cacheService.set(
          generateOpportunityKey(
            this.cacheService,
            id,
            this.OPPORTUNITY_CACHE_PREFIX,
          ),
          updatedOpportunityData[0],
          this.CACHE_TTL,
        );
      }
    } catch (error) {
      this.logger.warn('Failed to update opportunity caches', error);
    }

    return updatedOpportunityData;
  }

  /**
   * Get opportunity by ID with proper access control (cached for 1 day)
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.OPPORTUNITY,
    ttl: CACHE_TTL.ONE_DAY,
    keyGenerator: (args) => [args[0], args[1]?.id ?? 'anonymous'],
    condition: (args) => !!args[0],
  })
  async getOpportunityById(id: string, user: UserDecoratorType) {
    this.logger.debug(`Fetching opportunity ${id} from database (not cache)`);

    const opportunityData = await this.drizzle.db.query.opportunity.findFirst({
      where: eq(opportunity.id, id),
      with: {
        post: {
          with: {
            postedBy: {
              with: {
                profile: {
                  columns: {
                    id: true,
                    email: true,
                    name: true,
                  },
                },
                student_profile: {
                  columns: {
                    id: true,
                    first_name: true,
                    last_name: true,
                  },
                },
              },
            },
            countries: {
              with: {
                country: true,
              },
            },
            institutions: {
              with: {
                institution: true,
              },
            },
            postEngagements: true,
            images: true,
            club: true,
          },
        },
      },
    });

    if (!opportunityData) {
      throw new NotFoundException(`Opportunity with ID ${id} not found`);
    }

    // Apply access control checks
    this.validateOpportunityAccess(opportunityData, user);

    return opportunityData;
  }

  /**
   * Validates if a user has access to view a specific opportunity
   */
  private validateOpportunityAccess(
    opportunityData: any,
    user: UserDecoratorType,
  ): void {
    const post = opportunityData.post;

    // Super admins can access all opportunities
    if (user.role === user_roles.SUPER_ADMIN) {
      return;
    }

    // Check if opportunity is in draft or pending status
    if (
      post.status === post_statuses.DRAFT ||
      post.status === post_statuses.PENDING
    ) {
      // Only the creator or admins can view draft/pending opportunities
      if (
        post.postedBy !== user.id &&
        user.role !== user_roles.ADMIN &&
        user.role !== user_roles.STUDENT_ADMIN
      ) {
        throw new ForbiddenException(
          'You do not have permission to view this opportunity',
        );
      }
    }

    // Check if opportunity is disabled
    if (post.disabled) {
      // Only admins can view disabled opportunities
      const allowedRoles = [
        user_roles.ADMIN,
        user_roles.STUDENT_ADMIN,
        user_roles.SUPER_ADMIN,
      ];
      if (!allowedRoles.includes(user.role as any)) {
        throw new ForbiddenException('This opportunity is not available');
      }
    }

    // For students, check institution and country restrictions
    if (user.role === user_roles.STUDENT && user.student_profile) {
      const userInstitutionId = user.student_profile.institution_id;
      const userCountryId = user.student_profile.country_id;

      // If opportunity has specific institution restrictions
      if (post.institutions && post.institutions.length > 0) {
        const allowedInstitutions = post.institutions.map(
          (inst: any) => inst.institution.id,
        );
        if (!allowedInstitutions.includes(userInstitutionId)) {
          throw new ForbiddenException(
            'This opportunity is not available for your institution',
          );
        }
      }

      // If opportunity has specific country restrictions
      if (post.countries && post.countries.length > 0) {
        const allowedCountries = post.countries.map(
          (country: any) => country.country.id,
        );
        if (!allowedCountries.includes(userCountryId)) {
          throw new ForbiddenException(
            'This opportunity is not available in your country',
          );
        }
      }
    }
  }

  /**
   * Get all opportunities with proper access control and filtering
   */
  async getAllOpportunities(user: UserDecoratorType, queryParams: any) {
    // Use the existing PostService.getPosts method with type filter for opportunities
    return await this.postService.getPosts(user, {
      ...queryParams,
      type: 'opportunity',
    });
  }
}
