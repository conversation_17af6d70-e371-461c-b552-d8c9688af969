import { Injectable, Logger } from '@nestjs/common';
import { SelectionNotificationService } from '@/notification/selection-notification.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { opportunity, posts } from '@/db/schema/posts';
import { eq } from 'drizzle-orm';

@Injectable()
export class OpportunitySelectionService {
  private readonly logger = new Logger(OpportunitySelectionService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly selectionNotificationService: SelectionNotificationService,
  ) {}

  /**
   * Send notification to a user selected for an opportunity
   * @param userId User ID of the selected user
   * @param opportunityId ID of the opportunity
   */
  async notifyUserOfSelection(
    userId: string,
    opportunityId: string,
  ): Promise<string> {
    try {
      // Get opportunity details
      const [opportunityData] = await this.drizzle.db
        .select({
          post: posts,
          opportunity: opportunity,
        })
        .from(opportunity)
        .innerJoin(posts, eq(posts.id, opportunity.postId))
        .where(eq(opportunity.postId, opportunityId));

      if (!opportunityData) {
        throw new Error(`Opportunity with ID ${opportunityId} not found`);
      }

      const { post, opportunity: opportunityDetails } = opportunityData;

      // Format dates if provided
      let startDate: Date | undefined;
      let endDate: Date | undefined;

      if (opportunityDetails.startDate) {
        startDate = new Date(opportunityDetails.startDate);
      }

      if (opportunityDetails.endDate) {
        endDate = new Date(opportunityDetails.endDate);
      }

      // Calculate confirmation deadline (e.g., 3 days from now)
      const confirmDeadline = new Date();
      confirmDeadline.setDate(confirmDeadline.getDate() + 3);

      // Send notification
      const emailJobId =
        await this.selectionNotificationService.sendOpportunitySelectionNotification(
          userId,
          opportunityId,
          post.title,
          post.description,
          startDate,
          endDate,
          confirmDeadline,
          opportunityDetails.applicationUrl || undefined,
        );

      this.logger.log(
        `Sent opportunity selection notification to user ${userId} for opportunity ${opportunityId}`,
      );
      return emailJobId;
    } catch (error: any) {
      this.logger.error(
        `Failed to send opportunity selection notification: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send notifications to multiple users selected for an opportunity
   * @param userIds Array of user IDs of selected users
   * @param opportunityId ID of the opportunity
   */
  async notifyUsersOfSelection(
    userIds: string[],
    opportunityId: string,
  ): Promise<string[]> {
    const results: string[] = [];
    const errors: any[] = [];

    for (const userId of userIds) {
      try {
        const jobId = await this.notifyUserOfSelection(userId, opportunityId);
        results.push(jobId);
      } catch (error: any) {
        errors.push({ userId, error: error.message });
        this.logger.error(
          `Failed to send opportunity selection notification to user ${userId}: ${error.message}`,
          error.stack,
        );
      }
    }

    if (errors.length > 0) {
      this.logger.warn(
        `Failed to send ${errors.length} out of ${userIds.length} opportunity selection notifications`,
      );
    }

    return results;
  }
}
