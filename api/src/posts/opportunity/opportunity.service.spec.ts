import { Test, TestingModule } from '@nestjs/testing';
import { OpportunityService } from './opportunity.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { PostRepository } from '@/repositories/post.repository';
import { PostService } from '../post.service';
import { CacheService } from '@app/shared/redis/cache.service';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { type User as UserDecoratorType } from '@/db/schema';
import { user_roles } from '@/db/schema/users';

describe('OpportunityService', () => {
  let service: OpportunityService;

  const mockDrizzleService = {
    db: {
      query: {
        opportunity: {
          findFirst: jest.fn(),
        },
      },
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      transaction: jest.fn(),
    },
  };

  const mockCacheService = {
    get: jest.fn().mockResolvedValue(null), // Always return null to bypass cache
    set: jest.fn().mockResolvedValue(undefined),
    del: jest.fn().mockResolvedValue(undefined),
    generateKey: jest
      .fn()
      .mockImplementation(
        (keys, prefix) =>
          `${prefix}:${Array.isArray(keys) ? keys.join(':') : keys}`,
      ),
  };

  const mockPostRepository = {
    createPost: jest.fn(),
    updatePost: jest.fn(),
    updateOpportunity: jest.fn(),
  };

  const mockPostService = {
    uploadPostAttachments: jest.fn(),
    sendPostNotifications: jest.fn(),
    checkNonExistentCountries: jest.fn(),
    checkNonExistentInstitutions: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OpportunityService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: PostRepository,
          useValue: mockPostRepository,
        },
        {
          provide: PostService,
          useValue: mockPostService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<OpportunityService>(OpportunityService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getOpportunityById', () => {
    const opportunityId = '123e4567-e89b-12d3-a456-426614174000';
    const mockUser: UserDecoratorType = {
      id: 'user-456',
      email: '<EMAIL>',
      role: user_roles.STUDENT,
      state: 'active',
      profile_pic_url: null,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      deleted: false,
      deleted_at: null,
      student_profile: {
        id: 'student-456',
        user_id: 'user-456',
        first_name: 'Student',
        last_name: 'User',
        other_name: null,
        username: 'student_user',
        country_id: 'country-123',
        date_of_birth: '1990-01-01',
        phone_number: '+1234567890',
        institution_id: 'institution-123',
        enrollment_date: 2020,
        graduation_date: 2024,
        degree: 'Bachelors',
        programme: 'ICT',
        github_profile: null,
        linkedin_profile: null,
        about: null,
        club_id: null,
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
        username_last_updated: '2024-01-01T00:00:00.000Z',
        deleted: false,
        deleted_at: null,
      },
    };

    const mockOpportunity = {
      id: opportunityId,
      eligibility: [1, 2, 3],
      applicationUrl: 'https://example.com/apply',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      startTime: '09:00:00',
      endTime: '17:00:00',
      postId: 'post-123',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      post: {
        id: 'post-123',
        title: 'Test Opportunity',
        description: 'Test opportunity description',
        status: 'active',
        disabled: false,
        type: 'opportunity',
        postedBy: 'user-123',
        countries: [],
        institutions: [],
        postEngagements: [],
        images: [],
        club: null,
      },
    };

    it('should return an opportunity when found and user has access', async () => {
      mockDrizzleService.db.query.opportunity.findFirst.mockResolvedValue(
        mockOpportunity,
      );

      const result = await service.getOpportunityById(opportunityId, mockUser);

      expect(result).toEqual(mockOpportunity);
      expect(
        mockDrizzleService.db.query.opportunity.findFirst,
      ).toHaveBeenCalledWith({
        where: expect.anything(),
        with: expect.objectContaining({
          post: expect.any(Object),
        }),
      });
    });

    it('should throw NotFoundException when opportunity not found', async () => {
      mockDrizzleService.db.query.opportunity.findFirst.mockResolvedValue(null);

      await expect(
        service.getOpportunityById(opportunityId, mockUser),
      ).rejects.toThrow(
        new NotFoundException(`Opportunity with ID ${opportunityId} not found`),
      );
      expect(
        mockDrizzleService.db.query.opportunity.findFirst,
      ).toHaveBeenCalledWith({
        where: expect.anything(),
        with: expect.objectContaining({
          post: expect.any(Object),
        }),
      });
    });

    it('should throw ForbiddenException when student tries to access draft opportunity', async () => {
      const draftOpportunity = {
        ...mockOpportunity,
        post: {
          ...mockOpportunity.post,
          status: 'draft',
          postedBy: 'different-user-id',
        },
      };
      mockDrizzleService.db.query.opportunity.findFirst.mockResolvedValue(
        draftOpportunity,
      );

      await expect(
        service.getOpportunityById(opportunityId, mockUser),
      ).rejects.toThrow(
        new ForbiddenException(
          'You do not have permission to view this opportunity',
        ),
      );
    });

    it('should throw ForbiddenException when student tries to access disabled opportunity', async () => {
      const disabledOpportunity = {
        ...mockOpportunity,
        post: {
          ...mockOpportunity.post,
          disabled: true,
        },
      };
      mockDrizzleService.db.query.opportunity.findFirst.mockResolvedValue(
        disabledOpportunity,
      );

      await expect(
        service.getOpportunityById(opportunityId, mockUser),
      ).rejects.toThrow(
        new ForbiddenException('This opportunity is not available'),
      );
    });

    it('should allow super admin to access any opportunity', async () => {
      const superAdminUser = { ...mockUser, role: user_roles.SUPER_ADMIN };
      const draftOpportunity = {
        ...mockOpportunity,
        post: {
          ...mockOpportunity.post,
          status: 'draft',
          disabled: true,
        },
      };
      mockDrizzleService.db.query.opportunity.findFirst.mockResolvedValue(
        draftOpportunity,
      );

      const result = await service.getOpportunityById(
        opportunityId,
        superAdminUser,
      );

      expect(result).toEqual(draftOpportunity);
    });

    it('should throw ForbiddenException when opportunity is restricted by institution', async () => {
      const restrictedOpportunity = {
        ...mockOpportunity,
        post: {
          ...mockOpportunity.post,
          institutions: [
            {
              institution: {
                id: 'different-institution-id',
                name: 'Different Institution',
              },
            },
          ],
        },
      };
      mockDrizzleService.db.query.opportunity.findFirst.mockResolvedValue(
        restrictedOpportunity,
      );

      await expect(
        service.getOpportunityById(opportunityId, mockUser),
      ).rejects.toThrow(
        new ForbiddenException(
          'This opportunity is not available for your institution',
        ),
      );
    });

    it('should throw ForbiddenException when opportunity is restricted by country', async () => {
      const restrictedOpportunity = {
        ...mockOpportunity,
        post: {
          ...mockOpportunity.post,
          countries: [
            {
              country: {
                id: 'different-country-id',
                name: 'Different Country',
              },
            },
          ],
        },
      };
      mockDrizzleService.db.query.opportunity.findFirst.mockResolvedValue(
        restrictedOpportunity,
      );

      await expect(
        service.getOpportunityById(opportunityId, mockUser),
      ).rejects.toThrow(
        new ForbiddenException(
          'This opportunity is not available in your country',
        ),
      );
    });

    it('should have getOpportunityById method defined', () => {
      expect(service.getOpportunityById).toBeDefined();
      expect(typeof service.getOpportunityById).toBe('function');
    });
  });
});
