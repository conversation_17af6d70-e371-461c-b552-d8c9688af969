import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  events,
  opportunity,
  post_statuses,
  posts,
  postImages,
} from '@/db/schema';
import { and, eq, sql, lt, notExists } from 'drizzle-orm';
import { CacheService } from '@app/shared/redis/cache.service';
import { CACHE_PREFIXES } from '@app/shared/constants/cache.constant';
import {
  invalidatePostCaches,
  invalidateEventCaches,
  invalidateOpportunityCaches,
} from '@/posts/utils/unified-cache.utils';

@Injectable()
export class PostScheduler {
  constructor(
    private drizzle: DrizzleService,
    private cacheService: CacheService,
  ) {}
  private readonly logger = new Logger(PostScheduler.name);

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async updatePostStatus() {
    try {
      // Get expired events before updating
      const expiredEvents = await this.drizzle.db
        .select({ postId: posts.id })
        .from(posts)
        .innerJoin(events, eq(posts.id, events.postId))
        .where(
          and(
            sql`DATE(${events.endDate}) < CURRENT_DATE`,
            eq(posts.status, post_statuses.ACTIVE),
          ),
        );

      // Get expired opportunities before updating
      const expiredOpportunities = await this.drizzle.db
        .select({ postId: posts.id })
        .from(posts)
        .innerJoin(opportunity, eq(posts.id, opportunity.postId))
        .where(
          and(
            sql`DATE(${opportunity.endDate}) < CURRENT_DATE`,
            eq(posts.status, post_statuses.ACTIVE),
          ),
        );

      // Update expired events
      if (expiredEvents.length > 0) {
        await this.drizzle.db
          .update(posts)
          .set({ status: post_statuses.EXPIRED })
          .from(events)
          .where(
            and(
              sql`DATE(${events.endDate}) < CURRENT_DATE`,
              eq(posts.id, events.postId),
              eq(posts.status, post_statuses.ACTIVE),
            ),
          );

        this.logger.log(`Updated ${expiredEvents.length} expired events`);
      }

      // Update expired opportunities
      if (expiredOpportunities.length > 0) {
        await this.drizzle.db
          .update(posts)
          .set({ status: post_statuses.EXPIRED })
          .from(opportunity)
          .where(
            and(
              sql`DATE(${opportunity.endDate}) < CURRENT_DATE`,
              eq(posts.id, opportunity.postId),
              eq(posts.status, post_statuses.ACTIVE),
            ),
          );

        this.logger.log(
          `Updated ${expiredOpportunities.length} expired opportunities`,
        );
      }

      // Invalidate caches for all affected posts
      const allExpiredPosts = [
        ...expiredEvents.map((e) => e.postId),
        ...expiredOpportunities.map((o) => o.postId),
      ];

      if (allExpiredPosts.length > 0) {
        await Promise.all([
          // Invalidate post caches
          ...allExpiredPosts.map((postId) =>
            invalidatePostCaches(
              this.cacheService,
              postId,
              CACHE_PREFIXES.POST,
            ),
          ),
          // Invalidate event caches for expired events
          ...expiredEvents.map((event) =>
            invalidateEventCaches(
              this.cacheService,
              event.postId,
              CACHE_PREFIXES.EVENT,
            ),
          ),
          // Invalidate opportunity caches for expired opportunities
          ...expiredOpportunities.map((opp) =>
            invalidateOpportunityCaches(
              this.cacheService,
              opp.postId,
              CACHE_PREFIXES.OPPORTUNITY,
            ),
          ),
        ]);

        this.logger.log(
          `Invalidated caches for ${allExpiredPosts.length} expired posts`,
        );
      }

      this.logger.log('Daily post status update completed successfully');
    } catch (error) {
      this.logger.error(
        'Failed to update post statuses or invalidate caches',
        error,
      );
    }
  }

  @Cron(CronExpression.EVERY_6_HOURS)
  async cleanupOrphanImagesAndEmptyPosts() {
    try {
      this.logger.log('Starting cleanup of orphan images and empty posts');

      // 1. Delete orphan images (images that reference non-existent posts)
      const orphanImages = await this.drizzle.db
        .delete(postImages)
        .where(
          notExists(
            this.drizzle.db
              .select()
              .from(posts)
              .where(eq(posts.id, postImages.postId)),
          ),
        )
        .returning({ id: postImages.id, imageUrl: postImages.imageUrl });

      if (orphanImages.length > 0) {
        this.logger.log(`Deleted ${orphanImages.length} orphan images`);
      }

      // 3. Clean up posts that are PENDING for more than 24 hours regardless of images
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      const stalePendingPosts = await this.drizzle.db
        .delete(posts)
        .where(
          and(
            eq(posts.status, post_statuses.PENDING),
            lt(posts.created_at, twentyFourHoursAgo.toISOString()),
          ),
        )
        .returning({ id: posts.id });

      if (stalePendingPosts.length > 0) {
        // Invalidate caches for deleted stale posts
        await Promise.all(
          stalePendingPosts.map((post) =>
            invalidatePostCaches(
              this.cacheService,
              post.id,
              CACHE_PREFIXES.POST,
            ),
          ),
        );

        this.logger.log(
          `Deleted ${stalePendingPosts.length} posts that were pending for more than 24 hours`,
        );
      }

      this.logger.log(
        'Cleanup of orphan images and empty posts completed successfully',
      );
    } catch (error) {
      this.logger.error(
        'Failed to cleanup orphan images and empty posts',
        error instanceof Error ? error.stack : String(error),
      );
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async checkForScheduledPosts() {
    try {
      this.logger.log('Checking for scheduled posts to activate');

      // Get all posts that are scheduled to be activated
      const scheduledPosts = await this.drizzle.db
        .select()
        .from(posts)
        .where(
          and(
            eq(posts.status, post_statuses.SCHEDULED),
            lt(posts.scheduledAt, new Date().toISOString()),
          ),
        );

      if (scheduledPosts.length === 0) {
        this.logger.log('No scheduled posts to activate');
        return;
      }

      // Update the status of scheduled posts to ACTIVE
      await this.drizzle.db
        .update(posts)
        .set({ status: post_statuses.ACTIVE })
        .where(
          and(
            eq(posts.status, post_statuses.SCHEDULED),
            lt(posts.scheduledAt, new Date().toISOString()),
          ),
        );

      this.logger.log(`Activated ${scheduledPosts.length} scheduled posts`);

      // Invalidate caches for all activated posts
      await Promise.all(
        scheduledPosts.map((post) =>
          invalidatePostCaches(this.cacheService, post.id, CACHE_PREFIXES.POST),
        ),
      );

      this.logger.log('Invalidated caches for activated posts');
    } catch (error) {
      this.logger.error(
        'Failed to check or activate scheduled posts',
        error instanceof Error ? error.stack : String(error),
      );
    }
  }
}
