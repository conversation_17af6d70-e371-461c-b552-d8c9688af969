import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EventNotificationPreferencesService } from '@/event-notification/event-notification-preferences.service';
import {
  postEngagementsSchema,
  post_engagement_types,
} from '@/db/schema/post_engagements';
import { events, posts, post_types } from '@/db/schema/posts';
import { and, eq } from 'drizzle-orm';
import { EventEngagementDto } from '../dto/event-engagement.dto';
import { PostServiceMessages } from '@app/shared/constants/post.constants';

@Injectable()
export class EventEngagementService {
  private readonly logger = new Logger(EventEngagementService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly eventNotificationPreferencesService: EventNotificationPreferencesService,
  ) {}

  /**
   * RSVP to an event with notification preferences
   * @param userId - User ID
   * @param studentProfileId - Student profile ID
   * @param postId - Post ID
   * @param data - Event engagement data
   * @returns The created engagement
   */
  async rsvpToEvent(
    userId: string,
    studentProfileId: string,
    postId: string,
    data: EventEngagementDto,
  ) {
    try {
      // Check if the post exists and is an event
      const [post] = await this.drizzle.db
        .select({
          id: posts.id,
          type: posts.type,
        })
        .from(posts)
        .where(eq(posts.id, postId));

      if (!post) {
        throw new NotFoundException(PostServiceMessages.PostNotFound);
      }

      if (post.type !== post_types.EVENT) {
        throw new NotFoundException('Post is not an event');
      }

      // Check if the event exists
      const [event] = await this.drizzle.db
        .select({
          id: events.id,
        })
        .from(events)
        .where(eq(events.postId, postId));

      if (!event) {
        throw new NotFoundException('Event not found');
      }

      // Check if the user has already RSVP'd to this event
      const [existingEngagement] = await this.drizzle.db
        .select()
        .from(postEngagementsSchema)
        .where(
          and(
            eq(postEngagementsSchema.postId, postId),
            eq(postEngagementsSchema.student_profile_id, studentProfileId),
            eq(
              postEngagementsSchema.post_engagement_type,
              post_engagement_types.rsvp,
            ),
          ),
        );

      // If the user has already RSVP'd, return the existing engagement
      if (existingEngagement) {
        this.logger.log(`User ${userId} has already RSVP'd to event ${postId}`);

        // Update notification preferences if provided
        if (data.notificationPreferences) {
          await this.eventNotificationPreferencesService.setEventNotificationPreferences(
            userId,
            event.id,
            data.notificationPreferences.optedIn,
            data.notificationPreferences.reminderTimes,
            data.notificationPreferences.channels,
          );
        }

        return existingEngagement;
      }

      // Create a new engagement
      const [engagement] = await this.drizzle.db
        .insert(postEngagementsSchema)
        .values({
          postId,
          student_profile_id: studentProfileId,
          post_engagement_type: post_engagement_types.rsvp,
        })
        .returning();

      this.logger.log(`User ${userId} RSVP'd to event ${postId}`);

      // Set notification preferences if provided
      if (data.notificationPreferences) {
        await this.eventNotificationPreferencesService.setEventNotificationPreferences(
          userId,
          event.id,
          data.notificationPreferences.optedIn,
          data.notificationPreferences.reminderTimes,
          data.notificationPreferences.channels,
        );
      } else {
        // Set default notification preferences
        await this.eventNotificationPreferencesService.setEventNotificationPreferences(
          userId,
          event.id,
        );
      }

      return engagement;
    } catch (error: any) {
      this.logger.error(
        `Error RSVP'ing to event: ${error.message || 'Unknown error'}`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Cancel RSVP to an event
   * @param userId - User ID
   * @param studentProfileId - Student profile ID
   * @param postId - Post ID
   * @returns The deleted engagement
   */
  async cancelRsvp(userId: string, studentProfileId: string, postId: string) {
    try {
      // Check if the post exists and is an event
      const [post] = await this.drizzle.db
        .select({
          id: posts.id,
          type: posts.type,
        })
        .from(posts)
        .where(eq(posts.id, postId));

      if (!post) {
        throw new NotFoundException(PostServiceMessages.PostNotFound);
      }

      if (post.type !== post_types.EVENT) {
        throw new NotFoundException('Post is not an event');
      }

      // Check if the event exists
      const [event] = await this.drizzle.db
        .select({
          id: events.id,
        })
        .from(events)
        .where(eq(events.postId, postId));

      if (!event) {
        throw new NotFoundException('Event not found');
      }

      // Check if the user has RSVP'd to this event
      const [existingEngagement] = await this.drizzle.db
        .select()
        .from(postEngagementsSchema)
        .where(
          and(
            eq(postEngagementsSchema.postId, postId),
            eq(postEngagementsSchema.student_profile_id, studentProfileId),
            eq(
              postEngagementsSchema.post_engagement_type,
              post_engagement_types.rsvp,
            ),
          ),
        );

      if (!existingEngagement) {
        throw new NotFoundException("User has not RSVP'd to this event");
      }

      // Delete the engagement
      const result = await this.drizzle.db
        .delete(postEngagementsSchema)
        .where(eq(postEngagementsSchema.id, existingEngagement.id))
        .returning();

      this.logger.log(`User ${userId} cancelled RSVP to event ${postId}`);

      // Set notification preferences to opted out
      await this.eventNotificationPreferencesService.setEventNotificationPreferences(
        userId,
        event.id,
        false,
      );

      return result[0];
    } catch (error: any) {
      this.logger.error(
        `Error cancelling RSVP to event: ${error.message || 'Unknown error'}`,
        error?.stack,
      );
      throw error;
    }
  }
}
