import { Test, TestingModule } from '@nestjs/testing';
import { PostNotificationService } from './post-notification.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EnhancedNotificationService } from '@app/shared/enhanced-notification/enhanced-notification.service';
import { NotificationHistoryService } from '@app/shared/notification/notification-history.service';
import { EmailService } from '@/mail/email.service';
import { post_types } from '@/db/schema/posts';
import {
  NotificationStatus,
  NotificationType,
} from '@app/shared/constants/notification.constant';
import { PostNotificationParams } from '../dto/post.dto';

describe('PostNotificationService', () => {
  let service: PostNotificationService;

  const mockNotificationType = {
    id: 'notification-type-id',
    code: 'new_post',
    name: 'New Post',
    description: 'Notification for new posts',
    module: 'post',
    default_channels: ['push', 'email'],
  };

  const mockEventRecord = {
    id: 'event-123',
  };

  const mockOpportunityRecord = {
    id: 'opportunity-456',
  };

  const mockUsers = [{ id: 'user-1' }, { id: 'user-2' }, { id: 'user-3' }];

  const mockStudentAdmins = [
    { id: 'admin-1', email: '<EMAIL>' },
    { id: 'admin-2', email: '<EMAIL>' },
  ];

  const mockDrizzleService = {
    db: {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      execute: jest.fn(),
      then: jest.fn(),
    },
  };

  const mockEnhancedNotificationService = {
    sendNotificationToUsers: jest.fn().mockResolvedValue(undefined),
  };

  const mockHistoryService = {
    logNotification: jest.fn().mockResolvedValue(undefined),
  };

  const mockEmailService = {
    sendCustomEmail: jest.fn().mockResolvedValue(undefined),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    // Reset environment variables
    process.env.EMAIL_TOGGLE = 'ON';
    process.env.FRONTEND_URL = 'https://test.com';

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostNotificationService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: EnhancedNotificationService,
          useValue: mockEnhancedNotificationService,
        },
        {
          provide: NotificationHistoryService,
          useValue: mockHistoryService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
      ],
    }).compile();

    service = module.get<PostNotificationService>(PostNotificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getNotificationTypeCode', () => {
    it('should return NEW_EVENT for event posts', () => {
      const result = (service as any).getNotificationTypeCode(post_types.EVENT);
      expect(result).toBe(NotificationType.NEW_EVENT);
    });

    it('should return NEW_OPPORTUNITY for opportunity posts', () => {
      const result = (service as any).getNotificationTypeCode(
        post_types.OPPORTUNITY,
      );
      expect(result).toBe(NotificationType.NEW_OPPORTUNITY);
    });

    it('should return ANNOUNCEMENT for general posts', () => {
      const result = (service as any).getNotificationTypeCode(
        post_types.GENERAL,
      );
      expect(result).toBe(NotificationType.ANNOUNCEMENT);
    });

    it('should return NEW_POST for unknown post types', () => {
      const result = (service as any).getNotificationTypeCode('unknown');
      expect(result).toBe(NotificationType.NEW_POST);
    });
  });

  describe('getEntitySpecificIds', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return event_id for event posts', async () => {
      // Mock the query chain to return the event record
      mockDrizzleService.db.limit.mockResolvedValue([mockEventRecord]);

      const result = await (service as any).getEntitySpecificIds(
        'post-123',
        post_types.EVENT,
      );

      expect(result).toEqual({ event_id: 'event-123' });
      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.from).toHaveBeenCalled();
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.limit).toHaveBeenCalledWith(1);
    });

    it('should return opportunity_id for opportunity posts', async () => {
      // Mock the query chain to return the opportunity record
      mockDrizzleService.db.limit.mockResolvedValue([mockOpportunityRecord]);

      const result = await (service as any).getEntitySpecificIds(
        'post-456',
        post_types.OPPORTUNITY,
      );

      expect(result).toEqual({ opportunity_id: 'opportunity-456' });
    });

    it('should return empty object for general posts', async () => {
      const result = await (service as any).getEntitySpecificIds(
        'post-789',
        post_types.GENERAL,
      );

      expect(result).toEqual({});
      expect(mockDrizzleService.db.select).not.toHaveBeenCalled();
    });

    it('should return empty object when no records found', async () => {
      mockDrizzleService.db.limit.mockResolvedValue([]);

      const result = await (service as any).getEntitySpecificIds(
        'post-123',
        post_types.EVENT,
      );

      expect(result).toEqual({});
    });

    it('should return empty object on database error', async () => {
      // Mock the entire query chain to reject
      mockDrizzleService.db.select.mockImplementation(() => {
        throw new Error('DB Error');
      });

      const result = await (service as any).getEntitySpecificIds(
        'post-123',
        post_types.EVENT,
      );

      expect(result).toEqual({});
    });
  });

  describe('buildTargetAudience', () => {
    it('should build audience with explicit recipients', () => {
      const params = {
        hasExplicitRecipients: true,
        recipients: ['student', 'student_admin'],
        postId: 'post-123',
        isGlobal: false,
        clubId: 'club-456',
        countries: ['country-1'],
        institutions: ['institution-1'],
      };

      const result = (service as any).buildTargetAudience(params);

      // When hasExplicitRecipients is true, the method still adds filters based on the implementation
      expect(result).toEqual({
        roles: ['student', 'student_admin'],
        filters: {
          userState: 'active',
          clubId: 'club-456',
          countryIds: ['country-1'],
          institutionIds: ['institution-1'],
        },
      });
    });

    it('should build audience without explicit recipients', () => {
      const params = {
        hasExplicitRecipients: false,
        postId: 'post-123',
        isGlobal: false,
        clubId: 'club-456',
        countries: ['country-1'],
        institutions: ['institution-1'],
      };

      const result = (service as any).buildTargetAudience(params);

      expect(result).toEqual({
        roles: ['student', 'student_admin'],
        filters: {
          userState: 'active',
          postId: 'post-123',
          clubId: 'club-456',
          countryIds: ['country-1'],
          institutionIds: ['institution-1'],
        },
      });
    });

    it('should build global audience', () => {
      const params = {
        hasExplicitRecipients: false,
        postId: 'post-123',
        isGlobal: true,
        clubId: 'club-456',
        countries: ['country-1'],
        institutions: ['institution-1'],
      };

      const result = (service as any).buildTargetAudience(params);

      expect(result).toEqual({
        roles: ['student', 'student_admin'],
        filters: {
          userState: 'active',
          postId: 'post-123',
        },
      });
    });
  });

  describe('getEffectiveChannels', () => {
    it('should return provided channels with email added', () => {
      const result = (service as any).getEffectiveChannels(['push']);
      expect(result).toEqual(['push', 'email']);
    });

    it('should not duplicate email channel', () => {
      const result = (service as any).getEffectiveChannels(['push', 'email']);
      expect(result).toEqual(['push', 'email']);
    });

    it('should return default push and email when no channels provided', () => {
      const result = (service as any).getEffectiveChannels();
      expect(result).toEqual(['push', 'email']);
    });

    it('should return default push and email when empty array provided', () => {
      const result = (service as any).getEffectiveChannels([]);
      expect(result).toEqual(['push', 'email']);
    });
  });

  describe('truncateText', () => {
    it('should truncate text longer than max length', () => {
      const longText = 'This is a very long text that should be truncated';
      const result = (service as any).truncateText(longText, 20);
      expect(result).toBe('This is a very long ...');
    });

    it('should return original text if shorter than max length', () => {
      const shortText = 'Short text';
      const result = (service as any).truncateText(shortText, 20);
      expect(result).toBe('Short text');
    });

    it('should return original text if equal to max length', () => {
      const text = 'Exactly twenty chars';
      const result = (service as any).truncateText(text, 20);
      expect(result).toBe('Exactly twenty chars');
    });
  });

  describe('getPostTypeDisplayName', () => {
    it('should return Event for event posts', () => {
      const result = (service as any).getPostTypeDisplayName(post_types.EVENT);
      expect(result).toBe('Event');
    });

    it('should return Opportunity for opportunity posts', () => {
      const result = (service as any).getPostTypeDisplayName(
        post_types.OPPORTUNITY,
      );
      expect(result).toBe('Opportunity');
    });

    it('should return Post for general posts', () => {
      const result = (service as any).getPostTypeDisplayName(
        post_types.GENERAL,
      );
      expect(result).toBe('Post');
    });

    it('should return Post for unknown post types', () => {
      const result = (service as any).getPostTypeDisplayName('unknown');
      expect(result).toBe('Post');
    });
  });

  describe('shouldSendDirectEmails', () => {
    beforeEach(() => {
      process.env.EMAIL_TOGGLE = 'ON';
    });

    it('should return true for events with email channel', () => {
      const result = (service as any).shouldSendDirectEmails(
        post_types.EVENT,
        false,
        [],
        ['email', 'push'],
      );
      expect(result).toBe(true);
    });

    it('should return true for student_admin recipients with email channel', () => {
      const result = (service as any).shouldSendDirectEmails(
        post_types.GENERAL,
        true,
        ['student_admin'],
        ['email', 'push'],
      );
      expect(result).toBe(true);
    });

    it('should return false when EMAIL_TOGGLE is OFF', () => {
      process.env.EMAIL_TOGGLE = 'OFF';
      const result = (service as any).shouldSendDirectEmails(
        post_types.EVENT,
        false,
        [],
        ['email', 'push'],
      );
      expect(result).toBe(false);
    });

    it('should return false when email channel is not included', () => {
      const result = (service as any).shouldSendDirectEmails(
        post_types.EVENT,
        false,
        [],
        ['push'],
      );
      expect(result).toBe(false);
    });

    it('should return false for general posts without student_admin recipients', () => {
      const result = (service as any).shouldSendDirectEmails(
        post_types.GENERAL,
        true,
        ['student'],
        ['email', 'push'],
      );
      expect(result).toBe(false);
    });
  });

  describe('getTargetUsersForSample', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      // Set up the complete query chain for user lookup
      mockDrizzleService.db.select.mockReturnValue(mockDrizzleService.db);
      mockDrizzleService.db.from.mockReturnValue(mockDrizzleService.db);
      mockDrizzleService.db.where.mockReturnValue(mockDrizzleService.db);
      mockDrizzleService.db.limit.mockReturnValue(mockDrizzleService.db);
      // The final execute() returns the actual data
      mockDrizzleService.db.execute.mockResolvedValue(mockUsers);
    });

    it('should return user IDs for valid roles', async () => {
      const targetAudience = {
        roles: ['student', 'student_admin'],
        filters: { userState: 'active' },
      };

      const result = await (service as any).getTargetUsersForSample(
        targetAudience,
        10,
      );

      expect(result).toEqual(['user-1', 'user-2', 'user-3']);
      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockDrizzleService.db.from).toHaveBeenCalled();
      expect(mockDrizzleService.db.where).toHaveBeenCalled();
      expect(mockDrizzleService.db.limit).toHaveBeenCalledWith(10);
    });

    it('should handle all roles', async () => {
      const targetAudience = {
        roles: ['all'],
        filters: { userState: 'active' },
      };

      const result = await (service as any).getTargetUsersForSample(
        targetAudience,
      );

      expect(result).toEqual(['user-1', 'user-2', 'user-3']);
    });

    it('should filter out invalid roles', async () => {
      const targetAudience = {
        roles: ['student', 'invalid_role', 'student_admin'],
        filters: { userState: 'active' },
      };

      const result = await (service as any).getTargetUsersForSample(
        targetAudience,
      );

      expect(result).toEqual(['user-1', 'user-2', 'user-3']);
    });

    it('should return empty array on database error', async () => {
      // Mock the query chain to throw an error
      mockDrizzleService.db.select.mockImplementation(() => {
        throw new Error('DB Error');
      });

      const targetAudience = {
        roles: ['student'],
        filters: { userState: 'active' },
      };

      const result = await (service as any).getTargetUsersForSample(
        targetAudience,
      );

      expect(result).toEqual([]);
    });

    it('should use default limit of 10', async () => {
      const targetAudience = {
        roles: ['student'],
        filters: { userState: 'active' },
      };

      await (service as any).getTargetUsersForSample(targetAudience);

      expect(mockDrizzleService.db.limit).toHaveBeenCalledWith(10);
    });
  });

  describe('sendDirectEmailsToStudentAdmins', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      // Set up the complete query chain for student admin lookup
      mockDrizzleService.db.select.mockReturnValue(mockDrizzleService.db);
      mockDrizzleService.db.from.mockReturnValue(mockDrizzleService.db);
      // The final where() returns the actual data
      mockDrizzleService.db.where.mockResolvedValue(mockStudentAdmins);
    });

    it('should send emails to all active student admins', async () => {
      const notificationPayload = {
        data: {
          subject: 'New Event',
          postType: 'Event',
        },
      };

      await (service as any).sendDirectEmailsToStudentAdmins(
        post_types.EVENT,
        'Test Event',
        'Test Description',
        notificationPayload,
      );

      expect(mockEmailService.sendCustomEmail).toHaveBeenCalledTimes(2);
      expect(mockEmailService.sendCustomEmail).toHaveBeenCalledWith({
        email: '<EMAIL>',
        subject: 'New Event',
        template: 'post',
        context: expect.objectContaining({
          subject: 'New Event',
          userName: 'Student Admin',
          postType: 'Event',
        }),
        critical: true,
      });
    });

    it('should not send emails when no student admins found', async () => {
      // Override the mock to return empty array for this test
      mockDrizzleService.db.where.mockResolvedValue([]);

      await (service as any).sendDirectEmailsToStudentAdmins(
        post_types.EVENT,
        'Test Event',
        'Test Description',
        {},
      );

      expect(mockEmailService.sendCustomEmail).not.toHaveBeenCalled();
    });

    it('should handle email sending errors gracefully', async () => {
      mockEmailService.sendCustomEmail.mockRejectedValue(
        new Error('Email Error'),
      );

      await expect(
        (service as any).sendDirectEmailsToStudentAdmins(
          post_types.EVENT,
          'Test Event',
          'Test Description',
          { data: { subject: 'Test' } },
        ),
      ).resolves.not.toThrow();
    });

    it('should set critical flag for events', async () => {
      const notificationPayload = {
        data: { subject: 'New Event' },
      };

      await (service as any).sendDirectEmailsToStudentAdmins(
        post_types.EVENT,
        'Test Event',
        'Test Description',
        notificationPayload,
      );

      expect(mockEmailService.sendCustomEmail).toHaveBeenCalledWith(
        expect.objectContaining({ critical: true }),
      );
    });

    it('should not set critical flag for non-events', async () => {
      const notificationPayload = {
        data: { subject: 'New Post' },
      };

      await (service as any).sendDirectEmailsToStudentAdmins(
        post_types.GENERAL,
        'Test Post',
        'Test Description',
        notificationPayload,
      );

      expect(mockEmailService.sendCustomEmail).toHaveBeenCalledWith(
        expect.objectContaining({ critical: false }),
      );
    });
  });

  describe('logNotificationsInHistory', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      // Mock getTargetUsersForSample method
      jest
        .spyOn(service as any, 'getTargetUsersForSample')
        .mockResolvedValue(['user-1', 'user-2', 'user-3']);
      // Mock getEntitySpecificIds method
      jest.spyOn(service as any, 'getEntitySpecificIds').mockResolvedValue({});
    });

    it('should log notifications for target users', async () => {
      const targetAudience = {
        roles: ['student'],
        filters: { userState: 'active' },
      };

      await (service as any).logNotificationsInHistory(
        targetAudience,
        mockNotificationType,
        'new_post',
        'post-123',
        post_types.GENERAL,
        'Test Title',
        'Test Description',
        false,
        ['push', 'email'],
      );

      expect(mockHistoryService.logNotification).toHaveBeenCalledTimes(3);
      expect(mockHistoryService.logNotification).toHaveBeenCalledWith({
        notificationTypeId: 'notification-type-id',
        userId: 'user-1',
        title: 'New Post',
        body: 'Test Description',
        data: expect.objectContaining({
          postId: 'post-123',
          post_id: 'post-123',
          postType: post_types.GENERAL,
          postTitle: 'Test Title',
          isGlobal: false,
          module: 'post',
          type: 'new_post',
        }),
        channels: ['push', 'email'],
        status: NotificationStatus.SENT,
      });
    });

    it('should not log when no target users found', async () => {
      // Override the mock to return empty array
      jest
        .spyOn(service as any, 'getTargetUsersForSample')
        .mockResolvedValue([]);

      const targetAudience = {
        roles: ['student'],
        filters: { userState: 'active' },
      };

      await (service as any).logNotificationsInHistory(
        targetAudience,
        mockNotificationType,
        'new_post',
        'post-123',
        post_types.GENERAL,
        'Test Title',
        'Test Description',
        false,
        ['push'],
      );

      expect(mockHistoryService.logNotification).not.toHaveBeenCalled();
    });

    it('should handle logging errors gracefully', async () => {
      mockHistoryService.logNotification.mockRejectedValue(
        new Error('Log Error'),
      );

      const targetAudience = {
        roles: ['student'],
        filters: { userState: 'active' },
      };

      await expect(
        (service as any).logNotificationsInHistory(
          targetAudience,
          mockNotificationType,
          'new_post',
          'post-123',
          post_types.GENERAL,
          'Test Title',
          'Test Description',
          false,
          ['push'],
        ),
      ).resolves.not.toThrow();
    });
  });

  describe('sendPostNotification', () => {
    const validParams: PostNotificationParams = {
      postId: '123e4567-e89b-12d3-a456-426614174000',
      title: 'Test Post Title',
      description: 'Test post description that is long enough',
      postType: post_types.GENERAL,
      isGlobal: false,
      clubId: '123e4567-e89b-12d3-a456-426614174001',
      countries: ['country-1'],
      institutions: ['institution-1'],
      channels: ['push', 'email'],
      recipients: ['student', 'student_admin'],
    };

    beforeEach(() => {
      jest.clearAllMocks();

      // Reset all mocks to return themselves for chaining
      mockDrizzleService.db.select.mockReturnValue(mockDrizzleService.db);
      mockDrizzleService.db.from.mockReturnValue(mockDrizzleService.db);
      mockDrizzleService.db.where.mockReturnValue(mockDrizzleService.db);
      mockDrizzleService.db.limit.mockReturnValue(mockDrizzleService.db);

      // Mock the final result for notification type lookup
      mockDrizzleService.db.where.mockResolvedValue([mockNotificationType]);

      // Mock entity specific IDs lookup
      jest.spyOn(service as any, 'getEntitySpecificIds').mockResolvedValue({});

      // Mock target users lookup for history logging
      jest
        .spyOn(service as any, 'getTargetUsersForSample')
        .mockResolvedValue(['user-1', 'user-2']);
    });

    it('should successfully send notification for valid parameters', async () => {
      await service.sendPostNotification(validParams);

      expect(
        mockEnhancedNotificationService.sendNotificationToUsers,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          notificationTypeId: 'notification-type-id',
          data: expect.objectContaining({
            postId: validParams.postId,
            postType: validParams.postType,
            title: validParams.title,
            description: expect.any(String),
            isGlobal: validParams.isGlobal,
            type: NotificationType.ANNOUNCEMENT,
            subject: 'New Post',
            displayTitle: 'New Post',
            body: validParams.description,
            post_title: validParams.title,
            postTypeDisplay: 'Post',
            userName: 'Reach User',
            actionUrl: `${process.env.FRONTEND_URL}/posts/${validParams.postId}`,
            actionText: 'View Post',
          }),
          targetAudience: expect.objectContaining({
            roles: ['student', 'student_admin'],
            filters: expect.objectContaining({
              userState: 'active',
            }),
          }),
          channels: ['push', 'email'],
        }),
      );
    });

    it('should handle event posts correctly', async () => {
      const eventParams = { ...validParams, postType: post_types.EVENT };
      jest
        .spyOn(service as any, 'getEntitySpecificIds')
        .mockResolvedValue({ event_id: 'event-123' });

      await service.sendPostNotification(eventParams);

      expect(
        mockEnhancedNotificationService.sendNotificationToUsers,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            type: NotificationType.NEW_EVENT,
            subject: 'New Event',
            postTypeDisplay: 'Event',
            event_id: 'event-123',
          }),
        }),
      );
    });

    it('should handle opportunity posts correctly', async () => {
      const opportunityParams = {
        ...validParams,
        postType: post_types.OPPORTUNITY,
      };
      jest
        .spyOn(service as any, 'getEntitySpecificIds')
        .mockResolvedValue({ opportunity_id: 'opportunity-456' });

      await service.sendPostNotification(opportunityParams);

      expect(
        mockEnhancedNotificationService.sendNotificationToUsers,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            type: NotificationType.NEW_OPPORTUNITY,
            subject: 'New Opportunity',
            postTypeDisplay: 'Opportunity',
            opportunity_id: 'opportunity-456',
          }),
        }),
      );
    });

    it('should handle global posts correctly', async () => {
      const globalParams = { ...validParams, isGlobal: true };

      await service.sendPostNotification(globalParams);

      expect(
        mockEnhancedNotificationService.sendNotificationToUsers,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          targetAudience: expect.objectContaining({
            filters: expect.not.objectContaining({
              clubId: expect.any(String),
              countryIds: expect.any(Array),
              institutionIds: expect.any(Array),
            }),
          }),
        }),
      );
    });

    it('should send direct emails when conditions are met', async () => {
      const eventParams: PostNotificationParams = {
        ...validParams,
        postType: post_types.EVENT,
        channels: ['push', 'email'] as ('push' | 'email' | 'in_app')[],
        recipients: ['student_admin'] as (
          | 'student'
          | 'student_admin'
          | 'admin'
          | 'super_admin'
        )[],
      };

      jest
        .spyOn(service as any, 'shouldSendDirectEmails')
        .mockReturnValue(true);
      jest
        .spyOn(service as any, 'sendDirectEmailsToStudentAdmins')
        .mockResolvedValue(undefined);

      await service.sendPostNotification(eventParams);

      expect(service as any).toHaveProperty('sendDirectEmailsToStudentAdmins');
    });

    it('should not send notification when notification type not found', async () => {
      // Override the mock to return empty array for this test
      mockDrizzleService.db.where.mockResolvedValue([]); // No notification type found

      await service.sendPostNotification(validParams);

      expect(
        mockEnhancedNotificationService.sendNotificationToUsers,
      ).not.toHaveBeenCalled();
    });

    it('should handle invalid parameters gracefully', async () => {
      const invalidParams = {
        ...validParams,
        postId: 'invalid-uuid',
      };

      await expect(
        service.sendPostNotification(invalidParams),
      ).rejects.toThrow();
    });

    it('should handle notification service errors gracefully', async () => {
      mockEnhancedNotificationService.sendNotificationToUsers.mockRejectedValue(
        new Error('Notification service error'),
      );

      await expect(
        service.sendPostNotification(validParams),
      ).resolves.not.toThrow();
    });

    it('should truncate description correctly', async () => {
      const longDescriptionParams = {
        ...validParams,
        description: 'A'.repeat(300), // Very long description
      };

      await service.sendPostNotification(longDescriptionParams);

      expect(
        mockEnhancedNotificationService.sendNotificationToUsers,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            description: 'A'.repeat(100) + '...', // Should be truncated to 100 chars + ...
          }),
        }),
      );
    });

    it('should include metadata in notification payload', async () => {
      await service.sendPostNotification(validParams);

      expect(
        mockEnhancedNotificationService.sendNotificationToUsers,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            metadata: {
              postId: validParams.postId,
              postType: validParams.postType,
              isGlobal: validParams.isGlobal,
            },
          }),
        }),
      );
    });

    it('should log notifications in history', async () => {
      jest
        .spyOn(service as any, 'logNotificationsInHistory')
        .mockResolvedValue(undefined);

      await service.sendPostNotification(validParams);

      expect(service as any).toHaveProperty('logNotificationsInHistory');
    });
  });
});
