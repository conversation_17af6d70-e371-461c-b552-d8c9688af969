import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EnhancedNotificationService } from '@app/shared/enhanced-notification/enhanced-notification.service';
import { NotificationHistoryService } from '@app/shared/notification/notification-history.service';
import { notification_types, users } from '@/db/schema';
import { events, opportunity } from '@/db/schema/posts';
import { and, eq, inArray } from 'drizzle-orm';
import {
  NotificationStatus,
  NotificationType,
} from '@app/shared/constants/notification.constant';
import { post_types } from '@/db/schema/posts';
import { EmailService } from '@/mail/email.service';
import {
  PostNotificationParams,
  postNotificationParamsSchema,
} from '../dto/post.dto';

/**
 * Service responsible for sending notifications when posts are created or updated
 */
@Injectable()
export class PostNotificationService {
  private readonly logger = new Logger(PostNotificationService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly enhancedNotificationService: EnhancedNotificationService,
    private readonly historyService: NotificationHistoryService,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Send notification for a new post
   * @param params - Post notification parameters
   */
  async sendPostNotification(params: PostNotificationParams): Promise<void> {
    // Validate parameters using Zod schema
    const validatedParams = postNotificationParamsSchema.parse(params);

    const {
      postId,
      title,
      description,
      postType,
      isGlobal,
      clubId,
      countries,
      institutions,
      channels,
      recipients,
    } = validatedParams;

    try {
      // Determine notification type based on post type
      const notificationTypeCode = this.getNotificationTypeCode(postType);

      // Get notification type from database
      const notificationTypes = await this.drizzle.db
        .select()
        .from(notification_types)
        .where(eq(notification_types.code, notificationTypeCode));

      const notificationType = notificationTypes[0];

      if (!notificationType) {
        return;
      }

      // Determine if specific recipients are explicitly set
      const hasExplicitRecipients = !!(recipients && recipients.length > 0);

      // Prepare target audience filters
      const targetAudience = this.buildTargetAudience({
        hasExplicitRecipients,
        recipients,
        postId,
        isGlobal: !!isGlobal,
        clubId,
        countries,
        institutions,
      });

      // Configure notification channels
      const effectiveChannels = this.getEffectiveChannels(channels);

      // Get entity-specific IDs for events and opportunities
      const entitySpecificIds = await this.getEntitySpecificIds(
        postId,
        postType,
      );

      // Create notification payload
      const postTypeDisplay = this.getPostTypeDisplayName(postType);
      const subject = `New ${postTypeDisplay}`;
      const truncatedDescription = this.truncateText(description, 200);
      const emailMessage = `<p>${truncatedDescription}</p>`;

      const notificationPayload = {
        notificationTypeId: notificationType.id,
        data: {
          postId,
          post_id: postId,
          postType,
          title,
          description: this.truncateText(description, 100),
          isGlobal,
          type: notificationTypeCode,
          subject,
          displayTitle: subject,
          body: description,
          post_title: title,
          postTypeDisplay,
          userName: 'Reach User',
          date: new Date().toUTCString(),
          message: emailMessage,
          actionUrl: `${process.env.FRONTEND_URL}/posts/${postId}`,
          actionText: 'View Post',
          timestamp: new Date().toISOString(),
          ...entitySpecificIds,
          metadata: {
            postId,
            postType,
            isGlobal,
          },
        },
        targetAudience,
        channels: effectiveChannels,
      };

      try {
        notificationPayload.data.timestamp = new Date().toISOString();

        await this.enhancedNotificationService.sendNotificationToUsers(
          notificationPayload,
        );
        if (
          this.shouldSendDirectEmails(
            postType,
            hasExplicitRecipients,
            recipients,
            effectiveChannels,
          )
        ) {
          await this.sendDirectEmailsToStudentAdmins(
            postType,
            title,
            description,
            notificationPayload,
          );
        }

        await this.logNotificationsInHistory(
          targetAudience,
          notificationType,
          notificationTypeCode,
          postId,
          postType,
          title,
          description,
          !!isGlobal,
          effectiveChannels,
        );
      } catch (error: any) {
        this.logger.error(
          `Error sending notification for post ${postId}: ${error.message}`,
        );
      }
    } catch (error: any) {
      this.logger.error(`Failed to process notification for post ${postId}`);
    }
  }

  /**
   * Get notification type code based on post type
   */
  private getNotificationTypeCode(postType: string): string {
    switch (postType) {
      case post_types.EVENT:
        return NotificationType.NEW_EVENT;
      case post_types.OPPORTUNITY:
        return NotificationType.NEW_OPPORTUNITY;
      case post_types.GENERAL:
        return NotificationType.ANNOUNCEMENT;
      default:
        return NotificationType.NEW_POST;
    }
  }

  /**
   * Get entity-specific ID for events and opportunities
   * @param postId - The post ID
   * @param postType - The type of post (event, opportunity, etc.)
   * @returns Object containing the appropriate entity ID
   */
  private async getEntitySpecificIds(
    postId: string,
    postType: string,
  ): Promise<{ event_id?: string; opportunity_id?: string }> {
    try {
      if (postType === post_types.EVENT) {
        const eventRecord = await this.drizzle.db
          .select({ id: events.id })
          .from(events)
          .where(eq(events.postId, postId))
          .limit(1);

        if (eventRecord.length > 0 && eventRecord[0]) {
          return { event_id: eventRecord[0].id };
        }
      } else if (postType === post_types.OPPORTUNITY) {
        const opportunityRecord = await this.drizzle.db
          .select({ id: opportunity.id })
          .from(opportunity)
          .where(eq(opportunity.postId, postId))
          .limit(1);

        if (opportunityRecord.length > 0 && opportunityRecord[0]) {
          return { opportunity_id: opportunityRecord[0].id };
        }
      }

      return {};
    } catch (error) {
      return {};
    }
  }

  /**
   * Build target audience for notification
   */
  private buildTargetAudience({
    hasExplicitRecipients,
    recipients,
    postId,
    isGlobal,
    clubId,
    countries,
    institutions,
  }: {
    hasExplicitRecipients: boolean;
    recipients?: string[];
    postId: string;
    isGlobal: boolean;
    clubId?: string;
    countries?: string[];
    institutions?: string[];
  }) {
    const targetAudience: {
      roles: string[];
      filters?: Record<string, any>;
    } = {
      roles: hasExplicitRecipients ? recipients! : ['student', 'student_admin'],
      filters: {
        userState: 'active',
      },
    };

    if (!hasExplicitRecipients) {
      targetAudience.filters!.postId = postId;
    }
    if (!isGlobal) {
      if (clubId) {
        targetAudience.filters!.clubId = clubId;
      }

      if (countries && countries.length > 0) {
        targetAudience.filters!.countryIds = countries;
      }

      if (institutions && institutions.length > 0) {
        targetAudience.filters!.institutionIds = institutions;
      }
    }

    return targetAudience;
  }

  /**
   * Get effective channels for notification
   */
  private getEffectiveChannels(channels?: string[]): string[] {
    const effectiveChannels = channels?.length ? [...channels] : ['push'];
    if (!effectiveChannels.includes('email')) {
      effectiveChannels.push('email');
    }
    return effectiveChannels;
  }

  /**
   * Truncate text to specified length
   */
  private truncateText(text: string, maxLength: number): string {
    return text.length > maxLength
      ? text.substring(0, maxLength) + '...'
      : text;
  }

  /**
   * Get display name for post type
   */
  private getPostTypeDisplayName(postType: string): string {
    switch (postType) {
      case post_types.EVENT:
        return 'Event';
      case post_types.OPPORTUNITY:
        return 'Opportunity';
      default:
        return 'Post';
    }
  }

  /**
   * Check if direct emails should be sent
   */
  private shouldSendDirectEmails(
    postType: string,
    hasExplicitRecipients: boolean,
    recipients?: string[],
    channels?: string[],
  ): boolean {
    const isEvent = postType === post_types.EVENT;
    const hasStudentAdminRecipient =
      hasExplicitRecipients && recipients
        ? recipients.includes('student_admin')
        : false;
    const includesEmailChannel = channels ? channels.includes('email') : false;
    const emailToggleEnabled = process.env.EMAIL_TOGGLE === 'ON';

    return (
      (isEvent || hasStudentAdminRecipient) &&
      includesEmailChannel &&
      emailToggleEnabled
    );
  }

  /**
   * Send direct emails to student admins
   */
  private async sendDirectEmailsToStudentAdmins(
    postType: string,
    title: string,
    description: string,
    notificationPayload: any,
  ): Promise<void> {
    try {
      // Get all student_admin users who are active and not deleted
      const studentAdmins = await this.drizzle.db
        .select({
          id: users.id,
          email: users.email,
        })
        .from(users)
        .where(
          and(
            eq(users.role, 'student_admin'),
            eq(users.state, 'active'),
            eq(users.deleted, false),
          ),
        );

      if (studentAdmins.length === 0) {
        return;
      }

      for (const admin of studentAdmins) {
        try {
          const emailMessage = `<p>A new ${this.getPostTypeDisplayName(postType)} has been created: <strong>${title}</strong></p>
                              <p>${this.truncateText(description, 200)}</p>`;

          await this.emailService.sendCustomEmail({
            email: admin.email,
            subject: notificationPayload.data.subject,
            template: 'post',
            context: {
              ...notificationPayload.data,
              userName: 'Student Admin',
              postType: this.getPostTypeDisplayName(postType),
              message: emailMessage,
            },
            critical: postType === post_types.EVENT,
          });
        } catch (emailError) {
          // Silent fail
        }
      }
    } catch (error) {
      this.logger.error(`Error sending direct emails to student admins`);
    }
  }

  /**
   * Log notifications in history for tracking
   */
  private async logNotificationsInHistory(
    targetAudience: any,
    notificationType: any,
    notificationTypeCode: string,
    postId: string,
    postType: string,
    title: string,
    description: string,
    isGlobal: boolean,
    channels: string[],
  ): Promise<void> {
    const targetUsers = await this.getTargetUsersForSample(targetAudience);

    if (targetUsers.length === 0) {
      return;
    }

    const entitySpecificIds = await this.getEntitySpecificIds(postId, postType);
    await Promise.all(
      targetUsers.map(async (userId) => {
        try {
          await this.historyService.logNotification({
            notificationTypeId: notificationType.id,
            userId,
            title: `New ${this.getPostTypeDisplayName(postType)}`,
            body: description,
            data: {
              postId,
              post_id: postId,
              postType,
              postTitle: title,
              isGlobal,
              module: notificationType.module,
              type: notificationTypeCode,
              ...entitySpecificIds,
            },
            channels,
            status: NotificationStatus.SENT,
          });
        } catch (error) {
          // Silent fail
        }
      }),
    );
  }

  /**
   * Get a sample of target users for notification history logging
   */
  private async getTargetUsersForSample(
    targetAudience: {
      roles: string[];
      filters?: Record<string, any>;
    },
    limit: number = 10,
  ): Promise<string[]> {
    const conditions = [];
    const ALLOWED_ROLES = [
      'student',
      'student_admin',
      'super_admin',
      'admin',
    ] as const;
    type UserRole = (typeof ALLOWED_ROLES)[number];

    try {
      // Add role filter if roles are specified
      if (
        targetAudience.roles &&
        targetAudience.roles.length > 0 &&
        !targetAudience.roles.includes('all')
      ) {
        const validRoles: UserRole[] = targetAudience.roles.filter(
          (role): role is UserRole =>
            (ALLOWED_ROLES as readonly string[]).includes(role),
        );
        if (validRoles.length > 0) {
          conditions.push(inArray(users.role, validRoles));
        }
      }

      // Always filter for active and non-deleted users
      conditions.push(eq(users.state, 'active'));
      conditions.push(eq(users.deleted, false));

      // Execute the query
      const result = await this.drizzle.db
        .select({ id: users.id })
        .from(users)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .limit(limit)
        .execute();

      return result.map((user: { id: string }) => user.id);
    } catch (error) {
      return [];
    }
  }
}
