import { Test, TestingModule } from '@nestjs/testing';
import { EventsController } from './event.controller';
import { EventService } from './event.service';
import { NotFoundException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RolesBuilder } from 'nest-access-control';

describe('EventsController', () => {
  let controller: EventsController;
  let eventService: EventService;

  const mockEventService = {
    createEvent: jest.fn(),
    createClubEvent: jest.fn(),
    updateEvent: jest.fn(),
    getEventById: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EventsController],
      providers: [
        {
          provide: EventService,
          useValue: mockEventService,
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: '__roles_builder__',
          useValue: new RolesBuilder(),
        },
      ],
    }).compile();

    controller = module.get<EventsController>(EventsController);
    eventService = module.get<EventService>(EventService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getEventById', () => {
    const eventId = '123e4567-e89b-12d3-a456-426614174000';
    const mockEvent = {
      id: eventId,
      startDate: '2024-01-01',
      endDate: '2024-01-02',
      startTime: '10:00:00',
      endTime: '12:00:00',
      virtualLink: 'https://example.com',
      postId: 'post-123',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      post: {
        id: 'post-123',
        title: 'Test Event',
        description: 'Test event description',
        status: 'active',
        type: 'event',
        postedBy: {
          id: 'user-123',
          profile: {
            id: 'profile-123',
            email: '<EMAIL>',
            name: 'Test User',
          },
          student_profile: {
            id: 'student-123',
            first_name: 'Test',
            last_name: 'User',
          },
        },
        countries: [],
        institutions: [],
        postEngagements: [],
        images: [],
        club: null,
      },
    };

    it('should return an event when found', async () => {
      mockEventService.getEventById.mockResolvedValue(mockEvent);

      const result = await controller.getEventById(eventId);

      expect(result).toEqual(mockEvent);
      expect(eventService.getEventById).toHaveBeenCalledWith(eventId);
    });

    it('should throw NotFoundException when event not found', async () => {
      const error = new NotFoundException(`Event with ID ${eventId} not found`);
      mockEventService.getEventById.mockRejectedValue(error);

      await expect(controller.getEventById(eventId)).rejects.toThrow(error);
      expect(eventService.getEventById).toHaveBeenCalledWith(eventId);
    });

    it('should handle service errors gracefully', async () => {
      const error = new Error('Database connection failed');
      mockEventService.getEventById.mockRejectedValue(error);

      await expect(controller.getEventById(eventId)).rejects.toThrow(error);
      expect(eventService.getEventById).toHaveBeenCalledWith(eventId);
    });

    it('should validate UUID format with CustomParseUUIDPipe', () => {
      // This test verifies that the controller uses CustomParseUUIDPipe
      // The actual UUID validation is tested in the pipe's own test file
      const controllerMethod = controller.getEventById;
      expect(controllerMethod).toBeDefined();
    });
  });
});
