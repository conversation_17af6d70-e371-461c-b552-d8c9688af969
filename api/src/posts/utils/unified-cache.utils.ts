import { Logger } from '@nestjs/common';
import { CacheService } from '@app/shared/redis/cache.service';
import { user_roles } from '@/db/schema';
import { post_types, post_statuses, postType } from '@/db/schema/posts';

const logger = new Logger('PostCache');

export type PostEntityType = 'post' | 'event' | 'opportunity';

interface CacheInvalidationConfig {
  entityId: string;
  entityType: PostEntityType;
  cachePrefix: string;
  postCachePrefix?: string;
}

/**
 * Generates a cache key for a specific post entity (post, event, or opportunity)
 */
export const generateCacheKey = (
  cacheService: CacheService,
  entityId: string,
  cachePrefix: string,
): string => {
  return cacheService.generateResourceKey(entityId, cachePrefix);
};

/**
 * Gets the post type for a given entity type
 */
const getPostTypeForEntity = (entityType: PostEntityType): string | null => {
  switch (entityType) {
    case 'event':
      return post_types.EVENT;
    case 'opportunity':
      return post_types.OPPORTUNITY;
    case 'post':
      return null;
    default:
      return null;
  }
};

/**
 * Generates cache keys to invalidate based on entity type
 */
const generateCacheKeysToInvalidate = (
  entityType: PostEntityType,
  entityId: string,
): string[] => {
  const baseKeys = [
    // Role-specific list caches
    `all:${user_roles.ADMIN}`,
    `all:${user_roles.STUDENT}`,
    `all:${user_roles.STUDENT_ADMIN}`,
    // Status-specific caches
    ...Object.values(post_statuses).map((status) => `status:${status}`),
    // General lists
    'all',
    'active',
    // Club-specific caches (pattern match)
    'club-posts:*',
    // Student-specific caches (pattern match)
    'student-posts:*',
    // Specific entity cache
    entityId,
  ];

  // Add type-specific caches based on entity type
  const postTypeForEntity = getPostTypeForEntity(entityType);
  if (postTypeForEntity) {
    baseKeys.push(`type:${postTypeForEntity}`);
  } else if (entityType === 'post') {
    // For general posts, invalidate all type-specific caches
    baseKeys.push(...postType.map((type) => `type:${type}`));
    // Add user-specific caches for posts
    baseKeys.push(...Object.values(user_roles).map((role) => `all:${role}:*`));
  }

  return baseKeys;
};

/**
 * Invalidates cache keys using appropriate method (pattern or direct)
 */
const invalidateCacheKeys = async (
  cacheService: CacheService,
  keysToInvalidate: string[],
  cachePrefix: string,
): Promise<void> => {
  await Promise.all(
    keysToInvalidate.map(async (cacheKey) => {
      if (cacheKey.includes('*')) {
        const pattern = `*:${cachePrefix}:*:${cacheKey}`;
        await cacheService.invalidatePattern(pattern);
      } else {
        const fullCacheKey = cacheService.generateKey(cacheKey, cachePrefix);
        await cacheService.del(fullCacheKey);
      }
    }),
  );
};

/**
 * Unified cache invalidation for all post-related entities
 */
export const invalidateCaches = async (
  cacheService: CacheService,
  invalidationConfig: CacheInvalidationConfig,
): Promise<void> => {
  const { entityId, entityType, cachePrefix } = invalidationConfig;

  try {
    const keysToInvalidate = generateCacheKeysToInvalidate(
      entityType,
      entityId,
    );

    // Use the appropriate invalidation method based on entity type
    if (entityType === 'post') {
      // For posts, use the existing invalidateMany method
      await cacheService.invalidateMany(keysToInvalidate, cachePrefix);
    } else {
      // For events and opportunities, use pattern-based invalidation
      await invalidateCacheKeys(cacheService, keysToInvalidate, cachePrefix);
    }

    logger.debug(
      `${entityType} caches invalidated successfully for ${entityId}`,
    );
  } catch (error) {
    logger.warn(
      `Failed to invalidate ${entityType} caches for ${entityId}`,
      error,
    );
  }
};

/**
 * Invalidates post-related caches when events or opportunities are created/updated/deleted
 * Since events and opportunities are posts with specific types, we need to invalidate post caches too
 */
export const invalidatePostCachesForEntity = async (
  cacheService: CacheService,
  entityId: string,
  entityType: 'event' | 'opportunity',
  postCachePrefix: string,
): Promise<void> => {
  try {
    const keysToInvalidate = generateCacheKeysToInvalidate(
      entityType,
      entityId,
    );
    await invalidateCacheKeys(cacheService, keysToInvalidate, postCachePrefix);

    logger.debug(
      `Post caches invalidated successfully for ${entityType} ${entityId}`,
    );
  } catch (error) {
    logger.warn(
      `Failed to invalidate post caches for ${entityType} ${entityId}`,
      error,
    );
  }
};

// Legacy function names for backward compatibility during migration
export const generatePostKey = generateCacheKey;
export const generateEventKey = generateCacheKey;
export const generateOpportunityKey = generateCacheKey;

export const invalidatePostCaches = (
  cacheService: CacheService,
  entityId: string,
  cachePrefix: string,
): Promise<void> => {
  return invalidateCaches(cacheService, {
    entityId,
    entityType: 'post',
    cachePrefix,
  });
};

export const invalidateEventCaches = (
  cacheService: CacheService,
  entityId: string,
  cachePrefix: string,
): Promise<void> => {
  return invalidateCaches(cacheService, {
    entityId,
    entityType: 'event',
    cachePrefix,
  });
};

export const invalidateOpportunityCaches = (
  cacheService: CacheService,
  entityId: string,
  cachePrefix: string,
): Promise<void> => {
  return invalidateCaches(cacheService, {
    entityId,
    entityType: 'opportunity',
    cachePrefix,
  });
};

export const invalidatePostCachesForEvent = (
  cacheService: CacheService,
  entityId: string,
  postCachePrefix: string,
): Promise<void> => {
  return invalidatePostCachesForEntity(
    cacheService,
    entityId,
    'event',
    postCachePrefix,
  );
};

export const invalidatePostCachesForOpportunity = (
  cacheService: CacheService,
  entityId: string,
  postCachePrefix: string,
): Promise<void> => {
  return invalidatePostCachesForEntity(
    cacheService,
    entityId,
    'opportunity',
    postCachePrefix,
  );
};
