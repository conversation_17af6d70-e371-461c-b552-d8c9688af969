import { Test, TestingModule } from '@nestjs/testing';
import { PostController } from './post.controller';
import { PostService } from './post.service';
import { NotFoundException } from '@nestjs/common';
import { post_engagement_types } from '@/db/schema';
import { Reflector } from '@nestjs/core';
import { RolesBuilder } from 'nest-access-control';

describe('PostController', () => {
  let controller: PostController;
  let postService: PostService;

  const mockPostService = {
    createPost: jest.fn(),
    createClubGeneralPost: jest.fn(),
    getPosts: jest.fn(),
    getClubPosts: jest.fn(),
    getPostEngagements: jest.fn(),
    getTrendingById: jest.fn(),
    deletePost: jest.fn(),
    updatePost: jest.fn(),
    likePost: jest.fn(),
    sharePost: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PostController],
      providers: [
        {
          provide: PostService,
          useValue: mockPostService,
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: '__roles_builder__',
          useValue: new RolesBuilder(),
        },
      ],
    }).compile();

    controller = module.get<PostController>(PostController);
    postService = module.get<PostService>(PostService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getTrendingById', () => {
    const postId = '123e4567-e89b-12d3-a456-426614174000';
    const mockTrendingPost = {
      id: postId,
      title: 'Trending Post',
      description: 'This is a trending post',
      status: 'active',
      type: 'general',
      postedBy: {
        id: 'user-123',
        profile: {
          id: 'profile-123',
          email: '<EMAIL>',
          name: 'Test User',
        },
        student_profile: {
          id: 'student-123',
          first_name: 'Test',
          last_name: 'User',
        },
      },
      countries: [],
      institutions: [],
      opportunity: null,
      event: null,
      postEngagements: [
        {
          id: 'engagement-1',
          post_engagement_type: post_engagement_types.like,
          student_profile_id: 'student-1',
        },
        {
          id: 'engagement-2',
          post_engagement_type: post_engagement_types.like,
          student_profile_id: 'student-2',
        },
        {
          id: 'engagement-3',
          post_engagement_type: post_engagement_types.share,
          student_profile_id: 'student-3',
        },
      ],
      images: [],
      club: null,
      engagementMetrics: {
        totalEngagements: 3,
        likes: 2,
        shares: 1,
        trendingScore: 4,
      },
    };

    it('should return a trending post with engagement metrics when found', async () => {
      mockPostService.getTrendingById.mockResolvedValue(mockTrendingPost);

      const result = await controller.getTrendingById(postId);

      expect(result).toEqual(mockTrendingPost);
      expect(postService.getTrendingById).toHaveBeenCalledWith(postId);
    });

    it('should throw NotFoundException when trending post not found', async () => {
      const error = new NotFoundException(
        `Trending post with ID ${postId} not found`,
      );
      mockPostService.getTrendingById.mockRejectedValue(error);

      await expect(controller.getTrendingById(postId)).rejects.toThrow(error);
      expect(postService.getTrendingById).toHaveBeenCalledWith(postId);
    });

    it('should handle service errors gracefully', async () => {
      const error = new Error('Database connection failed');
      mockPostService.getTrendingById.mockRejectedValue(error);

      await expect(controller.getTrendingById(postId)).rejects.toThrow(error);
      expect(postService.getTrendingById).toHaveBeenCalledWith(postId);
    });

    it('should validate UUID format with CustomParseUUIDPipe', () => {
      // This test verifies that the controller uses CustomParseUUIDPipe
      // The actual UUID validation is tested in the pipe's own test file
      const controllerMethod = controller.getTrendingById;
      expect(controllerMethod).toBeDefined();
    });

    it('should return trending post with zero engagement metrics', async () => {
      const postWithNoEngagements = {
        ...mockTrendingPost,
        postEngagements: [],
        engagementMetrics: {
          totalEngagements: 0,
          likes: 0,
          shares: 0,
          trendingScore: 0,
        },
      };

      mockPostService.getTrendingById.mockResolvedValue(postWithNoEngagements);

      const result = await controller.getTrendingById(postId);

      expect(result.engagementMetrics).toEqual({
        totalEngagements: 0,
        likes: 0,
        shares: 0,
        trendingScore: 0,
      });
      expect(postService.getTrendingById).toHaveBeenCalledWith(postId);
    });
  });
});
