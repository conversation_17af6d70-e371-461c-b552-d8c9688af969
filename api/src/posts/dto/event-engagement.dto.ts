import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const NotificationChannels = ['push', 'email', 'in_app'] as const;
type NotificationChannel = (typeof NotificationChannels)[number];

export const eventEngagementSchema = z.object({
  type: z.literal('rsvp'),
  notificationPreferences: z
    .object({
      optedIn: z.boolean().default(true),
      reminderTimes: z
        .array(z.number().int().positive())
        .min(1)
        .max(5)
        .default([1440, 60, 10]), // Default to 24 hours, 1 hour, and 10 minutes
      channels: z
        .array(z.enum(NotificationChannels))
        .default(['push', 'email']),
    })
    .optional(),
});

export class EventEngagementDto extends createZodDto(eventEngagementSchema) {
  @ApiProperty({ enum: ['rsvp'], example: 'rsvp' })
  override type!: 'rsvp';

  @ApiProperty({
    required: false,
    example: {
      optedIn: true,
      reminderTimes: [1440, 60, 10],
      channels: ['push', 'email'],
    },
    description: 'Notification preferences for this event',
  })
  override notificationPreferences?: {
    optedIn: boolean;
    reminderTimes: number[];
    channels: NotificationChannel[];
  };
}
