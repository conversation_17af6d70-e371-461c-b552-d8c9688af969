import { querySchema } from '@/common/dto/query-params.dto';
import { insertPostSchema, posts } from '@/db/schema/posts';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { createQueryBooleanSchema } from '@/common/utils/zod-schema.utils';
import { format, isBefore } from 'date-fns';

export const urlRegex =
  /^(https?:\/\/)?([a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,})(:[0-9]{1,5})?(\/[^\s?#]*)?(\?[^#]*)?(#.*)?$/;
export const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)(?::([0-5]\d))?$/;
// Use the shared utility for boolean schema
export const booleanSchema = createQueryBooleanSchema(false);

export function createArrayOfNumbersSchema(fieldName: string) {
  return z.preprocess(
    (val) => {
      // Handle different input formats
      if (typeof val === 'string') {
        // Empty string case
        if (!val.trim()) {
          return [];
        }

        try {
          // Try to parse as JSON
          if (val.startsWith('[') && val.endsWith(']')) {
            const parsed = JSON.parse(val);
            if (Array.isArray(parsed)) {
              return parsed.map(Number);
            }
            return [Number(parsed)];
          }

          // Handle comma-separated string
          return val
            .split(',')
            .map((item) => item.trim())
            .filter(Boolean)
            .map(Number);
        } catch (error) {
          // We'll just handle the error silently and return an empty array
          // This is fine since we're handling empty arrays as valid

          // If parsing fails, try to convert the whole string to a number
          const num = Number(val);
          return isNaN(num) ? [] : [num];
        }
      }

      // If already an array, convert all elements to numbers
      if (Array.isArray(val)) {
        return val.map(Number);
      }

      // If it's a single number, return as a single-item array
      if (typeof val === 'number') {
        return [val];
      }

      // For any other type, return empty array
      return [];
    },
    z
      .array(z.number())
      .refine((arr) => arr.length === 0 || arr.every((num) => !isNaN(num)), {
        message: `All elements in ${fieldName} must be valid numbers`,
      })
      .describe(`Array of numbers for ${fieldName}`),
  );
}

export function createArrayOfStringsSchema(fieldName: string) {
  return z.preprocess(
    (val) => {
      // Handle different input formats
      if (typeof val === 'string') {
        // Empty string case
        if (!val.trim()) {
          return [];
        }

        try {
          // Try to parse as JSON
          if (val.startsWith('[') && val.endsWith(']')) {
            return JSON.parse(val);
          }

          // Handle comma-separated string
          return val
            .split(',')
            .map((item) => item.trim())
            .filter(Boolean);
        } catch (error) {
          // We'll just handle the error silently and return an empty array if parsing fails
          // This is fine since we're handling empty arrays as valid

          // If it's not empty, return as a single-item array
          return val.trim() ? [val] : [];
        }
      }

      // If already an array, return as is
      if (Array.isArray(val)) {
        return val;
      }

      // For any other type, return empty array
      return [];
    },
    z.array(z.string()).describe(`Array of strings for ${fieldName}`),
  );
}

const allowedRecipients = [
  'student',
  'student_admin',
  'admin',
  'super_admin',
  'all',
] as const;

const allowedChannels = ['push', 'in_app', 'email', 'all'] as const;

function withAllowedStrings<T extends string>(
  schema: z.ZodTypeAny,
  allowed: readonly T[],
  fieldName = 'field',
) {
  return schema.refine(
    (arr): arr is T[] => arr.every((val: T) => allowed.includes(val)),
    {
      message: `${fieldName} must contain only: ${allowed.join(', ')}`,
    },
  );
}

export const notificationDeliverTypeSchema = withAllowedStrings(
  createArrayOfStringsSchema('notificationDeliverType'),
  allowedChannels,
  'notificationDeliverType',
).optional();

export const notificationRecipientsSchema = withAllowedStrings(
  createArrayOfStringsSchema('notificationRecipients'),
  allowedRecipients,
  'notificationRecipients',
).optional();

const createPostSchema = insertPostSchema
  .extend({
    status: z.enum(['active', 'draft', 'scheduled']),
    title: z.string().trim(),
    isGlobal: createQueryBooleanSchema(false),
    notify_users: createQueryBooleanSchema(false),
    notificationDeliverType: notificationDeliverTypeSchema,
    notificationRecipients: notificationRecipientsSchema,
    scheduledAt: z
      .string()
      .optional()
      .refine((date) => !date || !isNaN(Date.parse(date)), {
        message: 'Scheduled at must be a valid date',
      })
      .transform((date) =>
        date ? format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS') : null,
      ),
  })
  .superRefine((data, ctx) => {
    const { status, description, title, scheduledAt } = data;
    const now = new Date();

    // Title length validation
    if (title.length < 5) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Title must be at least 5 characters long',
        path: ['title'],
      });
    }

    // Description length validation for active status
    if (status === 'active' && (!description || description.length <= 10)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Description must be at least 10 characters long',
        path: ['description'],
      });
    }

    if (status === 'scheduled') {
      if (!scheduledAt) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at is required when status is scheduled',
          path: ['scheduledAt'],
        });
      } else if (isBefore(scheduledAt, now)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at must be a future date',
          path: ['scheduledAt'],
        });
      }
    }

    if (scheduledAt && status !== 'scheduled') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status must be scheduled when scheduledAt is provided',
        path: ['status'],
      });
    }

    if (scheduledAt && isBefore(scheduledAt, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Scheduled at must be a future date',
        path: ['scheduledAt'],
      });
    }
  });

const updatePostSchema = insertPostSchema
  .extend({
    status: z.enum(['active', 'draft', 'scheduled']),
    title: z.string().trim(),
    imageUrl: z.string().url().optional(),
    isGlobal: createQueryBooleanSchema(false),
    notify_users: createQueryBooleanSchema(false),
    notificationDeliverType: notificationDeliverTypeSchema,
    notificationRecipients: notificationRecipientsSchema,
    scheduledAt: z
      .string()
      .optional()
      .refine((date) => !date || !isNaN(Date.parse(date)), {
        message: 'Scheduled at must be a valid date',
      })
      .transform((date) =>
        date ? format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS') : null,
      ),
  })
  .superRefine((data, ctx) => {
    const { status, description, title, scheduledAt } = data;
    const now = new Date();

    // Title length validation
    if (title.length < 5) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Title must be at least 5 characters long',
        path: ['title'],
      });
    }

    // Description length validation for active status
    if (status === 'active' && (!description || description.length <= 10)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Description must be at least 10 characters long',
        path: ['description'],
      });
    }

    // scheduledAt validation based on status
    if (status === 'scheduled') {
      if (!scheduledAt) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at is required when status is scheduled',
          path: ['scheduledAt'],
        });
      } else if (isBefore(scheduledAt, now)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at must be a future date',
          path: ['scheduledAt'],
        });
      }
    }

    // If scheduledAt is provided but status is not scheduled, it should be scheduled
    if (scheduledAt && status !== 'scheduled') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status must be scheduled when scheduledAt is provided',
        path: ['status'],
      });
    }

    // General scheduledAt validation: must be after now if provided
    if (scheduledAt && isBefore(scheduledAt, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Scheduled at must be a future date',
        path: ['scheduledAt'],
      });
    }
  });

export const commonInsertEventOrOpportunitySchema = insertPostSchema.extend({
  title: z.string().trim().min(5),
  isGlobal: booleanSchema,
  notify_users: createQueryBooleanSchema(false),
  notificationDeliverType: notificationDeliverTypeSchema,
  notificationRecipients: notificationRecipientsSchema,
  countries: createArrayOfStringsSchema('countries'),
  institutions: createArrayOfStringsSchema('institutions'),
  startDate: z
    .string()
    .refine((date) => !isNaN(Date.parse(date)), {
      message: 'Start date must be a valid date',
    })
    .transform((date) => format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS')),
  endDate: z
    .string()
    .refine((date) => !isNaN(Date.parse(date)), {
      message: 'End date must be a valid date',
    })
    .transform((date) => format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS')),

  startTime: z
    .string()
    .optional()
    .transform((val) => (val?.length ? val : null)),

  endTime: z
    .string()
    .optional()
    .transform((val) => (val?.length ? val : null)),
  scheduledAt: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), {
      message: 'Scheduled at must be a valid date',
    })
    .transform((date) =>
      date ? format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS') : null,
    ),
});

const createEventSchema = commonInsertEventOrOpportunitySchema
  .extend({
    virtualLink: z
      .string()
      .url()
      .regex(urlRegex, { message: 'Invalid virtual url' })
      .optional(),
    status: z.enum(['active', 'draft', 'scheduled']),
    imageUrl: z.string().url().optional(),
  })
  .superRefine((data, ctx) => {
    const now = new Date();

    // Non-global events must have countries or institutions
    if (
      !data.isGlobal &&
      data.countries.length === 0 &&
      data.institutions.length === 0
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          'Countries or institutions must be provided for non-global events',
        path: ['countries'],
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          'Countries or institutions must be provided for non-global events',
        path: ['institutions'],
      });
    }

    // Global events must not have countries or institutions
    if (
      data.isGlobal &&
      (data.countries.length > 0 || data.institutions.length > 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Countries and institutions must be empty for global events',
        path: ['countries'],
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Countries and institutions must be empty for global events',
        path: ['institutions'],
      });
    }

    // Description length for active status
    if (
      data.status === 'active' &&
      (!data.description || data.description.length <= 10)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Description must be at least 10 characters long',
        path: ['description'],
      });
    }

    // Start date must not be before now
    if (isBefore(data.startDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date must be after current date',
        path: ['startDate'],
      });
    }

    // End date must not be before now
    if (isBefore(data.endDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date must be after current date',
        path: ['endDate'],
      });
    }

    // End date cannot be before start date
    if (isBefore(data.endDate, data.startDate)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date cannot be before start date',
        path: ['endDate'],
      });
    }

    // scheduledAt validation based on status
    if (data.status === 'scheduled') {
      if (!data.scheduledAt) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at is required when status is scheduled',
          path: ['scheduledAt'],
        });
      } else if (isBefore(data.scheduledAt, now)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at must be a future date',
          path: ['scheduledAt'],
        });
      }
    }

    // If scheduledAt is provided but status is not scheduled, it should be scheduled
    if (data.scheduledAt && data.status !== 'scheduled') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status must be scheduled when scheduledAt is provided',
        path: ['status'],
      });
    }

    // General scheduledAt validation: must be after now if provided
    if (data.scheduledAt && isBefore(data.scheduledAt, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Scheduled at must be a future date',
        path: ['scheduledAt'],
      });
    }
  });

const updateEventSchema = commonInsertEventOrOpportunitySchema
  .extend({
    virtualLink: z
      .string()
      .url()
      .regex(urlRegex, { message: 'Invalid virtual url' })
      .optional(),
    status: z.enum(['active', 'draft', 'scheduled']),
    imageUrl: z.string().url().optional(),
  })
  .superRefine((data, ctx) => {
    const now = new Date();

    // Title length validation
    if (data.title.length < 5) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Title must be at least 5 characters long',
        path: ['title'],
      });
    }

    // Non-global events must have countries or institutions
    if (
      !data.isGlobal &&
      data.countries.length === 0 &&
      data.institutions.length === 0
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          'Countries or institutions must be provided for non-global events',
        path: ['countries'],
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          'Countries or institutions must be provided for non-global events',
        path: ['institutions'],
      });
    }

    // Global events must not have countries or institutions
    if (
      data.isGlobal &&
      (data.countries.length > 0 || data.institutions.length > 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Countries and institutions must be empty for global events',
        path: ['countries'],
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Countries and institutions must be empty for global events',
        path: ['institutions'],
      });
    }

    // Description length for active status
    if (
      data.status === 'active' &&
      (!data.description || data.description.length <= 10)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Description must be at least 10 characters long',
        path: ['description'],
      });
    }

    // Start date must not be before now
    if (isBefore(data.startDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date must be after current date',
        path: ['startDate'],
      });
    }

    // End date must not be before now
    if (isBefore(data.endDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date must be after current date',
        path: ['endDate'],
      });
    }

    // End date cannot be before start date
    if (isBefore(data.endDate, data.startDate)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date cannot be before start date',
        path: ['endDate'],
      });
    }

    // scheduledAt validation based on status
    if (data.status === 'scheduled') {
      if (!data.scheduledAt) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at is required when status is scheduled',
          path: ['scheduledAt'],
        });
      } else if (isBefore(data.scheduledAt, now)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at must be a future date',
          path: ['scheduledAt'],
        });
      }
    }

    // If scheduledAt is provided but status is not scheduled, it should be scheduled
    if (data.scheduledAt && data.status !== 'scheduled') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status must be scheduled when scheduledAt is provided',
        path: ['status'],
      });
    }

    // General scheduledAt validation: must be after now if provided
    if (data.scheduledAt && isBefore(data.scheduledAt, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Scheduled at must be a future date',
        path: ['scheduledAt'],
      });
    }
  });

export const postKeys = Object.keys(posts) as [string, ...string[]];

export const postQueryParamsSchema = querySchema.extend({
  sort: z.enum(postKeys).optional().default('created_at'),
  type: z.enum(['event', 'opportunity', 'general']).optional(),
  status: z.enum(['active', 'expired', 'draft', 'scheduled']).optional(),
  startDate: z
    .string()
    .optional()
    .refine(
      (val) =>
        !val ||
        ['past', 'upcoming', 'ongoing'].includes(val) ||
        !isNaN(Date.parse(val)),
      {
        message:
          'Invalid value for startDate. It must be "past", "upcoming", "today", or a valid date string.',
      },
    ),
});

export const postEngagementParamsSchema = querySchema.extend({
  type: z.enum(['like', 'share']).optional().default('like'),
});
/**
 * Zod schema for post notification parameters
 */
export const postNotificationParamsSchema = z.object({
  // Required fields
  postId: z.string().uuid('Post ID must be a valid UUID'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  postType: z.string().min(1, 'Post type is required'),
  isGlobal: createQueryBooleanSchema(false),
  // Optional fields
  clubId: z.string().uuid('Club ID must be a valid UUID').optional(),
  countries: createArrayOfStringsSchema('countries').optional(),
  institutions: createArrayOfStringsSchema('institutions').optional(),
  channels: z.array(z.enum(['push', 'email', 'in_app'])).optional(),
  recipients: z
    .array(z.enum(['student', 'student_admin', 'admin', 'super_admin']))
    .optional(),
});
/**
 * DTO class for post notification parameters
 */
export class PostNotificationParamsDto extends createZodDto(
  postNotificationParamsSchema,
) {}
/**
 * Type for post notification parameters
 */
export type PostNotificationParams = z.infer<
  typeof postNotificationParamsSchema
>;
export class postDto extends createZodDto(createPostSchema) {}
export class updatePostDto extends createZodDto(updatePostSchema) {}
export class eventDto extends createZodDto(createEventSchema) {}
export class updateEventDto extends createZodDto(updateEventSchema) {}
export class postQueryParamsDto extends createZodDto(postQueryParamsSchema) {}
export class PostEngagementParamsDto extends createZodDto(
  postEngagementParamsSchema,
) {}
