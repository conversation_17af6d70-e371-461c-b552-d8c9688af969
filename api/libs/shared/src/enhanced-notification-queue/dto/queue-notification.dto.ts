import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';
import { notification_channels } from '@/db/schema/notification_system';
import { querySchema } from '@/common/dto/query-params.dto';

const notificationChannelSchema = z.enum(notification_channels);

// Create a separate schema for queue-based notifications
export const queueNotificationSchema = querySchema.extend({
  notification_type_id: z.string().uuid('Invalid notification type ID'),
  title: z.string().optional(),
  body: z.string().optional(),
  data: z.record(z.any()).optional(),
  channels: z.array(notificationChannelSchema).optional(),
  userId: z.string().uuid('Invalid user ID'),
});

export class QueueNotificationDto extends createZodDto(
  queueNotificationSchema,
) {}
