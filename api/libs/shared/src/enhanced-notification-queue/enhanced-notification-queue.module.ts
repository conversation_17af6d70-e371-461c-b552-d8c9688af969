import { Module } from '@nestjs/common';
import { EnhancedNotificationQueueService } from './enhanced-notification-queue.service';
import { EnhancedNotificationQueueController } from './enhanced-notification-queue.controller';
import { NotificationQueueScheduler } from './notification-queue.scheduler';
import { FirebaseModule } from '@/firebase/firebase.module';
import { EmailModule } from '@/mail/email.module';
import { NotificationModule } from '../notification/notification.module';
import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [
    FirebaseModule,
    EmailModule,
    NotificationModule,
    QueueModule.forFeature(),
  ],
  controllers: [EnhancedNotificationQueueController],
  providers: [EnhancedNotificationQueueService, NotificationQueueScheduler],
  exports: [EnhancedNotificationQueueService],
})
export class EnhancedNotificationQueueModule {}
