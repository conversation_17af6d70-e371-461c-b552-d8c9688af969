import { Body, Controller, Get, Logger, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { EnhancedNotificationQueueService } from './enhanced-notification-queue.service';
import { QueueNotificationDto } from './dto/queue-notification.dto';

@Controller({ version: '1', path: 'notifications-queue' })
@ApiTags('Notifications Queue')
export class EnhancedNotificationQueueController {
  private readonly logger = new Logger(
    EnhancedNotificationQueueController.name,
  );

  constructor(
    private readonly notificationService: EnhancedNotificationQueueService,
  ) {}

  /**
   * Send immediate notification to a specific user
   *
   * This endpoint sends a notification to a single user through the queue system.
   * For sending to multiple users based on filters, use the regular /notifications/send endpoint.
   */
  @Post('send')
  @ApiOperation({
    summary: 'Send an immediate notification to a specific user using queue',
    description:
      'Sends a notification to a single user. The userId parameter is required.',
  })
  @ApiResponse({ status: 200, description: 'Notification queued successfully' })
  @ApiResponse({
    status: 400,
    description: 'Validation failed - missing or invalid parameters',
  })
  async sendNotification(@Body() data: QueueNotificationDto) {
    try {
      // userId is now required by the DTO validation
      const userId = data.userId;

      const jobId = await this.notificationService.sendNotificationToUsers({
        userId: userId,
        notificationTypeId: data.notification_type_id,
        data: data.data,
        channels: data.channels,
      });

      return {
        success: true,
        message: 'Notification queued successfully',
        jobId,
      };
    } catch (error) {
      this.logger.error('Error queuing notification', error);
      throw error;
    }
  }

  /**
   * Get notification job status
   */
  @Get('job/:id')
  @ApiOperation({ summary: 'Get notification job status' })
  @ApiResponse({
    status: 200,
    description: 'Job status retrieved successfully',
  })
  async getJobStatus(@Param('id', CustomParseUUIDPipe) jobId: string) {
    try {
      const status =
        await this.notificationService.getNotificationJobStatus(jobId);
      return { success: true, status };
    } catch (error) {
      this.logger.error('Error getting job status', error);
      throw error;
    }
  }

  /**
   * Get notification queue metrics
   */
  @Get('metrics')
  @ApiOperation({ summary: 'Get notification queue metrics' })
  @ApiResponse({
    status: 200,
    description: 'Queue metrics retrieved successfully',
  })
  async getQueueMetrics() {
    try {
      const metrics =
        await this.notificationService.getNotificationQueueMetrics();
      return { success: true, metrics };
    } catch (error) {
      this.logger.error('Error getting queue metrics', error);
      throw error;
    }
  }

  /**
   * Get notification types
   */
  @Get('types')
  @ApiOperation({ summary: 'Get notification types' })
  @ApiResponse({ status: 200, description: 'Types retrieved successfully' })
  async getNotificationTypes() {
    try {
      const types = await this.notificationService.getNotificationTypes();
      return { success: true, data: types };
    } catch (error) {
      this.logger.error('Error getting notification types', error);
      throw error;
    }
  }
}
