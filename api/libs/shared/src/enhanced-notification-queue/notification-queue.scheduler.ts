import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EnhancedNotificationQueueService } from './enhanced-notification-queue.service';

@Injectable()
export class NotificationQueueScheduler {
  private readonly logger = new Logger(NotificationQueueScheduler.name);

  constructor(
    private readonly notificationService: EnhancedNotificationQueueService,
  ) {}

  /**
   * Process scheduled notifications every 5 minutes for timely delivery
   * Note: Debug logs removed to reduce log noise in production
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async processScheduledNotifications() {
    try {
      await this.notificationService.processScheduledNotifications();
    } catch (error) {
      this.logger.error(
        'Error processing scheduled notifications',
        error instanceof Error ? error.stack : String(error),
      );
    }
  }

  /**
   * Clean up old notification logs (older than 30 days) once a day
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupOldNotificationLogs() {
    try {
      const deletedCount =
        await this.notificationService.cleanupOldNotificationLogs(30); // Keep logs for 30 days

      // Only log if there were logs to clean up
      if (deletedCount > 0) {
        this.logger.log(
          `Cleaned up ${deletedCount} old notification logs (>30 days)`,
        );
      }
    } catch (error) {
      this.logger.error(
        'Error cleaning up old notification logs',
        error instanceof Error ? error.stack : String(error),
      );
    }
  }
}
