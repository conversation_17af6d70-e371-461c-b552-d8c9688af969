import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  notification_logs,
  notification_preferences,
  notification_templates,
  notification_types,
  scheduled_notifications,
} from '@/db/schema/notification_system';
import { and, eq, sql } from 'drizzle-orm';
import { Subject } from 'rxjs';
import { QueueService } from '../queue/queue.service';
import { QueueName } from '../queue/queue.constants';

@Injectable()
export class EnhancedNotificationQueueService {
  private readonly logger = new Logger(EnhancedNotificationQueueService.name);
  private readonly inAppNotificationSubjects: Map<string, Subject<any>> =
    new Map();

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly queueService: QueueService,
  ) {}

  /**
   * Send notification to multiple users based on roles and filters
   * Adds the bulk notification to the queue for processing
   */
  async sendNotificationToUser(params: {
    notificationTypeId: string;
    data?: Record<string, any>;
    targetAudience: {
      roles?: string[];
      filters?: Record<string, any>;
    };
    channels?: string[];
    overridePreferences?: boolean;
    jobId?: string; // Optional job ID to prevent duplicate jobs
  }): Promise<string> {
    try {
      const sanitizedTargetAudience = this.sanitizeTargetAudience(
        params.targetAudience,
      );

      if (sanitizedTargetAudience.filters?.userId) {
        const userId = sanitizedTargetAudience.filters.userId;

        const jobId = `notification-${params.notificationTypeId}-${userId}-${Date.now()}`;

        return await this.queueService.addSingleNotificationJob(
          {
            userId,
            notificationTypeId: params.notificationTypeId,
            data: params.data || {},
            channels: params.channels || [],
            overridePreferences: params.overridePreferences,
          },
          jobId,
        );
      }

      const timestamp = Date.now();
      const jobId = `notification-bulk-${params.notificationTypeId}-${timestamp}`;

      if (sanitizedTargetAudience.filters) {
        sanitizedTargetAudience.filters.userState = 'active';
      } else {
        sanitizedTargetAudience.filters = { userState: 'active' };
      }

      try {
        const notificationType = await this.getNotificationType(
          params.notificationTypeId,
        );
        if (
          notificationType &&
          (notificationType.code === 'new_opportunity' ||
            notificationType.module === 'opportunity')
        ) {
          sanitizedTargetAudience.filters!.userState = 'active';
        }
      } catch {
        // Silently continue
      }

      if (
        !sanitizedTargetAudience.roles ||
        sanitizedTargetAudience.roles.length === 0
      ) {
        sanitizedTargetAudience.roles = ['student', 'student_admin', 'admin'];
      }

      const enhancedData = {
        ...(params.data || {}),
        timestamp: new Date(timestamp).toISOString(),
      };

      const queueJobId = await this.queueService.addBulkNotificationJob(
        {
          notificationTypeId: params.notificationTypeId,
          data: enhancedData,
          targetAudience: sanitizedTargetAudience,
          channels: params.channels || [],
          overridePreferences: params.overridePreferences,
        },
        jobId,
      );

      return queueJobId;
    } catch (error) {
      this.logger.error(
        `Failed to queue bulk notification:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Sanitize target audience to prevent infinite loops
   * @param targetAudience The target audience to sanitize
   * @returns The sanitized target audience
   */
  private sanitizeTargetAudience(targetAudience: {
    roles?: string[];
    filters?: Record<string, any>;
  }): {
    roles?: string[];
    filters?: Record<string, any>;
  } {
    if (!targetAudience) {
      return {};
    }

    const sanitized = {
      roles: targetAudience.roles ? [...targetAudience.roles] : undefined,
      filters: targetAudience.filters
        ? { ...targetAudience.filters }
        : undefined,
    };

    return sanitized;
  }

  /**
   * Schedule a notification for future delivery
   * @param params Notification scheduling parameters
   * @returns The created scheduled notification
   */
  async scheduleNotification(params: {
    notificationTypeId: string;
    title: string;
    body: string;
    data?: Record<string, any>;
    targetAudience: {
      roles?: string[];
      filters?: Record<string, any>;
    };
    scheduledFor: Date;
    channels: string[];
    createdBy: string;
  }): Promise<any> {
    const {
      notificationTypeId,
      title,
      body,
      data = {},
      targetAudience,
      scheduledFor,
      channels,
      createdBy,
    } = params;

    try {
      // Validate notification type exists
      const notificationType =
        await this.getNotificationType(notificationTypeId);
      if (!notificationType) {
        throw new NotFoundException(
          `Notification type with ID ${notificationTypeId} not found`,
        );
      }

      // Create scheduled notification
      const [scheduledNotification] = await this.drizzle.db
        .insert(scheduled_notifications)
        .values({
          notification_type_id: notificationTypeId,
          title,
          body,
          data,
          target_audience: targetAudience,
          scheduled_for: scheduledFor.toISOString(),
          channels,
          created_by: createdBy,
          status: 'pending',
        })
        .returning();

      return scheduledNotification;
    } catch (error) {
      this.logger.error(
        `Failed to schedule notification:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Process scheduled notifications that are due
   * @param batchSize Optional number of notifications to process in one batch
   * @returns Job ID of the scheduled notifications processing job
   */
  async processScheduledNotifications(batchSize?: number): Promise<string> {
    try {
      const jobId = await this.queueService.addProcessScheduledNotificationsJob(
        {
          batchSize,
        },
      );

      return jobId;
    } catch (error) {
      this.logger.error(
        `Failed to queue scheduled notifications processing:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Get job status for a notification job
   * @param jobId ID of the job to check
   * @returns Status of the notification job
   */
  async getNotificationJobStatus(jobId: string): Promise<any> {
    return this.queueService.getJobStatus(QueueName.NOTIFICATION, jobId);
  }

  /**
   * Get notification queue metrics
   * @returns Metrics for the notification queue
   */
  async getNotificationQueueMetrics(): Promise<any> {
    return this.queueService.getQueueMetrics(QueueName.NOTIFICATION);
  }

  /**
   * Get all notification templates
   * @returns Array of notification templates
   */
  async getNotificationTemplates(): Promise<any[]> {
    const templates = await this.drizzle.db
      .select()
      .from(notification_templates);

    return templates;
  }

  /**
   * Create notification template
   * @param data Template data to create
   * @returns The created notification template
   */
  async createNotificationTemplate(data: {
    name: string;
    description: string;
    title_template: string;
    body_template: string;
    email_subject_template?: string;
    email_body_template?: string;
    created_by?: string;
  }): Promise<any> {
    try {
      const [template] = await this.drizzle.db
        .insert(notification_templates)
        .values(data)
        .returning();

      return template;
    } catch (error) {
      this.logger.error(
        `Failed to create notification template:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Update notification template
   * @param id ID of the template to update
   * @param data Template data to update
   * @returns The updated notification template
   */
  async updateNotificationTemplate(
    id: string,
    data: Partial<{
      name: string;
      description: string;
      title_template: string;
      body_template: string;
      email_subject_template?: string;
      email_body_template?: string;
    }>,
  ): Promise<any> {
    try {
      const [template] = await this.drizzle.db
        .update(notification_templates)
        .set(data)
        .where(eq(notification_templates.id, id))
        .returning();

      if (!template) {
        throw new NotFoundException(`Template with ID ${id} not found`);
      }

      return template;
    } catch (error) {
      this.logger.error(
        `Failed to update notification template:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Get notification template by ID
   * @param id ID of the template to retrieve
   * @returns The notification template
   */
  async getNotificationTemplate(id: string): Promise<any> {
    const [template] = await this.drizzle.db
      .select()
      .from(notification_templates)
      .where(eq(notification_templates.id, id));

    return template;
  }

  /**
   * Get all notification types
   * @returns Array of notification types with their templates
   */
  async getNotificationTypes(): Promise<any[]> {
    const types = await this.drizzle.db
      .select()
      .from(notification_types)
      .leftJoin(
        notification_templates,
        eq(notification_types.template_id, notification_templates.id),
      )
      .orderBy(notification_types.name);

    return types.map((row) => ({
      ...row.notification_types,
      template: row.notification_templates,
    }));
  }

  /**
   * Create notification type
   * @param data Notification type data to create
   * @returns The created notification type
   */
  async createNotificationType(data: {
    code: string;
    name: string;
    description: string;
    module: string;
    template_id: string;
    default_channels: string[];
  }): Promise<any> {
    try {
      const [type] = await this.drizzle.db
        .insert(notification_types)
        .values(data)
        .returning();

      return type;
    } catch (error) {
      this.logger.error(
        `Failed to create notification type:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Update notification type
   * @param id ID of the notification type to update
   * @param data Notification type data to update
   * @returns The updated notification type
   */
  async updateNotificationType(
    id: string,
    data: Partial<{
      name: string;
      description: string;
      module: string;
      template_id: string;
      default_channels: string[];
    }>,
  ): Promise<any> {
    try {
      const [type] = await this.drizzle.db
        .update(notification_types)
        .set(data)
        .where(eq(notification_types.id, id))
        .returning();

      if (!type) {
        throw new NotFoundException(
          `Notification type with ID ${id} not found`,
        );
      }

      return type;
    } catch (error) {
      this.logger.error(
        `Failed to update notification type:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Get notification type by ID
   * @param id ID of the notification type to retrieve
   * @returns The notification type
   */
  async getNotificationType(id: string): Promise<any> {
    const [type] = await this.drizzle.db
      .select()
      .from(notification_types)
      .where(eq(notification_types.id, id));

    return type;
  }

  /**
   * Get notification type by code
   * @param code Code of the notification type to retrieve
   * @returns The notification type
   */
  async getNotificationTypeByCode(code: string): Promise<any> {
    const [type] = await this.drizzle.db
      .select()
      .from(notification_types)
      .where(eq(notification_types.code, code));

    return type;
  }

  /**
   * Get user notification preferences
   * @param userId ID of the user to get preferences for
   * @returns Array of user notification preferences with their types
   */
  async getUserNotificationPreferences(userId: string): Promise<any[]> {
    const preferences = await this.drizzle.db
      .select()
      .from(notification_preferences)
      .leftJoin(
        notification_types,
        eq(
          notification_preferences.notification_type_id,
          notification_types.id,
        ),
      )
      .where(eq(notification_preferences.user_id, userId));

    return preferences.map((row) => ({
      ...row.notification_preferences,
      type: row.notification_types,
    }));
  }

  /**
   * Get user notification preference for a specific type
   * @param userId ID of the user to get preference for
   * @param notificationTypeId ID of the notification type
   * @returns The user's notification preference for the specified type
   */
  async getUserNotificationPreference(
    userId: string,
    notificationTypeId: string,
  ): Promise<any> {
    const [preference] = await this.drizzle.db
      .select()
      .from(notification_preferences)
      .where(
        and(
          eq(notification_preferences.user_id, userId),
          eq(notification_preferences.notification_type_id, notificationTypeId),
        ),
      );

    return preference;
  }

  /**
   * Update user notification preferences
   * @param userId ID of the user to update preferences for
   * @param preferences Array of preference updates
   * @returns Array of updated preferences
   */
  async updateUserNotificationPreferences(
    userId: string,
    preferences: Array<{
      notification_type_id: string;
      email_enabled?: boolean;
      push_enabled?: boolean;
      in_app_enabled?: boolean;
      data?: Record<string, any>;
    }>,
  ): Promise<any[]> {
    try {
      const results = [];

      for (const pref of preferences) {
        // Check if preference exists
        const existingPref = await this.getUserNotificationPreference(
          userId,
          pref.notification_type_id,
        );

        if (existingPref) {
          // Update existing preference
          const [updated] = await this.drizzle.db
            .update(notification_preferences)
            .set({
              email_enabled:
                pref.email_enabled !== undefined
                  ? pref.email_enabled
                  : existingPref.email_enabled,
              push_enabled:
                pref.push_enabled !== undefined
                  ? pref.push_enabled
                  : existingPref.push_enabled,
              in_app_enabled:
                pref.in_app_enabled !== undefined
                  ? pref.in_app_enabled
                  : existingPref.in_app_enabled,
            })
            .where(
              and(
                eq(notification_preferences.user_id, userId),
                eq(
                  notification_preferences.notification_type_id,
                  pref.notification_type_id,
                ),
              ),
            )
            .returning();

          results.push(updated);
        } else {
          // Create new preference
          const [created] = await this.drizzle.db
            .insert(notification_preferences)
            .values({
              user_id: userId,
              notification_type_id: pref.notification_type_id,
              email_enabled:
                pref.email_enabled !== undefined ? pref.email_enabled : true,
              push_enabled:
                pref.push_enabled !== undefined ? pref.push_enabled : true,
              in_app_enabled:
                pref.in_app_enabled !== undefined ? pref.in_app_enabled : true,
            })
            .returning();

          results.push(created);
        }
      }

      return results;
    } catch (error) {
      this.logger.error(
        `Failed to update notification preferences for user ${userId}:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Get user notification logs
   * @param userId ID of the user to get logs for
   * @param options Pagination and filtering options
   * @returns Paginated notification logs
   */
  async getUserNotificationLogs(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      unreadOnly?: boolean;
    } = {},
  ): Promise<any> {
    try {
      const { limit = 50, offset = 0, unreadOnly = false } = options;

      const query = this.drizzle.db
        .select()
        .from(notification_logs)
        .leftJoin(
          notification_types,
          eq(notification_logs.notification_type_id, notification_types.id),
        )
        .where(eq(notification_logs.user_id, userId))
        .orderBy(sql`${notification_logs.created_at} DESC`)
        .limit(limit)
        .offset(offset);

      let finalQuery = query;
      if (unreadOnly) {
        finalQuery = this.drizzle.db
          .select()
          .from(notification_logs)
          .leftJoin(
            notification_types,
            eq(notification_logs.notification_type_id, notification_types.id),
          )
          .where(
            and(
              eq(notification_logs.user_id, userId),
              sql`${notification_logs.read_at} IS NULL`,
            ),
          )
          .orderBy(sql`${notification_logs.created_at} DESC`)
          .limit(limit)
          .offset(offset);
      }

      const logs = await finalQuery;

      const countResult = await this.drizzle.db
        .select({ count: sql<number>`count(*)` })
        .from(notification_logs)
        .where(eq(notification_logs.user_id, userId));

      return {
        logs: logs.map((row) => ({
          ...row.notification_logs,
          type: row.notification_types,
        })),
        total: Number(countResult[0]?.count || 0),
        limit,
        offset,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get notification logs for user ${userId}:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Mark notification as read
   * @param id ID of the notification to mark as read
   * @returns The updated notification
   */
  async markNotificationAsRead(id: string): Promise<any> {
    try {
      const [notification] = await this.drizzle.db
        .update(notification_logs)
        .set({ read_at: new Date().toISOString() })
        .where(eq(notification_logs.id, id))
        .returning();

      if (!notification) {
        throw new NotFoundException(`Notification with ID ${id} not found`);
      }

      return notification;
    } catch (error) {
      this.logger.error(
        `Failed to mark notification ${id} as read:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param userId ID of the user to mark all notifications as read
   * @returns Number of notifications marked as read
   */
  async markAllNotificationsAsRead(userId: string): Promise<number> {
    try {
      const result = await this.drizzle.db
        .update(notification_logs)
        .set({ read_at: new Date().toISOString() })
        .where(
          and(
            eq(notification_logs.user_id, userId),
            sql`${notification_logs.read_at} IS NULL`,
          ),
        );

      return result.rowCount || 0;
    } catch (error) {
      this.logger.error(
        `Failed to mark all notifications as read for user ${userId}:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Get in-app notification subject for a module
   * @param module Module name to get notification subject for
   * @returns Subject for the specified module or undefined if not found
   */
  getInAppNotificationSubject(module: string): Subject<any> | undefined {
    return this.inAppNotificationSubjects.get(module);
  }

  /**
   * Send in-app notification
   * @param userId ID of the user to send notification to
   * @param title Notification title
   * @param body Notification body
   * @param data Additional notification data
   * @param module Module name for the notification
   */
  private async sendInAppNotification(
    userId: string,
    title: string,
    body: string,
    data: Record<string, any>,
    module: string,
  ): Promise<void> {
    try {
      const subject = this.inAppNotificationSubjects.get(module);
      if (!subject) {
        this.logger.warn(
          `No in-app notification subject found for module ${module}`,
        );
        return;
      }

      subject.next({
        userId,
        title,
        body,
        data,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(
        `Failed to send in-app notification to user ${userId}:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Send notification to a specific user
   * @param params Notification parameters including userId and notification details
   * @returns Job ID of the notification job
   */
  async sendNotificationToUsers(params: {
    userId: string;
    notificationTypeId: string;
    title?: string;
    body?: string;
    data?: Record<string, any>;
    overridePreferences?: boolean;
    channels?: string[];
  }): Promise<string> {
    try {
      const jobId = await this.queueService.addSingleNotificationJob({
        userId: params.userId,
        notificationTypeId: params.notificationTypeId,
        title: params.title,
        body: params.body,
        data: params.data || {},
        channels: params.channels || [],
        overridePreferences: params.overridePreferences,
      });

      // Also send in-app notification (fire-and-forget)
      await this.sendInAppNotification(
        params.userId,
        params.title ?? '',
        params.body ?? '',
        params.data || {},
        params.notificationTypeId, // or use a module name if needed
      );

      return jobId;
    } catch (error) {
      this.logger.error(
        `Failed to queue notification for user ${params.userId}:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }
  /**
   * Clean up old notification logs
   * @param daysToKeep Number of days to keep logs for (default: 30)
   * @returns Number of logs deleted
   */
  async cleanupOldNotificationLogs(daysToKeep: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await this.drizzle.db
        .delete(notification_logs)
        .where(
          sql`${notification_logs.created_at} < ${cutoffDate.toISOString()}`,
        );

      return result.rowCount || 0;
    } catch (error) {
      this.logger.error(
        `Failed to clean up old notification logs:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }
}
