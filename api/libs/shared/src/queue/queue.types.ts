import { PostStatus } from '@/db/schema';
import { JobsOptions } from 'bullmq';

/**
 * Base job data interface
 */
export interface BaseJobData {
  id?: string;
  createdAt?: Date;
}

/**
 * Single notification job data
 */
export interface SingleNotificationJobData extends BaseJobData {
  userId: string;
  notificationTypeId: string;
  title?: string;
  body?: string;
  data?: Record<string, any>;
  channels: string[];
  overridePreferences?: boolean;
}

/**
 * Bulk notification job data
 */
export interface BulkNotificationJobData extends BaseJobData {
  notificationTypeId: string;
  data?: Record<string, any>;
  targetAudience: {
    roles?: string[];
    filters?: Record<string, any>;
  };
  channels: string[];
  overridePreferences?: boolean;
}

/**
 * Process scheduled notifications job data
 */
export interface ProcessScheduledNotificationsJobData extends BaseJobData {
  batchSize?: number;
}

/**
 * Single email job data
 */
export interface SingleEmailJobData extends BaseJobData {
  to: string;
  subject: string;
  template: string;
  context: Record<string, any>;
}

/**
 * Bulk email job data
 */
export interface BulkEmailJobData extends BaseJobData {
  emails: Array<{
    to: string;
    subject: string;
    template: string;
    context: Record<string, any>;
  }>;
  batchSize?: number;
}

/**
 * Single push notification job data
 */
export interface SinglePushJobData extends BaseJobData {
  token: string;
  title: string;
  body: string;
  data?: Record<string, any>;
}

/**
 * Bulk push notification job data
 */
export interface BulkPushJobData extends BaseJobData {
  tokens: string[];
  title: string;
  body: string;
  data?: Record<string, any>;
  batchSize?: number;
}

/**
 * General task job data
 */
export interface GeneralTaskJobData extends BaseJobData {
  taskType: string;
  payload: Record<string, any>;
}

/**
 * Academic email reminder job data
 */
export interface AcademicEmailReminderJobData extends BaseJobData {
  rejectionDays: number;
  batchSize?: number;
}

/**
 * Bulk user deletion job data
 */
export interface BulkDeleteUsersJobData extends BaseJobData {
  userIds: string[];
  adminUser: {
    id: string;
    email: string;
    role: string;
    firstName?: string;
    lastName?: string;
  };
}

export interface UploadJobData extends BaseJobData {
  file: Express.Multer.File;
  postId: string;
  isLastImage?: boolean;
  status?: PostStatus;
}

/**
 * Single in-app notification job data
 */
export interface SingleInAppJobData extends BaseJobData {
  userId: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  module: string;
}

/**
 * Bulk in-app notification job data
 */
export interface BulkInAppJobData extends BaseJobData {
  userIds: string[];
  title: string;
  body: string;
  data?: Record<string, any>;
  module: string;
  batchSize?: number;
}

/**
 * Queue configuration options
 */
export interface QueueOptions {
  name: string;
  defaultJobOptions?: JobsOptions;
  concurrency?: number;
}

/**
 * Job result interface
 */
export interface JobResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: any;
}
