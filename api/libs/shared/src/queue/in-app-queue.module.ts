import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from './queue.constants';
import { InAppProcessor } from './processors/in-app.processor';
import { EnhancedNotificationModule } from '../enhanced-notification/enhanced-notification.module';

/**
 * Module for in-app notification queue processing
 *
 * Provides specialized configuration for handling in-app notifications
 * with enhanced Redis failover handling and timeout protection.
 */
@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.IN_APP,
      // Use consistent prefix across all queues for dashboard integration
      prefix: 'bull',

      // Specialized job options for in-app notifications
      defaultJobOptions: {
        attempts: 5, // Retry failed jobs 5 times
        backoff: {
          type: 'exponential', // Exponential backoff between retries
          delay: 1000, // Starting with 1s delay
        },
        removeOnComplete: {
          age: 7 * 24 * 60 * 60, // Keep completed jobs for 7 days
          count: 1000, // Keep last 1000 completed jobs
        },
        removeOnFail: {
          age: 14 * 24 * 60 * 60, // Keep failed jobs for 14 days
        },
      },

      // Note: Advanced queue options like lockDuration are set in the forRootAsync configuration
      // This ensures consistent failover handling across all queue processors
    }),
    EnhancedNotificationModule,
  ],
  providers: [InAppProcessor],
})
export class InAppQueueModule {}
