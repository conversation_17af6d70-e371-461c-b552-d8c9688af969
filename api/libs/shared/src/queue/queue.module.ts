import { DynamicModule, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { QueueService } from './queue.service';
import { EmailProcessor } from './processors/email.processor';
import { PushProcessor } from './processors/push.processor';
import { GeneralProcessor } from './processors/general.processor';
// InAppProcessor is now in a separate module to avoid circular dependencies
import { QueueName } from './queue.constants';
import { EmailModule } from '@/mail/email.module';
import { NotificationModule } from '../notification/notification.module';
import { UploadProcessor } from './processors/upload.processor';
import { UploadModule } from '@/upload/upload.module';
import { RepositoriesModule } from '@/repositories/repositories.module';
import { UploadQueueModule } from '../upload/upload.module';
import { ConfigurableDatabaseModule } from '@app/shared/drizzle/drizzle.module-definition';
import { RedisService } from '../redis/redis.service';

@Module({})
export class QueueModule {
  /**
   * Register the queue module with enhanced Redis failover handling
   *
   * This configuration:
   * 1. Sets up resilient Redis connections with timeout handling
   * 2. Configures BullMQ for optimal performance with 30,000+ users
   * 3. Implements special handling for Redis failover events
   * 4. Provides consistent job processing across all queue types
   *
   * @returns The dynamic module
   */
  static register(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        BullModule.forRootAsync({
          inject: [RedisService],
          useFactory: (redisService: RedisService) => {
            return redisService.createBullMQConfig();
          },
        }),
        BullModule.registerQueue(
          {
            name: QueueName.NOTIFICATION,
          },
          {
            name: QueueName.EMAIL,
          },
          {
            name: QueueName.PUSH,
          },
          {
            name: QueueName.GENERAL,
          },
          {
            name: QueueName.IN_APP,
          },
          {
            name: QueueName.UPLOAD,
            // Special configuration for the UPLOAD queue to handle failover events
            // More aggressive retry settings to handle Redis UNBLOCKED errors
            defaultJobOptions: {
              attempts: 10, // Increased from 5 to 10 for upload jobs
              backoff: {
                type: 'exponential',
                delay: 2000, // Start with a longer delay (2s vs 1s)
              },
              // Keep job history longer for upload jobs to aid debugging
              removeOnComplete: {
                age: 14 * 24 * 60 * 60, // Keep completed jobs for 14 days (vs 7)
                count: 1000, // Keep last 1000 completed jobs
              },
              removeOnFail: {
                age: 30 * 24 * 60 * 60, // Keep failed jobs for 30 days (vs 14)
              },
            },
          },
        ),
        EmailModule,
        NotificationModule,
        UploadModule,
        RepositoriesModule,
        UploadQueueModule,
        ConfigurableDatabaseModule.register({
          connectionString:
            process.env.DATABASE_URL ||
            'postgresql://postgres:postgres@localhost:5432/postgres',
        }),
      ],
      providers: [
        QueueService,
        EmailProcessor,
        PushProcessor,
        GeneralProcessor,
        UploadProcessor,
        // NotificationProcessor and InAppProcessor are now in separate modules
      ],
      exports: [QueueService],
    };
  }

  /**
   * Register the queue module for feature use (without BullMQ root configuration)
   * This provides access to QueueService without registering BullMQ root again
   *
   * @returns The dynamic module for feature use
   */
  static forFeature(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        // Register queues without the root configuration
        BullModule.registerQueue(
          { name: QueueName.NOTIFICATION },
          { name: QueueName.EMAIL },
          { name: QueueName.PUSH },
          { name: QueueName.GENERAL },
          { name: QueueName.IN_APP },
        ),
      ],
      providers: [QueueService],
      exports: [QueueService],
    };
  }
}
