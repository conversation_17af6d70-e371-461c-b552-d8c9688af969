# Queue System

This module provides a generic queue system using BullMQ for handling background jobs. It's designed to be used by all modules in the application for tasks that need to be processed asynchronously.

## Features

- Generic queue service for job management
- Support for multiple queue types (notification, email, push, general)
- Job processing with retry logic
- Job status tracking
- Queue metrics

## Queue Types

The system supports the following queue types:

- **Notification Queue**: For handling notification-related jobs
- **Email Queue**: For handling email-related jobs
- **Push Queue**: For handling push notification-related jobs
- **General Queue**: For handling other types of jobs

## Job Types

Each queue supports different job types:

### Notification Queue
- `SEND_SINGLE`: Send a notification to a single user
- `SEND_BULK`: Send notifications to multiple users
- `PROCESS_SCHEDULED`: Process scheduled notifications

### Email Queue
- `SEND_SINGLE`: Send an email to a single recipient
- `SEND_BULK`: Send emails to multiple recipients

### Push Queue
- `SEND_SINGLE`: Send a push notification to a single device
- `SEND_BULK`: Send push notifications to multiple devices

### General Queue
- `PROCESS_TASK`: Process a general task

## Usage

### 1. Import the Queue Module

```typescript
import { QueueModule } from '@app/shared/queue/queue.module';

@Module({
  imports: [QueueModule.register()],
  // ...
})
export class YourModule {}
```

### 2. Inject the Queue Service

```typescript
import { QueueService } from '@app/shared/queue/queue.service';

@Injectable()
export class YourService {
  constructor(private readonly queueService: QueueService) {}

  // ...
}
```

### 3. Add Jobs to the Queue

```typescript
// Add a single notification job
const jobId = await this.queueService.addSingleNotificationJob({
  userId: 'user-id',
  notificationTypeId: 'notification-type-id',
  data: { /* data */ },
  channels: ['push', 'email'],
});

// Add a bulk notification job
const jobId = await this.queueService.addBulkNotificationJob({
  notificationTypeId: 'notification-type-id',
  data: { /* data */ },
  targetAudience: {
    roles: ['student', 'student_admin'],
  },
  channels: ['push', 'email'],
});

// Add an email job
const jobId = await this.queueService.addSingleEmailJob({
  to: '<EMAIL>',
  subject: 'Email Subject',
  template: 'template-name',
  context: { /* template data */ },
});

// Add a push notification job
const jobId = await this.queueService.addSinglePushJob({
  token: 'device-token',
  title: 'Notification Title',
  body: 'Notification Body',
  data: { /* data */ },
});

// Add a general task job
const jobId = await this.queueService.addGeneralTaskJob({
  taskType: 'custom-task',
  payload: { /* task data */ },
});
```

### 4. Check Job Status

```typescript
const status = await this.queueService.getJobStatus(QueueName.NOTIFICATION, jobId);
```

### 5. Get Queue Metrics

```typescript
const metrics = await this.queueService.getQueueMetrics(QueueName.NOTIFICATION);
```

## Configuration

The queue system uses Redis for job storage and processing. Configure Redis in your `.env` file:

```
# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_USERNAME=
REDIS_DB=0
REDIS_TLS=false



# BullMQ configuration
QUEUE_CONCURRENCY=5
QUEUE_ATTEMPTS=3
QUEUE_BACKOFF_DELAY=1000
QUEUE_REMOVE_ON_COMPLETE=true
QUEUE_REMOVE_ON_FAIL=false
```

## Example Implementation

You can refer to the `EnhancedNotificationQueueService` and `EventNotificationService` for examples of how to use the queue system.

## Creating Custom Processors

To create a custom processor for a new job type:

1. Create a new processor class that extends `BaseProcessor`
2. Implement the `process` method to handle your job
3. Register the processor in your module

Example:

```typescript
@Injectable()
@Processor(QueueName.CUSTOM, {
  concurrency: DEFAULT_CONCURRENCY,
})
export class CustomProcessor extends BaseProcessor implements WorkerHost {
  constructor() {
    super('CustomProcessor');
  }

  async process(job: Job): Promise<JobResult> {
    // Process the job
    return {
      success: true,
      message: 'Job processed successfully',
    };
  }
}
```
