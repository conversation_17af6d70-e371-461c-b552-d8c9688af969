import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ExpressAdapter } from '@bull-board/express';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { QueueName } from './queue.constants';

@Module({
  imports: [
    BullBoardModule.forRoot({
      route: '/admin/queues',
      adapter: ExpressAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.NOTIFICATION,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.EMAIL,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.PUSH,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.GENERAL,
      adapter: <PERSON>MQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.IN_APP,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.UPLOAD,
      adapter: BullMQAdapter,
    }),
    BullModule.registerQueue(
      {
        name: QueueName.NOTIFICATION,
      },
      {
        name: QueueName.EMAIL,
      },
      {
        name: QueueName.PUSH,
      },
      {
        name: QueueName.GENERAL,
      },
      {
        name: QueueName.IN_APP,
      },
      {
        name: QueueName.UPLOAD,
      },
    ),
  ],
})
export class BullBoardUIModule {}
