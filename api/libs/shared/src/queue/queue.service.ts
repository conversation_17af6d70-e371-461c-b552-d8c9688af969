import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import {
  DEFAULT_JOB_OPTIONS,
  EmailJobType,
  GeneralJobType,
  InAppJobType,
  NotificationJobType,
  PushJobType,
  QueueName,
} from './queue.constants';
import {
  AcademicEmailReminderJobData,
  BulkEmailJobData,
  BulkInAppJobData,
  BulkNotificationJobData,
  BulkPushJobData,
  GeneralTaskJobData,
  ProcessScheduledNotificationsJobData,
  SingleEmailJobData,
  SingleInAppJobData,
  SingleNotificationJobData,
  SinglePushJobData,
  BulkDeleteUsersJobData,
} from './queue.types';

// Define interface for job status response
interface JobStatusResponse {
  id?: string;
  status: string;
  progress?: number;
  result?: any;
  failedReason?: string;
  timestamp?: number;
  processedOn?: number;
  finishedOn?: number;
}

// Define interface for queue metrics response
interface QueueMetricsResponse {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  total: number;
}

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue(QueueName.NOTIFICATION)
    private readonly notificationQueue: Queue<any, any, string>,
    @InjectQueue(QueueName.EMAIL)
    private readonly emailQueue: Queue<any, any, string>,
    @InjectQueue(QueueName.PUSH)
    private readonly pushQueue: Queue<any, any, string>,
    @InjectQueue(QueueName.GENERAL)
    private readonly generalQueue: Queue<any, any, string>,
    @InjectQueue(QueueName.IN_APP)
    private readonly inAppQueue: Queue<any, any, string>,
  ) {}

  /**
   * Add a single notification job to the queue
   * @param data The notification job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addSingleNotificationJob(
    data: SingleNotificationJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      // Create a job name that's more descriptive for the dashboard
      const jobName = `${NotificationJobType.SEND_SINGLE}:${data.userId.substring(0, 8)}`;

      const job = await this.notificationQueue.add(
        NotificationJobType.SEND_SINGLE,
        {
          ...data,

          _summary: `Notification to user ${data.userId.substring(0, 8)}... via ${data.channels.join(', ')}`,
          // Add job name in the data for better display in the dashboard
          _jobName: jobName,
        },
        {
          ...DEFAULT_JOB_OPTIONS,
          jobId,
        },
      );
      this.logger.debug(
        `Added single notification job ${job.id} for user ${data.userId}`,
      );
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add single notification job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a bulk notification job to the queue
   * @param data The bulk notification job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addBulkNotificationJob(
    data: BulkNotificationJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      // Create a descriptive job name
      const targetRoles = data.targetAudience.roles?.join(',') || 'all';
      const jobName = `${NotificationJobType.SEND_BULK}:${targetRoles}`;

      // Create a summary of the target audience
      let audienceSummary = '';
      if (data.targetAudience.roles?.length) {
        audienceSummary += `Roles: ${data.targetAudience.roles.join(', ')}`;
      }

      if (data.targetAudience.filters) {
        const filterKeys = Object.keys(data.targetAudience.filters);
        if (filterKeys.length) {
          audienceSummary += audienceSummary ? ', ' : '';
          audienceSummary += `Filters: ${filterKeys.join(', ')}`;
        }
      }

      // Ensure we're only sending to active users
      const enhancedData = {
        ...data,
        targetAudience: {
          ...data.targetAudience,
          filters: {
            ...data.targetAudience.filters,
            userState: 'active', // Always set userState to active
          },
        },
        // Add a summary field for better display in the dashboard
        _summary: `Bulk notification via ${data.channels.join(', ')} to ${audienceSummary || 'all users'} (active only)`,
        // Add job name in the data for better display in the dashboard
        _jobName: jobName,
      };

      const job = await this.notificationQueue.add(
        NotificationJobType.SEND_BULK,
        enhancedData,
        {
          ...DEFAULT_JOB_OPTIONS,
          jobId,
          attempts: 5, // More attempts for bulk jobs
        },
      );
      this.logger.debug(`Added bulk notification job ${job.id}`);
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add bulk notification job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a job to process scheduled notifications
   * @param data The job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addProcessScheduledNotificationsJob(
    data: ProcessScheduledNotificationsJobData = {},
    jobId?: string,
  ): Promise<string> {
    try {
      const job = await this.notificationQueue.add(
        NotificationJobType.PROCESS_SCHEDULED,
        data,
        {
          ...DEFAULT_JOB_OPTIONS,
          jobId,
          // BullMQ options
        },
      );
      this.logger.debug(`Added process scheduled notifications job ${job.id}`);
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add process scheduled notifications job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a single email job to the queue
   * @param data The email job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addSingleEmailJob(
    data: SingleEmailJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      // Check if a job with this ID already exists to prevent duplicates
      if (jobId) {
        const existingJobs = await this.emailQueue.getJobs([
          'active',
          'waiting',
          'delayed',
        ]);
        const jobExists = existingJobs.some((job) => job.id === jobId);

        if (jobExists) {
          this.logger.debug(
            `Email job ${jobId} already exists, skipping duplicate for ${data.to}`,
          );
          return jobId;
        }
      }

      const job = await this.emailQueue.add(EmailJobType.SEND_SINGLE, data, {
        ...DEFAULT_JOB_OPTIONS,
        jobId,
      });
      this.logger.debug(`Added single email job ${job.id} to ${data.to}`);
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add single email job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a bulk email job to the queue
   * @param data The bulk email job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addBulkEmailJob(
    data: BulkEmailJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      const job = await this.emailQueue.add(EmailJobType.SEND_BULK, data, {
        ...DEFAULT_JOB_OPTIONS,
        jobId,
        attempts: 5, // More attempts for bulk jobs
        // BullMQ options
      });
      this.logger.debug(
        `Added bulk email job ${job.id} for ${data.emails.length} recipients`,
      );
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add bulk email job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a single push notification job to the queue
   * @param data The push notification job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addSinglePushJob(
    data: SinglePushJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      const job = await this.pushQueue.add(PushJobType.SEND_SINGLE, data, {
        ...DEFAULT_JOB_OPTIONS,
        jobId,
      });
      this.logger.debug(`Added single push job ${job.id}`);
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add single push job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a bulk push notification job to the queue
   * @param data The bulk push notification job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addBulkPushJob(data: BulkPushJobData, jobId?: string): Promise<string> {
    try {
      const job = await this.pushQueue.add(PushJobType.SEND_BULK, data, {
        ...DEFAULT_JOB_OPTIONS,
        jobId,
        attempts: 5, // More attempts for bulk jobs
        // BullMQ options
      });
      this.logger.debug(
        `Added bulk push job ${job.id} for ${data.tokens.length} devices`,
      );
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add bulk push job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a general task job to the queue
   * @param data The general task job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addGeneralTaskJob(
    data: GeneralTaskJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      const job = await this.generalQueue.add(
        GeneralJobType.PROCESS_TASK,
        data,
        {
          ...DEFAULT_JOB_OPTIONS,
          jobId,
        },
      );
      this.logger.debug(
        `Added general task job ${job.id} of type ${data.taskType}`,
      );
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add general task job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add an academic email reminder job to the queue
   * @param data The academic email reminder job data
   * @param jobId Optional job ID
   * @param delay Optional delay in milliseconds
   * @returns The job ID
   */
  async addAcademicEmailReminderJob(
    data: AcademicEmailReminderJobData,
    jobId?: string,
    delay?: number,
  ): Promise<string> {
    try {
      const job = await this.generalQueue.add(
        GeneralJobType.ACADEMIC_EMAIL_REMINDER,
        data,
        {
          ...DEFAULT_JOB_OPTIONS,
          jobId,
          delay,
        },
      );
      this.logger.debug(
        `Added academic email reminder job ${job.id} with rejection days: ${data.rejectionDays}`,
      );
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add academic email reminder job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a bulk user deletion job to the queue
   * @param data The bulk deletion job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addBulkDeleteUsersJob(
    data: BulkDeleteUsersJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      const job = await this.generalQueue.add(
        GeneralJobType.BULK_DELETE_USERS,
        data,
        {
          ...DEFAULT_JOB_OPTIONS,
          jobId,
          attempts: 3, // Limited attempts for deletion jobs
          removeOnComplete: true, // Remove completed jobs to save space
          removeOnFail: false, // Keep failed jobs for debugging
        },
      );
      this.logger.log(
        `Added bulk user deletion job ${job.id} for ${data.userIds.length} users`,
      );
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add bulk user deletion job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Get job status by ID and queue
   * @param queueName The queue name
   * @param jobId The job ID
   * @returns The job status
   */
  async getJobStatus(
    queueName: QueueName,
    jobId: string,
  ): Promise<JobStatusResponse> {
    try {
      let queue: Queue<any, any, string>;
      switch (queueName) {
        case QueueName.NOTIFICATION:
          queue = this.notificationQueue;
          break;
        case QueueName.EMAIL:
          queue = this.emailQueue;
          break;
        case QueueName.PUSH:
          queue = this.pushQueue;
          break;
        case QueueName.GENERAL:
          queue = this.generalQueue;
          break;
        case QueueName.IN_APP:
          queue = this.inAppQueue;
          break;
        default:
          throw new Error(`Unknown queue: ${queueName}`);
      }

      const job = await queue.getJob(jobId);
      if (!job) {
        return { status: 'not_found' };
      }

      const state = await job.getState();
      const progress = job.progress;
      const result = job.returnvalue;
      const failedReason = job.failedReason;

      return {
        id: job.id,
        status: state,
        progress,
        result,
        failedReason,
        timestamp: job.timestamp,
        processedOn: job.processedOn,
        finishedOn: job.finishedOn,
      };
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to get job status for ${jobId}: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Get queue metrics
   * @param queueName The queue name
   * @returns The queue metrics
   */
  /**
   * Add a single in-app notification job to the queue
   * @param data The in-app notification job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addSingleInAppJob(
    data: SingleInAppJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      const job = await this.inAppQueue.add(InAppJobType.SEND_SINGLE, data, {
        ...DEFAULT_JOB_OPTIONS,
        jobId,
      });
      this.logger.debug(
        `Added single in-app job ${job.id} for user ${data.userId}`,
      );
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add single in-app job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  /**
   * Add a bulk in-app notification job to the queue
   * @param data The bulk in-app notification job data
   * @param jobId Optional job ID
   * @returns The job ID
   */
  async addBulkInAppJob(
    data: BulkInAppJobData,
    jobId?: string,
  ): Promise<string> {
    try {
      const job = await this.inAppQueue.add(InAppJobType.SEND_BULK, data, {
        ...DEFAULT_JOB_OPTIONS,
        jobId,
        attempts: 5, // More attempts for bulk jobs
      });
      this.logger.debug(
        `Added bulk in-app job ${job.id} for ${data.userIds.length} users`,
      );
      return job.id as string;
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to add bulk in-app job: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }

  async getQueueMetrics(queueName: QueueName): Promise<QueueMetricsResponse> {
    try {
      let queue: Queue<any, any, string>;
      switch (queueName) {
        case QueueName.NOTIFICATION:
          queue = this.notificationQueue;
          break;
        case QueueName.EMAIL:
          queue = this.emailQueue;
          break;
        case QueueName.PUSH:
          queue = this.pushQueue;
          break;
        case QueueName.GENERAL:
          queue = this.generalQueue;
          break;
        case QueueName.IN_APP:
          queue = this.inAppQueue;
          break;
        default:
          throw new Error(`Unknown queue: ${queueName}`);
      }

      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaitingCount(),
        queue.getActiveCount(),
        queue.getCompletedCount(),
        queue.getFailedCount(),
        queue.getDelayedCount(),
      ]);

      return {
        waiting,
        active,
        completed,
        failed,
        delayed,
        total: waiting + active + completed + failed + delayed,
      };
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(
        `Failed to get queue metrics for ${queueName}: ${err.message}`,
        err.stack,
      );
      throw error;
    }
  }
}
