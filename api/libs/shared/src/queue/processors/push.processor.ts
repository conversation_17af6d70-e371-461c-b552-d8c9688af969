import { Injectable } from '@nestjs/common';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { NotificationService } from '../../notification/notification.service';
import { BaseProcessor } from './base.processor';
import type {
  BulkPushJobData,
  JobResult,
  SinglePushJobData,
} from '../queue.types';
import {
  DEFAULT_CONCURRENCY,
  PushJobType,
  QueueName,
} from '../queue.constants';

@Injectable()
@Processor(QueueName.PUSH, {
  concurrency: DEFAULT_CONCURRENCY,
})
export class PushProcessor extends BaseProcessor {
  constructor(private readonly notificationService: NotificationService) {
    super('PushProcessor');
  }

  /**
   * Process push notification jobs
   * @param job The job to process
   */
  async process(job: Job): Promise<JobResult> {
    this.logger.debug(`Processing push job ${job.id} of type ${job.name}`);

    try {
      switch (job.name) {
        case PushJobType.SEND_SINGLE:
          return await this.processSinglePush(job.data as SinglePushJobData);
        case PushJobType.SEND_BULK:
          return await this.processBulkPush(job.data as BulkPushJobData);
        default:
          throw new Error(`Unknown job type: ${job.name}`);
      }
    } catch (error: any) {
      this.logger.error(
        `Error processing push job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process a single push notification job
   * @param data The job data
   */
  private async processSinglePush(data: SinglePushJobData): Promise<JobResult> {
    try {
      this.logger.debug(
        `Sending push notification to token ${data.token.substring(0, 10)}...`,
      );

      const result = await this.notificationService.sendNotification({
        token: data.token,
        notification: {
          title: data.title,
          body: data.body,
        },
        data: data.data,
      });

      this.logger.debug(
        `Push notification sent successfully to token ${data.token.substring(0, 10)}...`,
      );

      return {
        success: true,
        message: `Push notification sent successfully`,
        data: {
          ...result,
          // Add a summary for better display in the dashboard
          summary: `Title: ${data.title}, Body: ${data.body.substring(0, 30)}${data.body.length > 30 ? '...' : ''}`,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to send push notification: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: `Failed to send push notification`,
        error: error.message,
        // Add more context to the error for better debugging
        data: {
          token: data.token.substring(0, 10) + '...',
          title: data.title,
          bodyPreview: data.body.substring(0, 30) + '...',
        },
      };
    }
  }

  /**
   * Process a bulk push notification job
   * @param data The job data
   */
  private async processBulkPush(data: BulkPushJobData): Promise<JobResult> {
    try {
      // Process tokens in batches if specified
      const batchSize = data.batchSize || 500;
      const batches = [];

      for (let i = 0; i < data.tokens.length; i += batchSize) {
        batches.push(data.tokens.slice(i, i + batchSize));
      }

      // Process each batch
      for (const {} of batches) {
        // Use the correct method from NotificationService
        await this.notificationService.sendTopicNotification({
          topic: 'all',
          title: data.title,
          body: data.body,
          icon: data.data?.icon,
        });
      }

      return {
        success: true,
        message: `${data.tokens.length} push notifications sent successfully`,
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Failed to send bulk push notifications',
        error: error.message,
      };
    }
  }
}
