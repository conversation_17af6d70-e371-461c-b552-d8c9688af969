import { Injectable } from '@nestjs/common';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { BaseProcessor } from './base.processor';
import type { JobResult, UploadJobData } from '../queue.types';
import {
  DEFAULT_CONCURRENCY,
  QueueName,
  UploadJobType,
} from '../queue.constants';
import { UploadService } from '@/upload/upload.service';
import { UploadQueueService } from '@app/shared/upload/upload.service';

@Injectable()
@Processor(QueueName.UPLOAD, {
  concurrency: DEFAULT_CONCURRENCY,
})
export class UploadProcessor extends BaseProcessor {
  constructor(
    private readonly uploadService: UploadService,
    private readonly uploadQueueService: UploadQueueService,
  ) {
    super('UploadProcessor');
  }

  /**
   * Process upload jobs
   * @param job The job to process
   */
  async process(job: Job): Promise<JobResult> {
    this.logger.debug(
      `Processing UploadProcessor job ${job.id} of type ${job.name}`,
    );

    try {
      switch (job.name) {
        case UploadJobType.UPLOAD_FILE:
          return await this.processTask(job.data as UploadJobData);
        default:
          throw new Error(`Unknown job type: ${job.name}`);
      }
    } catch (error: any) {
      this.logger.error(
        `Error processing general job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   *  Process the upload task
   * @param data The data for the upload job
   */
  private async processTask(data: UploadJobData): Promise<JobResult> {
    try {
      this.logger.debug(
        `UploadProcessor image for post ${data.postId} with file ${data.file.originalname}`,
      );
      const { imageUrl } = await this.uploadService.uploadFileToS3(data.file);

      await this.uploadQueueService.updatePostImages({
        imageUrl: imageUrl!,
        postId: data.postId,
        isLastImage: data.isLastImage,
        status: data.status,
      });

      return {
        success: true,
        message: `Successfully uploaded file ${data.file.originalname} for post ${data.postId}`,
        data: { processed: true },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to upload file ${data.file.originalname} for post ${data.postId}`,
        error: error.message,
      };
    }
  }
}
