import { Injectable } from '@nestjs/common';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { EmailService } from '@/mail/email.service';
import { BaseProcessor } from './base.processor';
import type {
  BulkEmailJobData,
  JobResult,
  SingleEmailJobData,
} from '../queue.types';
import {
  DEFAULT_CONCURRENCY,
  EmailJobType,
  QueueName,
} from '../queue.constants';

@Injectable()
@Processor(QueueName.EMAIL, {
  concurrency: DEFAULT_CONCURRENCY,
})
export class EmailProcessor extends BaseProcessor {
  constructor(private readonly emailService: EmailService) {
    super('EmailProcessor');
  }

  /**
   * Process email jobs
   * @param job The job to process
   */
  async process(job: Job): Promise<JobResult> {
    // Only log detailed information in development mode
    if (process.env.NODE_ENV === 'development') {
      this.logger.log(`Processing email job ${job.id} of type ${job.name}`);
    }

    try {
      // Always force EMAIL_TOGGLE to ON for testing
      if (process.env.EMAIL_TOGGLE !== 'ON') {
        this.logger.warn(
          `Email notifications are disabled (EMAIL_TOGGLE=${process.env.EMAIL_TOGGLE}). Forcing EMAIL_TOGGLE to ON for testing.`,
        );
        process.env.EMAIL_TOGGLE = 'ON';
      }

      // Only log detailed job info in development mode
      if (process.env.NODE_ENV === 'development') {
        this.logger.log(
          `Email job details: Job ID: ${job.id}, Job Type: ${job.name}`,
        );
      }

      switch (job.name) {
        case EmailJobType.SEND_SINGLE:
          // Only log in development mode
          if (process.env.NODE_ENV === 'development') {
            this.logger.log(
              `Processing single email job ${job.id} to ${job.data.to}`,
            );
          }
          return await this.processSingleEmail(job.data as SingleEmailJobData);
        case EmailJobType.SEND_BULK:
          // Always log bulk emails since they're less frequent and more important
          this.logger.log(
            `Processing bulk email job ${job.id} with ${job.data.emails.length} recipients`,
          );
          return await this.processBulkEmail(job.data as BulkEmailJobData);
        default:
          throw new Error(`Unknown job type: ${job.name}`);
      }
    } catch (error: any) {
      this.logger.error(
        `Error processing email job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process a single email job
   * @param data The job data
   */
  private async processSingleEmail(
    data: SingleEmailJobData,
  ): Promise<JobResult> {
    try {
      // Reduced logging for email jobs to minimize noise
      if (process.env.NODE_ENV === 'development') {
        this.logger.log(
          `Sending email to ${data.to} with subject "${data.subject}" using template "${data.template}"`,
        );

        // Log context keys only (not values) to avoid exposing sensitive data
        if (data.context && typeof data.context === 'object') {
          this.logger.log(
            `Email context keys: ${Object.keys(data.context).join(', ')}`,
          );
        }
      }

      // Use the correct method from EmailService with critical=true to ensure delivery
      await this.emailService.sendCustomEmail({
        email: data.to,
        subject: data.subject,
        template: data.template,
        context: data.context,
        critical: true,
      });

      // Only log success in development mode
      if (process.env.NODE_ENV === 'development') {
        this.logger.log(`Successfully sent email to ${data.to}`);
      }

      return {
        success: true,
        message: `Email sent to ${data.to}`,
        data: {
          to: data.to,
          subject: data.subject,
          template: data.template,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to send email to ${data.to}: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: `Failed to send email to ${data.to}`,
        error: error.message,
        data: {
          to: data.to,
          subject: data.subject,
          template: data.template,
        },
      };
    }
  }

  /**
   * Process a bulk email job
   * @param data The job data
   */
  private async processBulkEmail(data: BulkEmailJobData): Promise<JobResult> {
    try {
      // Process emails in batches if specified

      const batchSize = data.batchSize || 100;
      const batches = [];
      let successCount = 0;
      let failureCount = 0;
      const failures: Array<{ to: string; error: string | undefined }> = [];

      for (let i = 0; i < data.emails.length; i += batchSize) {
        batches.push(data.emails.slice(i, i + batchSize));
      }

      for (const batch of batches) {
        const emailResults = (await Promise.allSettled(
          batch.map(({ to, subject, template, context }) =>
            this.emailService
              .sendCustomEmail({
                email: to,
                subject,
                template,
                context,
              })
              .then(() => ({ success: true, to }))
              .catch((error) => ({
                success: false,
                to,
                error: error.message,
              })),
          ),
        )) as PromiseSettledResult<{
          success: boolean;
          to: string;
          error?: string;
        }>[];

        // Count successes and failures
        emailResults.forEach((result) => {
          if (result.status === 'fulfilled') {
            if (result.value.success) {
              successCount++;
            } else {
              failureCount++;
              failures.push({
                to: result.value.to,
                error: result.value.error,
              });
              this.logger.error(
                `Failed to send email to ${result.value.to}${result.value.error ? ': ' + result.value.error : ''}`,
              );
            }
          } else {
            failureCount++;
            this.logger.error(`Email promise rejected: ${result.reason}`);
          }
        });

        // Add a small delay between batches to prevent rate limiting
        if (batches.length > 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      return {
        success: failureCount === 0,
        message: `${successCount}/${data.emails.length} emails sent successfully (${failureCount} failed)`,
        data: {
          successCount,
          failureCount,
          totalEmails: data.emails.length,
          failures: failures.length > 10 ? failures.slice(0, 10) : failures,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to process bulk email job: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: 'Failed to send bulk emails',
        error: error.message,
        data: {
          totalEmails: data.emails.length,
        },
      };
    }
  }
}
