import { Injectable } from '@nestjs/common';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { BaseProcessor } from './base.processor';
import type {
  BulkInAppJobData,
  JobResult,
  SingleInAppJobData,
} from '../queue.types';
import {
  DEFAULT_CONCURRENCY,
  InAppJobType,
  QueueName,
} from '../queue.constants';
import { EnhancedNotificationService } from '../../enhanced-notification/enhanced-notification.service';

@Injectable()
@Processor(QueueName.IN_APP, {
  concurrency: DEFAULT_CONCURRENCY,
})
export class InAppProcessor extends BaseProcessor {
  constructor(
    private readonly notificationService: EnhancedNotificationService,
  ) {
    super('InAppProcessor');
  }

  /**
   * Process in-app notification jobs
   * @param job The job to process
   */
  async process(job: Job): Promise<JobResult> {
    this.logger.debug(`Processing in-app job ${job.id} of type ${job.name}`);

    try {
      switch (job.name) {
        case InAppJobType.SEND_SINGLE:
          return await this.processSingleInApp(job.data as SingleInAppJobData);
        case InAppJobType.SEND_BULK:
          return await this.processBulkInApp(job.data as BulkInAppJobData);
        default:
          throw new Error(`Unknown job type: ${job.name}`);
      }
    } catch (error: any) {
      this.logger.error(
        `Error processing in-app job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process a single in-app notification job
   * @param data The job data
   */
  private async processSingleInApp(
    data: SingleInAppJobData,
  ): Promise<JobResult> {
    try {
      this.logger.debug(
        `Processing in-app notification for user ${data.userId}`,
      );

      // Get notification subject for this event type
      const subject = this.notificationService.getInAppNotificationSubject(
        data.module,
      );
      if (!subject) {
        this.logger.warn(
          `No notification subject found for event type ${data.module}`,
        );
        return {
          success: false,
          message: `No notification subject found for event type ${data.module}`,
        };
      }

      // Send notification to the subject
      subject.next({
        userId: data.userId,
        title: data.title,
        body: data.body,
        data: data.data || {},
        timestamp: new Date(),
      });

      this.logger.debug(
        `Successfully sent in-app notification to user ${data.userId}`,
      );

      return {
        success: true,
        message: `In-app notification sent to user ${data.userId}`,
        data: {
          summary: `User: ${data.userId.substring(0, 8)}..., Module: ${data.module}`,
          title: data.title,
          body: data.body,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to send in-app notification to user ${data.userId}: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: `Failed to send in-app notification to user ${data.userId}`,
        error: error.message,
        data: {
          userId: data.userId,
          module: data.module,
        },
      };
    }
  }

  /**
   * Process a bulk in-app notification job
   * @param data The job data
   */
  private async processBulkInApp(data: BulkInAppJobData): Promise<JobResult> {
    try {
      this.logger.debug(
        `Processing bulk in-app notification for ${data.userIds.length} users`,
      );

      // Get notification subject for this event type
      const subject = this.notificationService.getInAppNotificationSubject(
        data.module,
      );
      if (!subject) {
        this.logger.warn(
          `No notification subject found for event type ${data.module}`,
        );
        return {
          success: false,
          message: `No notification subject found for event type ${data.module}`,
        };
      }

      // Send notification to each user
      let successCount = 0;
      let failureCount = 0;

      for (const userId of data.userIds) {
        try {
          // Send notification to the subject
          subject.next({
            userId,
            title: data.title,
            body: data.body,
            data: data.data || {},
            timestamp: new Date(),
          });
          successCount++;
        } catch (error: any) {
          this.logger.error(
            `Failed to send in-app notification to user ${userId}: ${error.message}`,
            error.stack,
          );
          failureCount++;
        }
      }

      this.logger.debug(
        `Successfully sent bulk in-app notifications to ${successCount}/${data.userIds.length} users (${failureCount} failed)`,
      );

      return {
        success: true,
        message: `Bulk in-app notifications sent to ${successCount}/${data.userIds.length} users (${failureCount} failed)`,
        data: {
          summary: `Users: ${data.userIds.length}, Module: ${data.module}, Success: ${successCount}, Failure: ${failureCount}`,
          title: data.title,
          body: data.body,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to send bulk in-app notifications: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: 'Failed to send bulk in-app notifications',
        error: error.message,
        data: {
          userCount: data.userIds.length,
          module: data.module,
        },
      };
    }
  }
}
