import { Logger } from '@nestjs/common';
import { OnWorkerEvent, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import type { JobResult } from '../queue.types';

/**
 * Base processor class that all job processors should extend
 */
export abstract class BaseProcessor extends WorkerHost {
  protected readonly logger: Logger;

  constructor(processorName: string) {
    super();
    this.logger = new Logger(processorName);
  }

  /**
   * Process a job
   * @param job The job to process
   */
  abstract override process(job: Job): Promise<JobResult>;

  /**
   * Handle job completion event
   * @param job The completed job
   * @param result The job result
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: JobResult) {
    // Skip logging for email jobs to reduce noise
    if (!job.id?.toString().startsWith('email-')) {
      this.logger.debug(
        `Job ${job.id} completed with result: ${JSON.stringify(result)}`,
      );
    }
  }

  /**
   * Handle job failure event
   * @param job The failed job
   * @param error The error that caused the failure
   */
  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(
      `Job ${job.id} failed with error: ${error.message}`,
      error.stack,
    );
  }

  /**
   * Handle job progress event
   * @param job The job in progress
   * @param progress The progress data
   */
  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: number | object) {
    // Skip logging for email jobs to reduce noise
    if (!job.id?.toString().startsWith('email-')) {
      this.logger.debug(
        `Job ${job.id} reported progress: ${JSON.stringify(progress)}`,
      );
    }
  }

  /**
   * Handle worker error event with enhanced Redis error handling
   * @param error The error that occurred
   */
  @OnWorkerEvent('error')
  onError(error: Error) {
    // Check if this is a Redis connection error
    const isRedisError = this.isRedisConnectionError(error);

    if (isRedisError) {
      // Log Redis errors at warn level to reduce noise
      this.logger.warn(`Redis connection error in worker: ${error.message}`);

      // In development, provide more context
      if (process.env.NODE_ENV === 'development') {
        this.logger.debug('Redis error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack,
        });
      }
    } else {
      this.logger.error(`Worker error: ${error.message}`, error.stack);
    }
  }

  /**
   * Check if an error is related to Redis connection issues
   * @param error The error to check
   * @returns True if the error is Redis-related
   */
  private isRedisConnectionError(error: Error): boolean {
    const redisErrorMessages = [
      'Command timed out',
      'Connection is closed',
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ENOTFOUND',
      'ENETUNREACH',
      'Redis connection',
      'READONLY',
    ];

    return redisErrorMessages.some(
      (msg) => error.message.includes(msg) || error.name.includes(msg),
    );
  }

  /**
   * Handle stalled job event
   * @param jobId The ID of the stalled job
   */
  @OnWorkerEvent('stalled')
  onStalled(jobId: string) {
    this.logger.warn(
      `Job ${jobId} stalled - likely due to Redis connection issues`,
    );
  }

  /**
   * Handle worker ready event
   */
  @OnWorkerEvent('ready')
  onReady() {
    this.logger.log(
      `Worker ${this.constructor.name} is ready and connected to Redis`,
    );
  }

  /**
   * Handle worker closing event
   */
  @OnWorkerEvent('closing')
  onClosing() {
    this.logger.log(`Worker ${this.constructor.name} is closing`);
  }
}
