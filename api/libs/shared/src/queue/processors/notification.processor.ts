import { Injectable } from '@nestjs/common';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { EnhancedNotificationService } from '../../enhanced-notification/enhanced-notification.service';
import { BaseProcessor } from './base.processor';
import type {
  BulkNotificationJobData,
  JobResult,
  SingleNotificationJobData,
} from '../queue.types';
import {
  DEFAULT_CONCURRENCY,
  NotificationJobType,
  QueueName,
} from '../queue.constants';

@Injectable()
@Processor(QueueName.NOTIFICATION, {
  concurrency: DEFAULT_CONCURRENCY,
})
export class NotificationProcessor extends BaseProcessor {
  constructor(
    private readonly notificationService: EnhancedNotificationService,
  ) {
    super('NotificationProcessor');
  }

  /**
   * Process notification jobs
   * @param job The job to process
   */
  async process(job: Job): Promise<JobResult> {
    try {
      switch (job.name) {
        case NotificationJobType.SEND_SINGLE:
          return await this.processSingleNotification(
            job.data as SingleNotificationJobData,
          );
        case NotificationJobType.SEND_BULK:
          return await this.processBulkNotification(
            job.data as BulkNotificationJobData,
          );
        case NotificationJobType.PROCESS_SCHEDULED:
          return await this.processScheduledNotifications();
        default:
          throw new Error(`Unknown job type: ${job.name}`);
      }
    } catch (error: any) {
      this.logger.error(
        `Error processing notification job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process a single notification job
   * @param data The job data
   */
  private async processSingleNotification(
    data: SingleNotificationJobData,
  ): Promise<JobResult> {
    try {
      // Get notification type details
      let notificationTypeDetails = '';
      try {
        const notificationType =
          await this.notificationService.getNotificationType(
            data.notificationTypeId,
          );
        if (notificationType) {
          notificationTypeDetails = ` (${notificationType.name})`;
        }
      } catch (e) {}

      // Send notification to user
      await this.notificationService.sendNotificationToUser({
        userId: data.userId,
        notificationTypeId: data.notificationTypeId,
        data: {
          ...data.data,
          title: data.title,
          body: data.body,
        },
        channels: data.channels as any,
        overridePreferences: data.overridePreferences,
      });

      return {
        success: true,
        message: `Notification sent to user ${data.userId}${notificationTypeDetails}`,
        data: {
          // Include summary information for better display in the dashboard
          summary: `User: ${data.userId.substring(0, 8)}..., Type: ${data.notificationTypeId.substring(0, 8)}..., Channels: ${data.channels.join(', ')}`,
          title: data.title,
          body: data.body,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to send notification to user ghana ${data.userId}: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: `Failed to send notification to user ${data.userId}`,
        error: error.message,
        // Include context information for better debugging
        data: {
          userId: data.userId,
          notificationTypeId: data.notificationTypeId,
          channels: data.channels,
        },
      };
    }
  }

  /**
   * Process a bulk notification job
   * @param data The job data
   */
  private async processBulkNotification(
    data: BulkNotificationJobData,
  ): Promise<JobResult> {
    try {
      // Check for specific userId in filters to prevent infinite loops
      if (data.targetAudience.filters?.userId) {
        const userId = data.targetAudience.filters.userId;

        // Process as a direct notification instead of querying all users
        try {
          await this.notificationService.sendDirectNotification({
            userId,
            notificationTypeId: data.notificationTypeId,
            data: data.data,
            channels: data.channels,
          });

          return {
            success: true,
            message: `Notification sent directly to user ${userId}`,
            data: {
              summary: `User: ${userId.substring(0, 8)}..., Type: ${data.notificationTypeId.substring(0, 8)}..., Channels: ${data.channels.join(', ')}`,
            },
          };
        } catch (directError: any) {
          this.logger.error(
            `Failed to send direct notification to user ${userId}: ${directError.message}`,
            directError.stack,
          );
          throw directError;
        }
      }

      // Create a summary of the target audience
      let audienceSummary = '';
      if (data.targetAudience.roles?.length) {
        audienceSummary += `Roles: ${data.targetAudience.roles.join(', ')}`;
      }

      if (data.targetAudience.filters) {
        const filterKeys = Object.keys(data.targetAudience.filters);
        if (filterKeys.length) {
          audienceSummary += audienceSummary ? ', ' : '';
          audienceSummary += `Filters: ${filterKeys.join(', ')}`;
        }
      }

      // Get notification type details for better logging
      let notificationType;
      let notificationTypeDetails = '';
      try {
        notificationType = await this.notificationService.getNotificationType(
          data.notificationTypeId,
        );
        if (notificationType) {
          notificationTypeDetails = ` (${notificationType.name})`;

          // For opportunity notifications, always ensure we're only sending to active users
          if (
            notificationType.code === 'new_opportunity' ||
            notificationType.module === 'opportunity'
          ) {
            if (!data.targetAudience.filters) {
              data.targetAudience.filters = {};
            }
            data.targetAudience.filters.userState = 'active';
          }
        }
      } catch (e) {
        // Ignore errors when fetching notification type details
        this.logger.warn(`Failed to get notification type details: ${e}`);
      }

      // Get users with the specified roles and state
      // Always filter for active users
      // Optimized query for large user sets (30,000+ users)
      const userQuery = `
        SELECT id, email, role, state
        FROM users
        WHERE deleted = false
        AND state = 'active'
        ${
          data.targetAudience.roles?.length
            ? `AND role IN (${data.targetAudience.roles.map((r) => `'${r}'`).join(',')})`
            : ''
        }
        ${
          data.targetAudience.filters?.clubId
            ? `AND id IN (SELECT user_id FROM student_profiles WHERE club_id = '${data.targetAudience.filters.clubId}')`
            : ''
        }
        ${
          data.targetAudience.filters?.institutionIds?.length
            ? `AND id IN (SELECT user_id FROM student_profiles WHERE institution_id IN (${data.targetAudience.filters.institutionIds.map((id: string) => `'${id}'`).join(',')}))`
            : ''
        }
        ${
          data.targetAudience.filters?.countryIds?.length
            ? `AND id IN (SELECT user_id FROM student_profiles WHERE country_id IN (${data.targetAudience.filters.countryIds.map((id: string) => `'${id}'`).join(',')}))`
            : ''
        }
        ${
          // Only apply post engagement filter if we're not filtering by specific roles
          data.targetAudience.filters?.postId &&
          (!data.targetAudience.roles || data.targetAudience.roles.length === 0)
            ? `AND id IN (SELECT student_profiles.user_id FROM student_profiles JOIN "post_engagements" ON student_profiles.id = "post_engagements"."student_profile_id" WHERE "post_engagements"."postId" = '${data.targetAudience.filters.postId}')`
            : ''
        }
        ORDER BY id
      `;

      // Execute the user query

      // Execute the query using the drizzle service
      const result = await this.notificationService.executeQuery(userQuery);
      let targetUsers = result.rows || [];

      // Check if we found any users

      // If no users found, try a more general query without the postId filter
      // But only if we're not filtering by specific roles
      if (
        targetUsers.length === 0 &&
        data.targetAudience.filters?.postId &&
        (!data.targetAudience.roles || data.targetAudience.roles.length === 0)
      ) {
        // Create a copy of the filters without the postId
        const fallbackFilters = { ...data.targetAudience.filters };
        delete fallbackFilters.postId;

        // Remove the postId filter and try again
        const fallbackQuery = `
          SELECT id, email, role, state
          FROM users
          WHERE deleted = false
          AND state = 'active'
          ${
            // If a specific userId is provided, use that as the primary filter
            fallbackFilters.userId ? `AND id = '${fallbackFilters.userId}'` : ''
          }
          ${
            // Only apply role filter if no specific userId is provided
            !fallbackFilters.userId && data.targetAudience.roles?.length
              ? `AND role IN (${data.targetAudience.roles.map((r) => `'${r}'`).join(',')})`
              : ''
          }
          ${
            fallbackFilters.clubId
              ? `AND id IN (SELECT user_id FROM student_profiles WHERE club_id = '${fallbackFilters.clubId}')`
              : ''
          }
          ${
            fallbackFilters.institutionIds?.length
              ? `AND id IN (SELECT user_id FROM student_profiles WHERE institution_id IN (${fallbackFilters.institutionIds.map((id: string) => `'${id}'`).join(',')}))`
              : ''
          }
          ${
            fallbackFilters.countryIds?.length
              ? `AND id IN (SELECT user_id FROM student_profiles WHERE country_id IN (${fallbackFilters.countryIds.map((id: string) => `'${id}'`).join(',')}))`
              : ''
          }
          ORDER BY id

        `;

        // Execute the fallback query
        const fallbackResult =
          await this.notificationService.executeQuery(fallbackQuery);
        targetUsers = fallbackResult.rows || [];
      }

      // If still no users found, try a final fallback with just the role filter
      // This is our last resort when we have specific roles but no users were found
      if (targetUsers.length === 0 && data.targetAudience.roles?.length) {
        // Final fallback query with just role filter
        const finalFallbackQuery = `
          SELECT id, email, role, state
          FROM users
          WHERE deleted = false
          AND state = 'active'
          ${
            data.targetAudience.roles?.length
              ? `AND role IN (${data.targetAudience.roles.map((r) => `'${r}'`).join(',')})`
              : ''
          }
          ORDER BY id

        `;

        // Execute the final fallback query
        const finalFallbackResult =
          await this.notificationService.executeQuery(finalFallbackQuery);
        targetUsers = finalFallbackResult.rows || [];
      }

      // If still no users found, return
      if (targetUsers.length === 0) {
        this.logger.warn('No users found matching the target audience');
        return {
          success: true,
          message: 'No users found matching the target audience',
          data: {
            summary: `Target: ${audienceSummary || 'all users'}, Type: ${data.notificationTypeId.substring(0, 8)}..., Channels: ${data.channels.join(', ')}`,
          },
        };
      }

      // Log the first few users for debugging
      if (targetUsers.length > 0) {
        const sampleUsers = targetUsers.slice(
          0,
          Math.min(5, targetUsers.length),
        );
        this.logger.log(
          `Sample users: ${sampleUsers.map((u: any) => `${u.id} (${u.role})`).join(', ')}`,
        );
      }

      // Check if email channel is included and EMAIL_TOGGLE is ON
      if (
        data.channels.includes('email') &&
        process.env.EMAIL_TOGGLE !== 'ON'
      ) {
        // Force EMAIL_TOGGLE to ON for testing
        process.env.EMAIL_TOGGLE = 'ON';
      }

      // Send notifications to users in batches for better throughput
      let successCount = 0;
      let failureCount = 0;

      // Process in batches of 50 users at a time for better performance with 30,000+ users
      const BATCH_SIZE = 50;

      for (let i = 0; i < targetUsers.length; i += BATCH_SIZE) {
        const batch = targetUsers.slice(i, i + BATCH_SIZE);

        // Process batch of users

        // Add timestamp to ensure uniqueness for each batch
        const batchData = {
          ...data.data,
          batchTimestamp: new Date().toISOString(),
        };

        // Use Promise.allSettled to process batch in parallel while handling errors
        const results = await Promise.allSettled(
          batch.map((user: { id: string; email: string }) => {
            return this.notificationService.sendDirectNotification({
              userId: user.id,
              notificationTypeId: data.notificationTypeId,
              data: batchData,
              channels: data.channels,
              // Don't pass jobId to ensure a new job is created each time
            });
          }),
        );

        // Count successes and failures
        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            successCount++;
          } else {
            failureCount++;
            this.logger.error(
              `Failed to send notification to user ${batch[index].id}: ${result.reason}`,
              result.reason?.stack,
            );
          }
        });
      }

      return {
        success: true,
        message: `Bulk notifications sent successfully to ${successCount}/${targetUsers.length} users (${failureCount} failed) ${notificationTypeDetails}`,
        data: {
          // Include summary information for better display in the dashboard
          summary: `Target: ${audienceSummary || 'all users'}, Type: ${data.notificationTypeId.substring(0, 8)}..., Channels: ${data.channels.join(', ')}`,
          successCount,
          failureCount,
          totalUsers: targetUsers.length,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to send bulk notifications: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: 'Failed to send bulk notifications',
        error: error.message,
        // Include context information for better debugging
        data: {
          notificationTypeId: data.notificationTypeId,
          targetAudience: data.targetAudience,
          channels: data.channels,
        },
      };
    }
  }

  /**
   * Process scheduled notifications
   * @param data The job data
   */
  private async processScheduledNotifications(): Promise<JobResult> {
    try {
      await this.notificationService.processScheduledNotifications();

      return {
        success: true,
        message: 'Scheduled notifications processed successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Failed to process scheduled notifications',
        error: error.message,
      };
    }
  }
}
