import { Injectable } from '@nestjs/common';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import type {
  AcademicEmailReminderJobData,
  GeneralTaskJobData,
  JobResult,
  BulkDeleteUsersJobData,
} from '../queue.types';
import { GeneralJobType, QueueName } from '../queue.constants';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { users, user_states } from '@/db/schema/users';
import { and, eq, sql } from 'drizzle-orm';
import { validateAcademicEmail } from '@/util/validate-academic-email';
import { EmailService } from '@/mail/email.service';
import { BaseProcessor } from './base.processor';
import { DEFAULT_CONCURRENCY } from '../queue.constants';
import { questionsSchema } from '@/db/schema';

@Injectable()
@Processor(QueueName.GENERAL, {
  concurrency: DEFAULT_CONCURRENCY,
})
export class GeneralProcessor extends BaseProcessor {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly emailService: EmailService,
  ) {
    super('GeneralProcessor');
  }

  /**
   * Process general task jobs
   * @param job The job to process
   */
  async process(job: Job): Promise<JobResult> {
    this.logger.debug(`Processing general job ${job.id} of type ${job.name}`);

    try {
      switch (job.name) {
        case GeneralJobType.PROCESS_TASK:
          return await this.processTask(job.data as GeneralTaskJobData);
        case GeneralJobType.ACADEMIC_EMAIL_REMINDER:
          return await this.processAcademicEmailReminder(
            job.data as AcademicEmailReminderJobData,
          );
        case GeneralJobType.BULK_DELETE_USERS:
          return await this.processBulkDeleteUsers(
            job.data as BulkDeleteUsersJobData,
          );
        default:
          throw new Error(`Unknown job type: ${job.name}`);
      }
    } catch (error: any) {
      this.logger.error(
        `Error processing general job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process a general task
   * @param data The task data
   */
  private async processTask(data: GeneralTaskJobData): Promise<JobResult> {
    try {
      // This is a placeholder for custom task processing logic
      // In a real implementation, you would handle different task types here
      this.logger.debug(
        `Processing task of type ${data.taskType} with payload: ${JSON.stringify(
          data.payload,
        )}`,
      );

      // Simulate task processing
      await new Promise((resolve) => setTimeout(resolve, 100));

      return {
        success: true,
        message: `Task ${data.taskType} processed successfully`,
        data: { processed: true, taskType: data.taskType },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to process task ${data.taskType}`,
        error: error.message,
      };
    }
  }

  /**
   * Process academic email reminder job
   * @param data The academic email reminder job data
   */
  private async processAcademicEmailReminder(
    data: AcademicEmailReminderJobData,
  ): Promise<JobResult> {
    const startTime = Date.now();
    const metrics = {
      count: 0,
      failures: 0,
    };

    try {
      // Get all users with PENDING status
      const pendingUsers = await this.drizzle.db
        .select()
        .from(users)
        .where(
          and(eq(users.state, user_states.PENDING), eq(users.deleted, false)),
        );

      if (pendingUsers.length === 0) {
        return {
          success: true,
          message: 'No pending users found',
          data: { count: 0, failures: 0, duration: Date.now() - startTime },
        };
      }

      // Filter users with non-academic emails
      const nonAcademicEmailUsers = pendingUsers.filter(
        (user) => !validateAcademicEmail(user.email),
      );
      if (nonAcademicEmailUsers.length > 0) {
        this.logger.debug(
          `Found ${nonAcademicEmailUsers.length} pending users with non-academic emails`,
        );
      }

      if (nonAcademicEmailUsers.length === 0) {
        return {
          success: true,
          message: 'No pending users with non-academic emails found',
          data: { count: 0, failures: 0, duration: Date.now() - startTime },
        };
      }

      // Calculate rejection date
      const rejectionDate = new Date();
      rejectionDate.setDate(rejectionDate.getDate() + data.rejectionDays);
      const formattedRejectionDate = rejectionDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
      });

      // Process users in batches to avoid overwhelming the email service
      const batchSize = data.batchSize || 10;
      const batches = Math.ceil(nonAcademicEmailUsers.length / batchSize);

      for (let i = 0; i < batches; i++) {
        const batchStart = i * batchSize;
        const batchEnd = Math.min(
          (i + 1) * batchSize,
          nonAcademicEmailUsers.length,
        );
        const userBatch = nonAcademicEmailUsers.slice(batchStart, batchEnd);

        // Process each user in the batch
        const emailPromises = userBatch.map(async (user) => {
          try {
            await this.emailService.sendCustomEmail({
              email: user.email,
              subject: 'Action Required: Academic Email Needed',
              template: 'academic-email-reminder',
              context: {
                userEmail: user.email,
                rejectionDate: formattedRejectionDate,
                daysRemaining: data.rejectionDays,
              },
            });

            metrics.count++;
            return { success: true, email: user.email };
          } catch (error: any) {
            metrics.failures++;
            this.logger.error(
              `Failed to send academic email reminder to ${user.email}:`,
              error?.stack,
            );
            return { success: false, email: user.email, error };
          }
        });

        // Wait for all emails in this batch to be processed
        await Promise.all(emailPromises);

        if (i < batches - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      const duration = Date.now() - startTime;
      this.logger.log(
        `Sent academic email reminders to ${metrics.count} users (${metrics.failures} failures) in ${duration}ms`,
      );

      return {
        success: true,
        message: `Sent academic email reminders to ${metrics.count} users`,
        data: {
          count: metrics.count,
          failures: metrics.failures,
          duration,
        },
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error('Error sending academic email reminders', error?.stack);

      return {
        success: false,
        message: `Failed to send academic email reminders: ${error.message}`,
        error: error.message,
        data: {
          count: metrics.count,
          failures: metrics.failures,
          duration,
        },
      };
    }
  }

  /**
   * Process bulk user deletion job
   * @param data The bulk deletion job data
   */
  private async processBulkDeleteUsers(
    data: BulkDeleteUsersJobData,
  ): Promise<JobResult> {
    const startTime = Date.now();
    const stats = {
      totalProcessed: data.userIds.length,
      successCount: 0,
      failureCount: 0,
      deletedUsers: [] as Array<{ id: string; email: string; role: string }>,
      failures: [] as Array<{ userId: string; email?: string; reason: string }>,
    };

    try {
      this.logger.log(
        `Starting bulk deletion of ${data.userIds.length} users initiated by ${data.adminUser.email}`,
      );

      // Process users in batches to avoid overwhelming the database
      const BATCH_SIZE = 10;
      const batches = Math.ceil(data.userIds.length / BATCH_SIZE);

      for (let i = 0; i < batches; i++) {
        const batchStart = i * BATCH_SIZE;
        const batchEnd = Math.min((i + 1) * BATCH_SIZE, data.userIds.length);
        const userBatch = data.userIds.slice(batchStart, batchEnd);

        // Process each user in the batch
        const deletionPromises = userBatch.map(async (userId) => {
          try {
            // Implement user deletion logic directly to avoid circular dependencies
            const deletedUser = await this.deleteUserById(userId);

            if (deletedUser) {
              stats.successCount++;
              stats.deletedUsers.push({
                id: deletedUser.id,
                email: deletedUser.email,
                role: deletedUser.role,
              });
            }

            return { success: true, userId };
          } catch (error: any) {
            stats.failureCount++;

            // Try to get user email for better error reporting
            let userEmail: string | undefined;
            try {
              const user = await this.drizzle.db.query.users.findFirst({
                where: eq(users.id, userId),
                columns: { email: true },
              });
              userEmail = user?.email;
            } catch {
              // Ignore error when fetching user email
            }

            stats.failures.push({
              userId,
              email: userEmail,
              reason: error.message || 'Unknown error',
            });

            this.logger.error(
              `Failed to delete user ${userId}: ${error.message}`,
              error.stack,
            );

            return { success: false, userId, error };
          }
        });

        // Wait for all deletions in this batch to complete
        await Promise.all(deletionPromises);

        // Add a small delay between batches to prevent overwhelming the system
        if (i < batches - 1) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }

      const duration = Date.now() - startTime;
      const completedAt = new Date().toISOString();

      this.logger.log(
        `Bulk deletion completed: ${stats.successCount} successful, ${stats.failureCount} failed in ${duration}ms`,
      );

      // Send completion email with statistics
      try {
        const adminName =
          data.adminUser.firstName && data.adminUser.lastName
            ? `${data.adminUser.firstName} ${data.adminUser.lastName}`
            : data.adminUser.email;

        await this.emailService.sendCustomEmail({
          email: data.adminUser.email,
          subject: 'Bulk User Deletion Report - Operation Complete',
          template: 'bulk-deletion-report',
          context: {
            ...stats,
            completedAt: new Date(completedAt).toLocaleString(),
            adminName,
            adminEmail: data.adminUser.email,
            adminRole: data.adminUser.role,
            duration: `${(duration / 1000).toFixed(2)} seconds`,
          },
          critical: true,
        });

        this.logger.log(`Bulk deletion report sent to ${data.adminUser.email}`);
      } catch (emailError: any) {
        this.logger.error(
          `Failed to send bulk deletion report to ${data.adminUser.email}: ${emailError.message}`,
          emailError.stack,
        );
      }

      return {
        success: true,
        message: `Bulk deletion completed: ${stats.successCount} successful, ${stats.failureCount} failed`,
        data: {
          ...stats,
          duration,
          completedAt,
        },
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error('Error in bulk user deletion', error.stack);

      return {
        success: false,
        message: `Bulk deletion failed: ${error.message}`,
        error: error.message,
        data: {
          ...stats,
          duration,
        },
      };
    }
  }

  /**
   * Delete a user by ID - implements the same logic as AuthService.deleteUser
   * This is done to avoid circular dependencies between AuthModule and QueueModule
   * @param userId The user ID to delete
   * @returns The deleted user information
   */
  private async deleteUserById(
    userId: string,
  ): Promise<{ id: string; email: string; role: string } | null> {
    // First, check if user exists and get their information
    const userToDelete = await this.drizzle.db.query.users.findFirst({
      where: eq(users.id, userId),
      with: {
        student_profile: true,
        profile: true,
      },
    });

    if (!userToDelete) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Check for questions created by this user (RESTRICT constraint)
    const questionsCount = await this.drizzle.db
      .select({ count: sql`COUNT(*)` })
      .from(questionsSchema)
      .where(eq(questionsSchema.created_by, userId));

    if (Number(questionsCount[0]?.count || 0) > 0) {
      throw new Error(
        'Cannot delete user: User has created questions that are referenced by other records. Please reassign or delete the questions first.',
      );
    }

    // Perform deletion in a transaction
    const deletionResult = await this.drizzle.db.transaction(async (tx) => {
      this.logger.log(`Deleting user ${userId} and all related data...`);

      // CASCADE: student_profiles, token, posts, notification_preferences, device_tokens
      // SET NULL: organisations.user_id, points_config.created_by, notification_templates.created_by,
      //           scheduled_notifications.created_by, raffles.createdBy, student_clubs admin fields

      // Delete the user (this will cascade to all related tables)
      const deletedUser = await tx
        .delete(users)
        .where(eq(users.id, userId))
        .returning({
          id: users.id,
          email: users.email,
          role: users.role,
        });

      if (!deletedUser.length) {
        throw new Error('Failed to delete user');
      }

      return deletedUser[0];
    });

    this.logger.log(
      `User deletion completed successfully for: ${deletionResult?.email}`,
    );

    return deletionResult || null;
  }
}
