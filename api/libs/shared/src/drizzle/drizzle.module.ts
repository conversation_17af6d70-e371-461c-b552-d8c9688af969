import { Global, Module, Logger } from '@nestjs/common';

import { Pool } from 'pg';
import { DrizzleService } from './drizzle.service';
import {
  ConfigurableDatabaseModule,
  CONNECTION_POOL,
  DATABASE_OPTIONS,
  DatabaseOptions,
} from './drizzle.module-definition';
import { EnvConfig } from '../dto/env-config.dto';

@Global()
@Module({
  exports: [DrizzleService],
  providers: [
    DrizzleService,
    {
      provide: CONNECTION_POOL,
      inject: [DATABASE_OPTIONS, EnvConfig],
      useFactory: (databaseOptions: DatabaseOptions, envConfig: EnvConfig) => {
        const logger = new Logger('DatabaseModule');

        let connectionString = databaseOptions.connectionString;

        if (
          envConfig.NODE_ENV === 'development' &&
          !connectionString.includes('sslmode=')
        ) {
          connectionString += '?sslmode=no-verify';
        }

        const maskedConnectionString = connectionString.replace(
          /:[^:@]*@/,
          ':********@',
        );
        logger.log(
          `Initializing database connection pool: ${maskedConnectionString}`,
        );

        const isDevelopment = envConfig.NODE_ENV === 'development';

        const calculatePoolSize = () => {
          if (isDevelopment) {
            return { max: 5, min: 1 };
          }

          const maxConnections = databaseOptions.max || 50;
          const minConnections =
            databaseOptions.min ||
            Math.max(10, Math.floor(maxConnections * 0.2));

          return { max: maxConnections, min: minConnections };
        };

        const { max, min } = calculatePoolSize();

        const pool = new Pool({
          connectionString,
          max,
          min,
          connectionTimeoutMillis: isDevelopment ? 5000 : 8000,
          idleTimeoutMillis: isDevelopment ? 30000 : 45000,
          statement_timeout: isDevelopment ? 15000 : 25000,
          idle_in_transaction_session_timeout: isDevelopment ? 15000 : 20000,
          query_timeout: isDevelopment ? 10000 : 15000,
          keepAlive: true,
          keepAliveInitialDelayMillis: isDevelopment ? 5000 : 10000,
          allowExitOnIdle: false,
          application_name: `${envConfig.APP_NAME || 'touching-lives'}-${envConfig.NODE_ENV}`,
        });

        pool.on('connect', () => {
          // Connection logging disabled for cleaner logs
        });

        pool.on('error', (err: any) => {
          logger.error('Database pool error', {
            error: {
              message: err.message,
              code: err.code || 'UNKNOWN',
              ...(isDevelopment && { stack: err.stack }),
            },
          });
        });

        pool.on('remove', () => {
          // Client removal logging disabled for cleaner logs
        });

        return pool;
      },
    },
  ],
})
export class DatabaseModule extends ConfigurableDatabaseModule {}
