import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from './drizzle.service';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import * as databaseSchema from 'src/db/schema/index';

export type DatabaseType = NodePgDatabase<typeof databaseSchema>;

export interface TransactionOptions {
  timeout?: number;
}

@Injectable()
export class TransactionService {
  private readonly logger = new Logger(TransactionService.name);

  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * Execute operation within a transaction with basic timeout handling
   */
  async withTransaction<T>(
    operation: (tx: DatabaseType) => Promise<T>,
    options: TransactionOptions = {},
  ): Promise<T> {
    const { timeout = 30000 } = options;

    return Promise.race([
      this.drizzle.db.transaction(operation),
      new Promise<never>((_, reject) =>
        setTimeout(
          () => reject(new Error(`Transaction timeout after ${timeout}ms`)),
          timeout,
        ),
      ),
    ]);
  }

  /**
   * Execute multiple operations in a single transaction
   */
  async withBatchTransaction<T>(
    operations: Array<(tx: DatabaseType) => Promise<any>>,
    options: TransactionOptions = {},
  ): Promise<T[]> {
    return this.withTransaction(async (tx) => {
      const results: T[] = [];

      for (const operation of operations) {
        const result = await operation(tx);
        results.push(result);
      }

      return results;
    }, options);
  }
}
