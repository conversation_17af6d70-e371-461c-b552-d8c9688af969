import { ConfigurableModuleBuilder } from '@nestjs/common';

export const CONNECTION_POOL = 'CONNECTION_POOL';

export interface DatabaseOptions {
  connectionString: string;
  max?: number;
  min?: number;
  debug?: boolean;
  connectionTimeoutMillis?: number;
  idleTimeoutMillis?: number;
  statementTimeout?: number;
  queryTimeout?: number;
}

export const {
  ConfigurableModuleClass: ConfigurableDatabaseModule,
  MODULE_OPTIONS_TOKEN: DATABASE_OPTIONS,
} = new ConfigurableModuleBuilder<DatabaseOptions>({
  moduleName: 'Drizzle',
}).build();
