export const NotificationRoutes = {
  SEND_NOTIFICATION: 'send-notification',
  SEND_MULTIPLE_NOTIFICATIONS: 'send-multiple-notifications',
  SEND_TOPIC_NOTIFICATION: 'send-topic-notification',
  GET_TOKEN: 'token',
  NOTIFICATION_STREAM: 'stream',
  SUBSCRIBE: 'subscribe',
  UNSUBSCRIBE: 'unsubscribe',
  SEND_NOTIFICATION_STREAM: 'send-notification-stream',
  POST_NOTIFICATION: 'send',
  GET_NOTIFICATION_STREAM: 'get-notification-stream',
};

export const NotificationEvent = {
  RAFFLE: 'raffle',
  QUIZ: 'quiz',
  SURVEY: 'survey',
  OPPORTUNITY: 'opportunity',
  EVENT: 'event',
  POST: 'post',
};

export const NotificationType = {
  NEW_QUIZ: 'new_quiz',
  ACTIVE_QUIZ: 'active_quiz',
  NEW_POST: 'new_post',
  NEW_EVENT: 'new_event',
  NEW_OPPORTUNITY: 'new_opportunity',
  ANNOUNCEMENT: 'announcement',
  RAFFLE_WINNER: 'raffle_winner',
  OPPORTUNITY_SELECTION: 'opportunity_selection',
};

export const NotificationChannel = {
  EMAIL: 'email',
  PUSH: 'push',
  IN_APP: 'in_app',
};

export const NotificationStatus = {
  PENDING: 'pending',
  SENT: 'sent',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
};

export const NotificationTemplate = {
  QUIZ: 'quiz',
  POST: 'post',
  ANNOUNCEMENT: 'announcement',
  RAFFLE_WINNER: 'raffle_winner',
  OPPORTUNITY_SELECTION: 'opportunity_selection',
};

export const NotificationModule = {
  QUIZ: 'quiz',
  POST: 'post',
  EVENT: 'event',
  OPPORTUNITY: 'opportunity',
  ANNOUNCEMENT: 'announcement',
  RAFFLE: 'raffle',
};
