export const QuestionRoutes = {
  GET_QUESTION: '/:id',
  DELETE_QUESTION: '/:id',
  UPDATE_QUESTION: '/:id',
  QUESTION_BANK_ID: '/questionBank/:questionBankId',
  CREATE_QUESTION: '',
  UPLOAD_CSV: 'upload',
  GET_GOLDEN_QUESTIONS: 'golden/:bankId',
  GET_QUIZ_BY_QUESTION_BANK_ID: 'golden-question/:questionBankId',
};

export const Question_Messages = {
  INVALID_REFERENCE:
    'Invalid reference provided. Please ensure that the referenced questionBankId exists.',
  //'A question with the same questionBankId and question already exists.',
  QUESTION_EXISTS:
    'A question with the same questionBankId and question already exists.',
  QUESTION_NOT_FOUND: 'Question not found',
  NO_QUESTION_BANK_ID: 'No questions found for the given questionBankId',
};
