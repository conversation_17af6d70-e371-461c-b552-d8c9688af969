export const MCQ_ROUTES = {
  Create_Question_Bank: '',
};

export const MCQ_Messages = {
  Question_Bank_Already_Exists:
    'Question bank with the same name or framework already exists',
};

export const QuestionBankRoutes = {
  GET_QUESTION_BANK_QUESTIONS: ':questionBankId/questions',
  UPDATE_QUESTION_BANK_QUESTIONS: ':questionBankId/questions',
};

export const Two_MB = 2 * 1054 * 1054;

export const MCQ_LeaderBoardRoutes = {
  GET_STUDENT_RANK: 'student-rank',
  GET_LEADERBOARD_BY_RANK: 'rank/:rank',
  REFRESH: 'refresh',
  FORCE_REFRESH: 'force-refresh',
  GET_FOR_WEB: 'web',
};

export const MCQ_QuestionBankRoutes = {
  GET_BY_ID: ':id',
  UPDATE_BY_ID: ':id',
  DELETE_BY_ID: ':id',
};
