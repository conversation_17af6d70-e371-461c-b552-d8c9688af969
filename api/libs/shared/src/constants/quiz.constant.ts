export const QuizRoutes = {
  CREATE_SCORE: 'score',
  GET_SCORE: ':quizId/score/:student_id',
  GET_ALL_QUIZ_SCORES: 'all/scores',
  GET_QUIZ_QUESTIONS: 'questions/:id',
  DELETE_QUESTION: ':id',
  UPDATE_QUESTION: ':id',
  GET_QUIZ: ':id',
  DELETE_QUIZ: ':id',
  UPDATE_QUIZ: ':id',
  ACTIVE_QUIZ: 'active',
  GET_QUIZ_BY_QUESTION_BANK: 'question-bank/:questionBankId',
};

export const QuizMessages = {
  QUIZ_SCORE_EXISTS: 'Quiz score already exists for the given user and quiz',
  QUIZ_NOT_FOUND: 'Quiz not found',
  QUIZ_SCORE_NOT_FOUND: 'Quiz score not found',
  QUIZ_UPDATE_STATUS: 'Quiz status updated to active',
  QUIZ_NOT_DUE: 'No quiz is due for activation',
  QUIZ_EXPIRED_STATUS: 'Quiz status updated to expired',
};
