export const PointConstant = {
  MODULES: {
    USER: 'User',
    PROFILE: 'Profile',
    POST: 'Post',
    MCQ: 'MCQ',
    EVENT: 'Event',
    OPPORTUNITY: 'Opportunity',
    CLUB: 'Club',
    QUIZ: 'Quiz',
  },
  ACTIONS: {
    LOGIN: 'Login',
    COMPLETE_PROFILE: 'Complete',
    LIKE_POST: 'Like',
    SHARE_POST: 'Share',
    ANSWER_MCQ: 'Answer',
    APPLY_EVENT: 'Apply',
    APPLY_OPPORTUNITY: 'Apply',
    JOIN_CLUB: 'Join',
    UNLIKE_POST: 'Unlike',
  },
  FREQUENCIES: {
    ONCE_A_DAY: 'Once Daily',
    ONCE: 'Once',
    EVERY_EVENT: 'Every event',
    ONCE_A_MONTH: 'Once a month',
    ONCE_A_WEEK: 'Once a week',
  },
};

export const PointSystemRoutes = {
  CONFIG: 'config',
  REWARD: 'reward',
  CONFIG_ID: 'config/:id',
  POINTS: 'points',
  ID: ':id',
  DISABLE: 'disable/:id',
  VERIFY_POINTS: 'verify-points/:studentId/:module/:action',
};
