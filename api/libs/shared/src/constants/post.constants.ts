export const PostControllerMessages = {
  CannotCreatePost:
    'An error occurred while creating the post. Please try again later.',
  CannotGetPosts:
    'An error occurred while fetching posts. Please try again later.',
  ImageIsRequired: "Image is required for 'active' posts",
  PostNotFound: 'Post not found',
};

export const PostServiceMessages = {
  StudentProfileNotFound: 'Student profile not found',
  PostNotFound: 'Post not found',
  UnAuthorized: 'User not authorized to perform the action',
  StudentNotAClubMember: 'Student is not a member of the club',
  StudentNotAClubAdmin: 'Student is not an admin of the club',
  ClubNotFound: 'Club not found',
  ClubNotCreatedByAdmin: 'Club was not created by the admin',
};

export const PostRoutes = {
  DISABLE_POST: ':id/disable',
  ENABLE_POST: ':id/enable',
  DELETE_POST: ':id',
  GET_ONE_POST: ':id',
  UPDATE_POST: ':id',
  UPDATE_EVENT: ':id',
  UPDATE_OPPORTUNITY: ':id',
  GET_EVENT_BY_ID: ':id',
  GET_OPPORTUNITY_BY_ID: ':id',
  GET_TRENDING_BY_ID: 'trending/:id',
  CLUB_EVENT: 'club-event/:clubId',
  GET_CLUB_POSTS: 'club/:id',
  CREATE_CLUB_SPECIFIC_POST: '/club-general-post/:clubId',
  LIKE_POST: ':id/like',
  SHARE_POST: ':id/share',
  GET_POST_ENGAGEMENTS: ':id/engagements',
  SCHEDULE_POST: ':id/schedule',
};

export const PostEngagementRoutes = {
  RSVP: ':id/rsvp',
};
