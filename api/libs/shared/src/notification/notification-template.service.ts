import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  notification_templates,
  notification_types,
} from '@/db/schema/notification_system';
import { eq } from 'drizzle-orm';
import * as Handlebars from 'handlebars';
import {
  formatNotificationTypeCode,
  isValidNotificationTypeCode,
} from './utils/notification-code-formatter';
import { CacheService } from '@app/shared/cache/cache.service';
import { CacheConfigService } from '@app/shared/cache/cache-config.service';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { CacheInvalidate } from '@app/shared/cache/decorators/cache-invalidate.decorator';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';

@Injectable()
export class NotificationTemplateService {
  private readonly logger = new Logger(NotificationTemplateService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
    private readonly cacheConfigService: CacheConfigService,
  ) {}

  /**
   * Get all notification templates
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    ttl: CACHE_TTL.ONE_HOUR,
    keyGenerator: () => ['templates', 'all'],
  })
  async getNotificationTemplates(): Promise<any[]> {
    this.logger.debug('Fetching all notification templates from database');
    const templates = await this.drizzle.db
      .select()
      .from(notification_templates);

    return templates;
  }

  /**
   * Create notification template
   *
   * @param data Template data
   * @param notificationType Optional notification type to associate with this template
   * @returns The created template
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    keys: () => ['templates', 'all'],
  })
  async createNotificationTemplate(
    data: {
      name: string;
      description: string;
      title_template: string;
      body_template: string;
      email_subject_template?: string;
      email_body_template?: string;
      created_by?: string;
    },
    notificationType?: {
      code?: string;
      name: string;
      description: string;
      module: string;
      default_channels?: string[];
      isSeedOperation?: boolean;
    },
  ): Promise<any> {
    try {
      // Start a transaction to ensure template and type are created together
      return await this.drizzle.db.transaction(async (tx) => {
        // Create the template
        const [template] = await tx
          .insert(notification_templates)
          .values(data)
          .returning();

        if (!template) {
          throw new Error('Failed to create notification template');
        }

        // If notification type data is provided, create the type and associate it with the template
        if (notificationType) {
          // Format or validate the code
          let typeCode = notificationType.code;

          if (!typeCode) {
            // If no code is provided, generate one from the name and module
            typeCode = formatNotificationTypeCode(
              notificationType.name,
              notificationType.module,
            );
          } else if (!isValidNotificationTypeCode(typeCode)) {
            // Validate the code format
            throw new BadRequestException(
              `Invalid notification type code format: ${typeCode}. Code must be lowercase, contain only letters, numbers, and underscores, and start with a letter.`,
            );
          }

          // Check if a type with this code already exists
          const existingType = await tx
            .select()
            .from(notification_types)
            .where(eq(notification_types.code, typeCode))
            .then((results) => results[0]);

          if (existingType) {
            throw new BadRequestException(
              `A notification type with code '${typeCode}' already exists`,
            );
          }

          // Create the notification type
          await tx.insert(notification_types).values({
            code: typeCode,
            name: notificationType.name,
            description: notificationType.description,
            module: notificationType.module,
            template_id: template.id,
            default_channels: notificationType.default_channels || ['push'],
          });
        }

        return template;
      });
    } catch (error: any) {
      this.logger.error(
        `Failed to create notification template:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Update notification template
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    keys: (args) => ['templates', 'all', `template:${args[0]}`],
  })
  async updateNotificationTemplate(
    id: string,
    data: Partial<{
      name: string;
      description: string;
      title_template: string;
      body_template: string;
      email_subject_template?: string;
      email_body_template?: string;
    }>,
  ): Promise<any> {
    try {
      const [template] = await this.drizzle.db
        .update(notification_templates)
        .set(data)
        .where(eq(notification_templates.id, id))
        .returning();

      if (!template) {
        throw new NotFoundException(`Template with ID ${id} not found`);
      }

      return template;
    } catch (error: any) {
      this.logger.error(
        `Failed to update notification template:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Get notification template by ID
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    ttl: CACHE_TTL.ONE_HOUR,
    keyGenerator: (args) => ['template', args[0]], // args[0] is the id
    condition: (args) => !!args[0], // Only cache if ID is provided
  })
  async getNotificationTemplate(id: string): Promise<any> {
    this.logger.debug(`Fetching notification template ${id} from database`);
    const [template] = await this.drizzle.db
      .select()
      .from(notification_templates)
      .where(eq(notification_templates.id, id));

    return template;
  }

  /**
   * Compile template with Handlebars
   */
  compileTemplate(template: string, data: Record<string, any>): string {
    try {
      const compiledTemplate = Handlebars.compile(template);
      return compiledTemplate(data);
    } catch (error: any) {
      this.logger.error(
        `Failed to compile template: ${template}`,
        error?.stack,
      );
      return template;
    }
  }
}
