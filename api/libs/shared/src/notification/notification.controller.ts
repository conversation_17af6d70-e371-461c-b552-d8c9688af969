import {
  <PERSON>,
  Post,
  Body,
  UseGuards,
  Get,
  S<PERSON>,
  <PERSON><PERSON>,
  Query,
  Logger,
  Param,
} from '@nestjs/common';
import { NotificationService } from './notification.service';
import { EnhancedNotificationService } from '../enhanced-notification/enhanced-notification.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UseRoles } from 'nest-access-control';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { RoleGuard } from '@/guards/role.guard';
import * as notificationDto from '@app/shared/notification/dto/notification.dto';
import { NotificationRoutes } from '../constants/notification.constant';
import { map, Observable } from 'rxjs';
import type { Response } from 'express';

export { PushNotificationController as NotificationController };

@ApiTags('Push Notifications')
@Controller({ version: '1', path: 'notification/push' })
export class PushNotificationController {
  private readonly logger = new Logger(PushNotificationController.name, {
    timestamp: true,
  });

  constructor(private readonly notificationService: NotificationService) {}

  @Get(NotificationRoutes.GET_TOKEN)
  @ApiOperation({ summary: 'Get Firebase access token' })
  @ApiResponse({ status: 200, description: 'Token retrieved successfully' })
  async getToken() {
    try {
      return this.notificationService.getAccessToken();
    } catch (error) {
      this.logger.error('Error getting token', error);
      throw error;
    }
  }

  @Post(NotificationRoutes.SEND_NOTIFICATION)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'create', possession: 'any' })
  @ApiOperation({ summary: 'Send a push notification to a single device' })
  @ApiResponse({ status: 200, description: 'Notification sent successfully' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async sendNotification(@Body() data: notificationDto.SendNotificationDto) {
    try {
      return this.notificationService.sendNotification(data);
    } catch (error) {
      this.logger.error('Error sending notification', error);
      throw error;
    }
  }

  @Post(NotificationRoutes.SEND_MULTIPLE_NOTIFICATIONS)
  @ApiOperation({ summary: 'Send push notifications to multiple devices' })
  @ApiResponse({ status: 200, description: 'Notifications sent successfully' })
  async sendMultipleNotifications(
    @Body() body: notificationDto.MultipleDeviceNotificationDtoClass,
  ) {
    try {
      return this.notificationService.sendNotificationToMultipleTokens(body);
    } catch (error) {
      this.logger.error('Error sending multiple notifications', error);
      throw error;
    }
  }

  @Post(NotificationRoutes.SEND_TOPIC_NOTIFICATION)
  @ApiOperation({ summary: 'Send a push notification to a topic' })
  @ApiResponse({
    status: 200,
    description: 'Topic notification sent successfully',
  })
  async sendTopicNotification(
    @Body() body: notificationDto.TopicNotificationDtoClass,
  ) {
    try {
      return this.notificationService.sendTopicNotification(body);
    } catch (error) {
      this.logger.error('Error sending topic notification', error);
      throw error;
    }
  }
}

@ApiTags('Server-Sent Events')
@Controller({ version: '1', path: 'notification/sse' })
export class SSENotificationController {
  private readonly logger = new Logger(SSENotificationController.name, {
    timestamp: true,
  });

  constructor(private readonly notificationService: NotificationService) {}

  @Post(NotificationRoutes.SUBSCRIBE)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'notification-web-stream',
    action: 'create',
    possession: 'any',
  })
  @ApiOperation({ summary: 'Subscribe to notification stream' })
  @ApiResponse({
    status: 200,
    description: 'Subscribed to notification stream successfully',
  })
  @CLIENT_TYPE(AppClients.WEB)
  subscribeToNotificationStream(
    @Body() data: { eventId: string; event: string },
    @Res() res: Response,
  ) {
    this.setupSseHeaders(res);
    const { eventId, event } = data;
    return this.notificationService.subscribe(event, eventId);
  }

  @Post(NotificationRoutes.UNSUBSCRIBE)
  @ApiOperation({ summary: 'Unsubscribe from notification stream' })
  @ApiResponse({
    status: 200,
    description: 'Unsubscribed from notification stream successfully',
  })
  @CLIENT_TYPE(AppClients.WEB)
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'notification-web-stream',
    action: 'delete',
    possession: 'any',
  })
  unsubscribeFromNotificationStream(
    @Body() data: { eventId: string; event: string },
  ) {
    const { eventId, event } = data;
    return this.notificationService.unsubscribe(eventId, event);
  }

  @Sse('stream')
  @ApiOperation({ summary: 'Get notification stream' })
  @ApiResponse({ status: 200, description: 'Stream established successfully' })
  sse(@Query('event') event: string): Observable<MessageEvent> {
    const stream = this.notificationService.getNotificationStream(event);
    if (!stream) {
      this.logger.error('Notification stream not found');
      throw new Error('Notification stream not found');
    }
    return stream.pipe(
      map(
        (data) => new MessageEvent('message', { data: JSON.stringify(data) }),
      ),
    );
  }

  private setupSseHeaders(res: Response) {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders();
  }
}
/**
 * Controller for handling notification unsubscribe functionality
 */
@ApiTags('Unsubscribe Notification')
@Controller({ version: '1', path: 'notification/unsubscribe' })
export class UnSubscribeNotificationController {
  private readonly logger = new Logger(UnSubscribeNotificationController.name, {
    timestamp: true,
  });

  constructor(
    private readonly enhancedNotificationService: EnhancedNotificationService,
    private readonly notificationService: NotificationService,
  ) {}

  @Post('device-token')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'delete', possession: 'own' })
  @ApiOperation({
    summary: 'Unsubscribe a device token from push notifications',
  })
  @ApiResponse({
    status: 200,
    description: 'Device token unsubscribed successfully',
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async unsubscribeDeviceToken(
    @Body() data: { userId: string; token: string },
  ) {
    try {
      await this.enhancedNotificationService.unregisterDeviceToken(
        data.userId,
        data.token,
      );
      return {
        success: true,
        message: 'Device token unsubscribed successfully',
      };
    } catch (error) {
      this.logger.error('Error unsubscribing device token', error);
      throw error;
    }
  }

  @Post('remove-notification')
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'push-notification',
    action: 'delete',
    possession: 'own',
  })
  @ApiOperation({
    summary: 'Remove a specific notification for a mobile user',
  })
  @ApiResponse({
    status: 200,
    description: 'Notification removed successfully',
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async removeNotification(
    @Body() data: { userId: string; notificationId: string },
  ) {
    try {
      this.logger.log(
        `Removing notification ${data.notificationId} for user ${data.userId}`,
      );

      // First, check if the notification exists and belongs to the user
      await this.enhancedNotificationService.getNotificationLogById(
        data.userId,
        data.notificationId,
      );

      // Delete the notification
      await this.enhancedNotificationService.deleteNotificationLog(
        data.userId,
        data.notificationId,
      );

      return {
        success: true,
        message: 'Notification removed successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error removing notification ${data.notificationId} for user ${data.userId}`,
        error,
      );
      throw error;
    }
  }

  @Post('preferences')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'own' })
  @ApiOperation({ summary: 'Update notification preferences' })
  @ApiResponse({
    status: 200,
    description: 'Notification preferences updated successfully',
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async updateNotificationPreferences(
    @Body()
    data: {
      userId: string;
      preferences: Array<{
        notification_type_id: string;
        email_enabled?: boolean;
        push_enabled?: boolean;
        in_app_enabled?: boolean;
      }>;
    },
  ) {
    try {
      await this.enhancedNotificationService.updateUserNotificationPreferences(
        data.userId,
        data.preferences,
      );
      return {
        success: true,
        message: 'Notification preferences updated successfully',
      };
    } catch (error) {
      this.logger.error('Error updating notification preferences', error);
      throw error;
    }
  }

  @Post('all/:userId')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'own' })
  @ApiOperation({ summary: 'Unsubscribe from all notifications' })
  @ApiResponse({
    status: 200,
    description: 'Unsubscribed from all notifications successfully',
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async unsubscribeFromAllNotifications(@Param('userId') userId: string) {
    try {
      // Get all notification types
      const notificationTypes =
        await this.enhancedNotificationService.getNotificationTypes();

      // Create preferences array with all channels disabled
      const preferences = notificationTypes.map((type: { id: string }) => ({
        notification_type_id: type.id,
        email_enabled: false,
        push_enabled: false,
        in_app_enabled: false,
      }));

      // Update preferences
      await this.enhancedNotificationService.updateUserNotificationPreferences(
        userId,
        preferences,
      );

      return {
        success: true,
        message: 'Unsubscribed from all notifications successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error unsubscribing from all notifications for user ${userId}`,
        error,
      );
      throw error;
    }
  }

  @Post('test-token')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'any' })
  @ApiOperation({ summary: 'Test a device token' })
  @ApiResponse({ status: 200, description: 'Token test results' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async testDeviceToken(@Body() data: { token: string }) {
    try {
      try {
        const result = await this.notificationService.sendNotification({
          token: data.token,
          notification: {
            title: 'Token Test',
            body: 'This is a test notification to verify your device token',
          },
          data: {
            test: 'true',
            timestamp: new Date().toISOString(),
          },
        });

        return {
          success: true,
          message: 'Device token is valid',
          details: result,
        };
      } catch (error: any) {
        // Check if it's a token validation error
        if (error.message && error.message.includes('registration token')) {
          return {
            success: false,
            message: 'Device token is invalid',
            error: error.message,
          };
        }

        // If it's another type of error, it might be a Firebase configuration issue
        return {
          success: false,
          message: 'Error testing device token',
          error: error.message,
          stack: error.stack,
        };
      }
    } catch (error: any) {
      this.logger.error('Error testing device token', error);
      throw error;
    }
  }
}
