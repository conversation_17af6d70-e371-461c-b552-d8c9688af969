/**
 * Utility functions for standardizing notification data
 */

/**
 * Validate that notification data contains the appropriate ID fields based on module type
 * @param data - Notification data to validate
 * @param module - Module type (event, post, opportunity, quiz, raffle)
 * @returns Validation result with missing fields
 */
export function validateNotificationData(
  data: Record<string, unknown> | undefined,
  module: string,
): {
  isValid: boolean;
  missingFields: string[];
  recommendations: string[];
} {
  if (!data) {
    return {
      isValid: false,
      missingFields: ['data'],
      recommendations: ['Notification data is required'],
    };
  }

  const missingFields: string[] = [];
  const recommendations: string[] = [];

  switch (module) {
    case 'event':
      if (!data.event_id && !data.eventId) {
        missingFields.push('event_id');
        recommendations.push('Event notifications should include event_id');
      }
      if (!data.post_id && !data.postId) {
        missingFields.push('post_id');
        recommendations.push(
          'Event notifications should include post_id (events are posts)',
        );
      }
      break;

    case 'post':
      if (!data.post_id && !data.postId) {
        missingFields.push('post_id');
        recommendations.push('Post notifications should include post_id');
      }
      break;

    case 'opportunity':
      if (!data.opportunity_id && !data.opportunityId) {
        missingFields.push('opportunity_id');
        recommendations.push(
          'Opportunity notifications should include opportunity_id',
        );
      }
      if (!data.post_id && !data.postId) {
        missingFields.push('post_id');
        recommendations.push(
          'Opportunity notifications should include post_id (opportunities are posts)',
        );
      }
      break;

    case 'quiz':
      if (!data.quiz_id && !data.quizId) {
        missingFields.push('quiz_id');
        recommendations.push('Quiz notifications should include quiz_id');
      }
      break;

    case 'raffle':
      if (!data.raffle_id && !data.raffleId) {
        missingFields.push('raffle_id');
        recommendations.push('Raffle notifications should include raffle_id');
      }
      break;

    default:
      // For other modules, no specific validation needed
      break;
  }

  return {
    isValid: missingFields.length === 0,
    missingFields,
    recommendations,
  };
}

/**
 * Get expected ID field names for a given module type
 * @param module - Module type
 * @returns Array of expected ID field names
 */
export function getExpectedIdFields(module: string): string[] {
  switch (module) {
    case 'event':
      return ['event_id', 'post_id'];
    case 'post':
      return ['post_id'];
    case 'opportunity':
      return ['opportunity_id', 'post_id'];
    case 'quiz':
      return ['quiz_id'];
    case 'raffle':
      return ['raffle_id'];
    default:
      return [];
  }
}

/**
 * Check if notification data has all required ID fields for the module type
 * @param data - Notification data
 * @param module - Module type
 * @returns True if all required fields are present
 */
export function hasRequiredIdFields(
  data: Record<string, unknown> | undefined,
  module: string,
): boolean {
  const validation = validateNotificationData(data, module);
  return validation.isValid;
}

/**
 * Get a summary of notification data structure for debugging
 * @param data - Notification data
 * @param module - Module type
 * @returns Summary object with validation info
 */
export function getNotificationDataSummary(
  data: Record<string, unknown> | undefined,
  module: string,
): {
  module: string;
  hasData: boolean;
  presentFields: string[];
  expectedFields: string[];
  validation: ReturnType<typeof validateNotificationData>;
} {
  const expectedFields = getExpectedIdFields(module);
  const presentFields = data ? Object.keys(data) : [];
  const validation = validateNotificationData(data, module);

  return {
    module,
    hasData: !!data,
    presentFields,
    expectedFields,
    validation,
  };
}
