/**
 * Utility functions for formatting notification type codes
 */

/**
 * Format a string into a valid notification type code
 *
 * Rules:
 * - Convert to lowercase
 * - Replace spaces with underscores
 * - Remove special characters
 * - Ensure it starts with a letter
 * - Prefix with module name if provided
 *
 * @param name The name to format
 * @param module Optional module name to prefix
 * @returns Formatted code
 */
export function formatNotificationTypeCode(
  name: string,
  module?: string,
): string {
  // Convert to lowercase
  let code = name.toLowerCase();

  // Replace spaces with underscores
  code = code.replace(/\s+/g, '_');

  // Remove special characters (keep only letters, numbers, and underscores)
  code = code.replace(/[^a-z0-9_]/g, '');

  // Ensure it starts with a letter
  if (!/^[a-z]/.test(code)) {
    code = 'n_' + code;
  }

  // Prefix with module name if provided
  if (module) {
    const modulePrefix = module
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '');

    // Only add the module prefix if it's not already part of the code
    if (!code.startsWith(modulePrefix + '_')) {
      code = modulePrefix + '_' + code;
    }
  }

  return code;
}

/**
 * Validate if a notification type code is properly formatted
 *
 * @param code The code to validate
 * @returns True if valid, false otherwise
 */
export function isValidNotificationTypeCode(code: string): boolean {
  // Code must be lowercase, contain only letters, numbers, and underscores,
  // and start with a letter
  return /^[a-z][a-z0-9_]*$/.test(code);
}
