import { Test, TestingModule } from '@nestjs/testing';
import { NotificationHistoryService } from './notification-history.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CacheService } from '@app/shared/cache/cache.service';
import { NotificationEntityService } from './notification-entity.service';
import { NotFoundException } from '@nestjs/common';
import { CACHE_PREFIXES } from '@app/shared/constants/cache.constant';

describe('NotificationHistoryService', () => {
  let service: NotificationHistoryService;
  let drizzleService: DrizzleService;
  let cacheService: CacheService;

  const mockNotificationLog = {
    id: 'test-notification-id',
    user_id: 'test-user-id',
    notification_type_id: 'test-type-id',
    title: 'Test Notification',
    body: 'This is a test notification',
    data: { module: 'test-module' },
    channels: ['push'],
    status: 'sent',
    error: null,
    created_at: new Date().toISOString(),
    read_at: null,
  };

  beforeEach(async () => {
    const mockDrizzleService = {
      db: {
        select: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        then: jest.fn().mockResolvedValue([mockNotificationLog]),
        delete: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        values: jest.fn().mockReturnThis(),
        returning: jest
          .fn()
          .mockResolvedValue([{ id: 'test-notification-id' }]),
      },
    };

    const mockCacheService = {
      get: jest.fn().mockResolvedValue(null),
      set: jest.fn().mockResolvedValue(true),
      del: jest.fn().mockResolvedValue(true),
      invalidatePattern: jest.fn().mockResolvedValue(1),
      incrementCacheVersion: jest.fn().mockResolvedValue(true),
    };

    const mockNotificationEntityService = {
      enrichNotificationWithEntityDetails: jest
        .fn()
        .mockImplementation((notification) => notification),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationHistoryService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: NotificationEntityService,
          useValue: mockNotificationEntityService,
        },
      ],
    }).compile();

    service = module.get<NotificationHistoryService>(
      NotificationHistoryService,
    );
    drizzleService = module.get<DrizzleService>(DrizzleService);
    cacheService = module.get<CacheService>(CacheService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('deleteNotificationLog', () => {
    it('should delete a notification log and invalidate caches', async () => {
      // Mock the getNotificationLogById method
      jest
        .spyOn(service, 'getNotificationLogById')
        .mockResolvedValue(mockNotificationLog);

      // Mock the drizzle delete operation
      const deleteSpy = jest
        .spyOn(drizzleService.db, 'delete')
        .mockReturnValue({
          where: jest.fn().mockReturnThis(),
          returning: jest
            .fn()
            .mockResolvedValue([{ id: 'test-notification-id' }]),
        } as any);

      // Call the method
      await service.deleteNotificationLog(
        'test-user-id',
        'test-notification-id',
      );

      // Verify the notification was deleted
      expect(deleteSpy).toHaveBeenCalled();

      // Verify cache invalidation
      expect(cacheService.del).toHaveBeenCalledWith(
        `${CACHE_PREFIXES.NOTIFICATION}:log:test-user-id:test-notification-id`,
      );

      // Verify the timestamp-based cache invalidation was called
      expect(cacheService.set).toHaveBeenCalledWith(
        `${CACHE_PREFIXES.NOTIFICATION}:history:test-user-id:last_updated`,
        expect.any(String),
        expect.any(Number),
      );

      // Verify pattern-based invalidation was attempted
      expect(cacheService.invalidatePattern).toHaveBeenCalledWith(
        expect.stringContaining(
          `*:${CACHE_PREFIXES.NOTIFICATION}:*:test-user-id:*`,
        ),
      );
    });

    it('should throw NotFoundException if notification does not exist', async () => {
      // Mock the getNotificationLogById method to throw NotFoundException
      const notFoundError = new NotFoundException('Notification log not found');
      jest
        .spyOn(service, 'getNotificationLogById')
        .mockRejectedValue(notFoundError);

      // Temporarily spy on the logger to suppress the expected error
      const loggerErrorSpy = jest
        .spyOn(service['logger'], 'error')
        .mockImplementation(() => {});

      // Call the method and expect it to throw
      try {
        await service.deleteNotificationLog('test-user-id', 'non-existent-id');
        // If we get here, the test should fail
        fail(
          'Expected service.deleteNotificationLog to throw NotFoundException',
        );
      } catch (error: unknown) {
        // Verify the error is a NotFoundException
        expect(error).toBe(notFoundError);

        // Verify delete was not called
        expect(drizzleService.db.delete).not.toHaveBeenCalled();

        // Verify cache invalidation was not called
        expect(cacheService.del).not.toHaveBeenCalled();
        expect(cacheService.set).not.toHaveBeenCalled();
        expect(cacheService.invalidatePattern).not.toHaveBeenCalled();

        // Verify that the logger.error was called
        expect(loggerErrorSpy).toHaveBeenCalled();
      } finally {
        // Restore the original logger implementation
        loggerErrorSpy.mockRestore();
      }
    });
  });

  describe('logNotification', () => {
    it('should be defined as a method', () => {
      expect(typeof service.logNotification).toBe('function');
    });
  });
});
