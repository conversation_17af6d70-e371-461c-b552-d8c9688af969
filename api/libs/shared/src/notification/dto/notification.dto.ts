import { ApiProperty } from '@nestjs/swagger';

import { z } from 'zod';

export const NotificationSchemaDto = z.object({
  title: z.string(),
  body: z.string(),
  icon: z.string().optional(),
});

export const MultipleDeviceNotificationDto = z.object({
  tokens: z.array(z.string()),
  title: z.string(),
  body: z.string(),
  icon: z.string().optional(),
  data: z.record(z.any()).optional(),
});

export const TopicNotificationDto = z.object({
  topic: z.string(),
  title: z.string(),
  body: z.string(),
  icon: z.string().optional(),
  data: z.record(z.any()).optional(),
});

export type NotificationDto = z.infer<typeof NotificationSchemaDto>;
export type MultipleDeviceNotificationDto = z.infer<
  typeof MultipleDeviceNotificationDto
>;
export type TopicNotificationDto = z.infer<typeof TopicNotificationDto>;

export class NotificationDtoClass {
  @ApiProperty({ description: 'FCM registration token' })
  token!: string;

  @ApiProperty({ description: 'Notification title' })
  title!: string;

  @ApiProperty({ description: 'Notification body' })
  body!: string;

  @ApiProperty({ description: 'Notification icon URL', required: false })
  icon?: string;
}

export class MultipleDeviceNotificationDtoClass {
  @ApiProperty({ description: 'Array of FCM registration tokens' })
  tokens!: string[];

  @ApiProperty({ description: 'Notification title' })
  title!: string;

  @ApiProperty({ description: 'Notification body' })
  body!: string;

  @ApiProperty({ description: 'Notification icon URL', required: false })
  icon?: string;

  @ApiProperty({
    description: 'Additional data to send with the notification',
    required: false,
  })
  data?: Record<string, any>;
}

export class TopicNotificationDtoClass {
  @ApiProperty({ description: 'FCM topic name' })
  topic!: string;

  @ApiProperty({ description: 'Notification title' })
  title!: string;

  @ApiProperty({ description: 'Notification body' })
  body!: string;

  @ApiProperty({ description: 'Notification icon URL', required: false })
  icon?: string;

  @ApiProperty({
    description: 'Additional data to send with the notification',
    required: false,
  })
  data?: Record<string, any>;
}

export const SendNotificationSchema = z.object({
  token: z.string(),
  notification: NotificationSchemaDto,
  data: z.record(z.string()).optional(),
});
export const SubscriptionSchema = z.object({
  id: z.string(),
  event: z.string(),
  res: z.any(),
});

export type Subscription = z.infer<typeof SubscriptionSchema>;
export type SendNotificationDto = z.infer<typeof SendNotificationSchema>;
