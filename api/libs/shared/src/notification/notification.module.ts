import { Module } from '@nestjs/common';
import { NotificationService } from './notification.service';
import {
  NotificationController,
  SSENotificationController,
} from './notification.controller';
import { FirebaseModule } from '@/firebase/firebase.module';
import { UserRepository } from '@/repositories/user.repository';

@Module({
  imports: [FirebaseModule],
  controllers: [NotificationController, SSENotificationController],
  providers: [NotificationService, UserRepository],
  exports: [NotificationService],
})
export class NotificationModule {}
