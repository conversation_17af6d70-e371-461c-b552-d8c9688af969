import { Injectable, Logger } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';

/**
 * Service to enrich notification logs with entity details
 * This service fetches the actual entities (events, posts, opportunities)
 * referenced in notification data
 */
@Injectable()
export class NotificationEntityService {
  private readonly logger = new Logger(NotificationEntityService.name);

  constructor(private readonly moduleRef: ModuleRef) {}

  /**
   * Enrich notification log with entity details
   * @param notificationLog - The notification log to enrich
   * @returns Enriched notification log with entity details
   */
  async enrichNotificationWithEntityDetails(
    notificationLog: any,
  ): Promise<any> {
    if (!notificationLog.data) {
      return notificationLog;
    }

    const data = notificationLog.data;
    const enrichedData = { ...data };

    try {
      // Determine the module type from notification type
      const module = notificationLog.notification_type?.module;

      switch (module) {
        case 'event':
          enrichedData.entityDetails = await this.fetchEventDetails(data);
          break;
        case 'post':
          enrichedData.entityDetails = await this.fetchPostDetails(data);
          break;
        case 'opportunity':
          enrichedData.entityDetails = await this.fetchOpportunityDetails(data);
          break;
        default:
          // For other modules, no specific entity fetching
          break;
      }
    } catch (error: any) {
      this.logger.warn(
        `Failed to enrich notification ${notificationLog.id} with entity details:`,
        error?.message || 'Unknown error',
      );
      // Don't fail the entire request if entity fetching fails
      enrichedData.entityFetchError = error?.message || 'Unknown error';
    }

    return {
      ...notificationLog,
      data: enrichedData,
    };
  }

  /**
   * Fetch event details from event_id
   */
  private async fetchEventDetails(data: any): Promise<any> {
    const eventId = data.event_id || data.eventId;
    if (!eventId) {
      return null;
    }

    try {
      const eventService = this.moduleRef.get('EventService', {
        strict: false,
      });
      if (!eventService) {
        this.logger.warn('EventService not available');
        return null;
      }

      const event = await eventService.getEventById(eventId);
      return {
        type: 'event',
        id: eventId,
        title: event.title,
        description: event.description,
        startDate: event.startDate,
        startTime: event.startTime,
        imageUrl: event.imageUrl,
        post: event.post
          ? {
              id: event.post.id,
              title: event.post.title,
              description: event.post.description,
              status: event.post.status,
            }
          : null,
      };
    } catch (error: any) {
      this.logger.warn(
        `Failed to fetch event ${eventId}:`,
        error?.message || 'Unknown error',
      );
      return null;
    }
  }

  /**
   * Fetch post details from post_id
   */
  private async fetchPostDetails(data: any): Promise<any> {
    const postId = data.post_id || data.postId;
    if (!postId) {
      return null;
    }

    try {
      const postService = this.moduleRef.get('PostService', { strict: false });
      if (!postService) {
        this.logger.warn('PostService not available');
        return null;
      }

      // Create a mock admin user for fetching post details
      const mockUser = { id: 'system', role: 'admin' };
      const posts = await postService.getPosts(mockUser, { id: postId });

      if (posts.data && posts.data.length > 0) {
        const post = posts.data[0];
        return {
          type: 'post',
          id: postId,
          title: post.title,
          description: post.description,
          status: post.status,
          postType: post.type,
          isGlobal: post.isGlobal,
          createdAt: post.created_at,
        };
      }

      return null;
    } catch (error: any) {
      this.logger.warn(
        `Failed to fetch post ${postId}:`,
        error?.message || 'Unknown error',
      );
      return null;
    }
  }

  /**
   * Fetch opportunity details from opportunity_id
   */
  private async fetchOpportunityDetails(data: any): Promise<any> {
    const opportunityId = data.opportunity_id || data.opportunityId;
    if (!opportunityId) {
      return null;
    }

    try {
      const opportunityService = this.moduleRef.get('OpportunityService', {
        strict: false,
      });
      if (!opportunityService) {
        this.logger.warn('OpportunityService not available');
        return null;
      }

      // Create a mock admin user for fetching opportunity details
      const mockUser = { id: 'system', role: 'admin' };
      const opportunity = await opportunityService.getOpportunityById(
        opportunityId,
        mockUser,
      );

      return {
        type: 'opportunity',
        id: opportunityId,
        title: opportunity.post?.title,
        description: opportunity.post?.description,
        eligibility: opportunity.eligibility,
        applicationUrl: opportunity.applicationUrl,
        startDate: opportunity.startDate,
        endDate: opportunity.endDate,
        post: opportunity.post
          ? {
              id: opportunity.post.id,
              title: opportunity.post.title,
              description: opportunity.post.description,
              status: opportunity.post.status,
            }
          : null,
      };
    } catch (error: any) {
      this.logger.warn(
        `Failed to fetch opportunity ${opportunityId}:`,
        error?.message || 'Unknown error',
      );
      return null;
    }
  }

  /**
   * Validate that notification data contains valid entity IDs
   * @param notificationLog - The notification log to validate
   * @returns Validation result
   */
  async validateNotificationEntityIds(notificationLog: any): Promise<{
    isValid: boolean;
    missingEntities: string[];
    validEntities: string[];
  }> {
    const data = notificationLog.data;
    const module = notificationLog.notification_type?.module;
    const missingEntities: string[] = [];
    const validEntities: string[] = [];

    if (!data) {
      return { isValid: false, missingEntities: ['data'], validEntities: [] };
    }

    try {
      switch (module) {
        case 'event':
          const eventId = data.event_id || data.eventId;
          if (eventId) {
            const eventExists = await this.checkEventExists(eventId);
            if (eventExists) {
              validEntities.push(`event:${eventId}`);
            } else {
              missingEntities.push(`event:${eventId}`);
            }
          }

          const postId = data.post_id || data.postId;
          if (postId) {
            const postExists = await this.checkPostExists(postId);
            if (postExists) {
              validEntities.push(`post:${postId}`);
            } else {
              missingEntities.push(`post:${postId}`);
            }
          }
          break;

        case 'post':
          const postIdForPost = data.post_id || data.postId;
          if (postIdForPost) {
            const postExists = await this.checkPostExists(postIdForPost);
            if (postExists) {
              validEntities.push(`post:${postIdForPost}`);
            } else {
              missingEntities.push(`post:${postIdForPost}`);
            }
          }
          break;

        case 'opportunity':
          const opportunityId = data.opportunity_id || data.opportunityId;
          if (opportunityId) {
            const opportunityExists =
              await this.checkOpportunityExists(opportunityId);
            if (opportunityExists) {
              validEntities.push(`opportunity:${opportunityId}`);
            } else {
              missingEntities.push(`opportunity:${opportunityId}`);
            }
          }
          break;
      }
    } catch (error) {
      this.logger.error('Error validating notification entity IDs:', error);
    }

    return {
      isValid: missingEntities.length === 0,
      missingEntities,
      validEntities,
    };
  }

  /**
   * Check if an event exists
   */
  private async checkEventExists(eventId: string): Promise<boolean> {
    try {
      const eventService = this.moduleRef.get('EventService', {
        strict: false,
      });
      if (!eventService) return false;

      await eventService.getEventById(eventId);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a post exists
   */
  private async checkPostExists(postId: string): Promise<boolean> {
    try {
      const postService = this.moduleRef.get('PostService', { strict: false });
      if (!postService) return false;

      const mockUser = { id: 'system', role: 'admin' };
      const posts = await postService.getPosts(mockUser, { id: postId });
      return posts.data && posts.data.length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if an opportunity exists
   */
  private async checkOpportunityExists(
    opportunityId: string,
  ): Promise<boolean> {
    try {
      const opportunityService = this.moduleRef.get('OpportunityService', {
        strict: false,
      });
      if (!opportunityService) return false;

      const mockUser = { id: 'system', role: 'admin' };
      await opportunityService.getOpportunityById(opportunityId, mockUser);
      return true;
    } catch (error) {
      return false;
    }
  }
}
