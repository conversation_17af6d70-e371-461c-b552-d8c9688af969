import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  notification_templates,
  notification_types,
} from '@/db/schema/notification_system';
import { eq } from 'drizzle-orm';
import {
  formatNotificationTypeCode,
  isValidNotificationTypeCode,
} from './utils/notification-code-formatter';
import { CacheService } from '@app/shared/cache/cache.service';
import { CacheConfigService } from '@app/shared/cache/cache-config.service';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { CacheInvalidate } from '@app/shared/cache/decorators/cache-invalidate.decorator';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';

@Injectable()
export class NotificationTypeService {
  private readonly logger = new Logger(NotificationTypeService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
    private readonly cacheConfigService: CacheConfigService,
  ) {}

  /**
   * Get all notification types
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    ttl: CACHE_TTL.ONE_HOUR,
    keyGenerator: () => ['types', 'all'],
  })
  async getNotificationTypes(): Promise<any[]> {
    this.logger.debug('Fetching all notification types from database');
    const types = await this.drizzle.db
      .select()
      .from(notification_types)
      .leftJoin(
        notification_templates,
        eq(notification_types.template_id, notification_templates.id),
      )
      .orderBy(notification_types.name);

    return types.map((row) => ({
      ...row.notification_types,
      template: row.notification_templates,
    }));
  }

  /**
   * Create notification type
   *
   * @param data The notification type data
   * @param isSeedOperation Whether this is a seed operation (allows bypassing code validation)
   * @returns The created notification type
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    keys: () => ['types', 'all'],
  })
  async createNotificationType(
    data: {
      code?: string;
      name: string;
      description: string;
      module: string;
      template_id: string;
      default_channels?: string[];
    },
    isSeedOperation: boolean = false,
  ): Promise<any> {
    try {
      // Format or validate the code
      let typeCode = data.code;

      if (!typeCode) {
        // If no code is provided, generate one from the name and module
        typeCode = formatNotificationTypeCode(data.name, data.module);
      } else if (!isSeedOperation && !isValidNotificationTypeCode(typeCode)) {
        // For non-seed operations, validate the code format
        throw new BadRequestException(
          `Invalid notification type code format: ${typeCode}. Code must be lowercase, contain only letters, numbers, and underscores, and start with a letter.`,
        );
      }

      // Check if a type with this code already exists
      const existingType = await this.drizzle.db
        .select()
        .from(notification_types)
        .where(eq(notification_types.code, typeCode))
        .then((results) => results[0]);

      if (existingType) {
        throw new BadRequestException(
          `A notification type with code '${typeCode}' already exists`,
        );
      }

      // Create the notification type
      const [type] = await this.drizzle.db
        .insert(notification_types)
        .values({
          ...data,
          code: typeCode,
        })
        .returning();

      return type;
    } catch (error: any) {
      this.logger.error(`Failed to create notification type:`, error?.stack);
      throw error;
    }
  }

  /**
   * Update notification type
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    keys: (args) => ['types', 'all', `type:${args[0]}`],
  })
  async updateNotificationType(
    id: string,
    data: Partial<{
      name: string;
      description: string;
      module: string;
      template_id: string;
      default_channels?: string[];
    }>,
  ): Promise<any> {
    try {
      const [type] = await this.drizzle.db
        .update(notification_types)
        .set(data)
        .where(eq(notification_types.id, id))
        .returning();

      if (!type) {
        throw new NotFoundException(
          `Notification type with ID ${id} not found`,
        );
      }

      return type;
    } catch (error: any) {
      this.logger.error(`Failed to update notification type:`, error?.stack);
      throw error;
    }
  }

  /**
   * Get notification type by ID
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    ttl: CACHE_TTL.ONE_HOUR,
    keyGenerator: (args) => ['type', args[0]], // args[0] is the id
    condition: (args) => !!args[0], // Only cache if ID is provided
  })
  async getNotificationType(id: string): Promise<any> {
    this.logger.debug(`Fetching notification type ${id} from database`);
    const [notificationType] = await this.drizzle.db
      .select()
      .from(notification_types)
      .where(eq(notification_types.id, id));

    return notificationType;
  }
}
