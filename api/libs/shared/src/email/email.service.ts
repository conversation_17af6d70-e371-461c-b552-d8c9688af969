import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly mailClient: MailerService;

  constructor(mailerService: MailerService) {
    this.mailClient = mailerService;
  }

  async sendBulkEmails(
    emails: Array<{ email: string; template: string; data: any }>,
  ) {
    try {
      const emailPromises = emails.map(({ email, template, data }) =>
        this.mailClient.sendMail({
          to: email,
          template: template,
          context: data,
          subject: 'Account Activation Notification',
        }),
      );

      await Promise.all(emailPromises);
      return true;
    } catch (error) {
      this.logger.error('Bulk email sending failed:', error);
      throw error;
    }
  }

  private getTemplateId(template: string): string {
    const templateMap: Record<string, string> = {
      'waiting-list-approval': 'waiting-list',
      // Add other template mappings as needed
    };
    return templateMap[template] || template;
  }
}
