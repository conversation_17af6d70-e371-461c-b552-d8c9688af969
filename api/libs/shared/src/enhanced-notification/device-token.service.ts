import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { device_tokens } from '@/db/schema/notification_system';
import { and, eq } from 'drizzle-orm';

@Injectable()
export class DeviceTokenService {
  private readonly logger = new Logger(DeviceTokenService.name);

  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * Register device token for push notifications
   */
  async registerDeviceToken(
    userId: string,
    token: string,
    deviceType: string,
  ): Promise<void> {
    try {
      // Check if token already exists for this user
      const [existingToken] = await this.drizzle.db
        .select()
        .from(device_tokens)
        .where(
          and(
            eq(device_tokens.user_id, userId),
            eq(device_tokens.token, token),
          ),
        );

      if (existingToken) {
        // Update existing token
        await this.drizzle.db
          .update(device_tokens)
          .set({
            device_type: deviceType,
            updated_at: new Date().toISOString(),
          })
          .where(eq(device_tokens.id, existingToken.id));
      } else {
        // Create new token
        await this.drizzle.db.insert(device_tokens).values({
          user_id: userId,
          token,
          device_type: deviceType,
        });
      }
    } catch (error: any) {
      this.logger.error(
        `Failed to register device token for user ${userId}:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Unregister device token
   */
  async unregisterDeviceToken(userId: string, token: string): Promise<void> {
    try {
      await this.drizzle.db
        .delete(device_tokens)
        .where(
          and(
            eq(device_tokens.user_id, userId),
            eq(device_tokens.token, token),
          ),
        );
    } catch (error: any) {
      this.logger.error(
        `Failed to unregister device token for user ${userId}:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Get user device tokens
   */
  async getUserDeviceTokens(userId: string): Promise<string[]> {
    try {
      const deviceTokens = await this.drizzle.db
        .select({ token: device_tokens.token })
        .from(device_tokens)
        .where(eq(device_tokens.user_id, userId));

      return deviceTokens.map((dt) => dt.token);
    } catch (error: any) {
      this.logger.error(
        `Failed to get device tokens for user ${userId}:`,
        error?.stack,
      );
      throw error;
    }
  }
}
