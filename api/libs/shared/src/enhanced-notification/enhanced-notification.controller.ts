import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Sse,
  Request,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UseRoles } from 'nest-access-control';
import { UseGuards } from '@nestjs/common';
import { RoleGuard } from '@/guards/role.guard';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { User } from '@/guards/user.decorator';
import type { User as IUser } from '@/db/schema/users';
import { EnhancedNotificationService } from './enhanced-notification.service';
import {
  CreateNotificationTypeDto,
  CreateTemplateDto,
  ModuleToggleDto,
  ModuleToggleResponseDto,
  NotificationHistoryQueryDto,
  RegisterDeviceTokenDto,
  ScheduleNotificationDto,
  ScheduledNotificationsQueryDto,
  SendNotificationDto,
  UpdateBulkPreferencesDto,
  UpdateMobilePreferencesDto,
  UpdateNotificationTypeDto,
  UpdateTemplateDto,
} from './dto/enhanced-notification.dto';
import { MobileNotificationOptimizer } from './dto/mobile-notification.dto';
import { Observable, map } from 'rxjs';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@Controller({ version: '1', path: 'notifications' })
@ApiTags('Notifications')
export class EnhancedNotificationController {
  private readonly logger = new Logger(EnhancedNotificationController.name);

  constructor(
    private readonly notificationService: EnhancedNotificationService,
  ) {}

  /**
   * Send immediate notification
   *
   * Target audience can be filtered by:
   * - roles: Array of user roles (e.g., ['student', 'student_admin'])
   * - filters:
   *   - clubId: Filter by specific club ID
   *   - institutionIds: Array of institution IDs to target
   *   - countryIds: Array of country IDs to target
   */
  @Post('send')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'create', possession: 'any' })
  @ApiOperation({
    summary: 'Send an immediate notification',
    description:
      'Send notification to users filtered by roles, club, institution, or country',
  })
  @ApiResponse({ status: 200, description: 'Notification sent successfully' })
  @CLIENT_TYPE(AppClients.WEB)
  async sendNotification(@Body() data: SendNotificationDto) {
    try {
      await this.notificationService.sendNotificationToUsers({
        notificationTypeId: data.notification_type_id,
        data: data.data,
        targetAudience: data.target_audience,
        channels: data.channels,
      });
      return { success: true, message: 'Notification sent successfully' };
    } catch (error) {
      this.logger.error('Error sending notification', error);
      throw error;
    }
  }

  /**
   * Schedule notification for future delivery
   *
   * Target audience can be filtered by:
   * - roles: Array of user roles (e.g., ['student', 'student_admin'])
   * - filters:
   *   - clubId: Filter by specific club ID
   *   - institutionIds: Array of institution IDs to target
   *   - countryIds: Array of country IDs to target
   */
  @Post('schedule')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'create', possession: 'any' })
  @ApiOperation({
    summary: 'Schedule a notification for future delivery',
    description:
      'Schedule notification to users filtered by roles, club, institution, or country',
  })
  @ApiResponse({
    status: 200,
    description: 'Notification scheduled successfully',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async scheduleNotification(
    @Body() data: ScheduleNotificationDto,
    @User() user: IUser,
  ) {
    try {
      const scheduledNotification =
        await this.notificationService.scheduleNotification({
          notificationTypeId: data.notification_type_id,
          title: data.title,
          body: data.body,
          data: data.data,
          targetAudience: data.target_audience,
          scheduledFor: new Date(data.scheduled_for),
          channels: data.channels,
          createdBy: user.id,
        });
      return {
        message: 'Notification scheduled successfully',
        data: scheduledNotification,
      };
    } catch (error) {
      this.logger.error('Error scheduling notification', error);
      throw error;
    }
  }

  /**
   * Get notification templates
   */
  @Get('templates')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'any' })
  @ApiOperation({ summary: 'Get notification templates' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  @CLIENT_TYPE(AppClients.WEB)
  async getNotificationTemplates() {
    try {
      const templates =
        await this.notificationService.getNotificationTemplates();
      return { data: templates };
    } catch (error) {
      this.logger.error('Error getting notification templates', error);
      throw error;
    }
  }

  /**
   * Create notification template
   */
  @Post('templates')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'create', possession: 'any' })
  @ApiOperation({ summary: 'Create notification template' })
  @ApiResponse({ status: 201, description: 'Template created successfully' })
  @CLIENT_TYPE(AppClients.WEB)
  async createNotificationTemplate(
    @Body() data: CreateTemplateDto,
    @User() user: IUser,
  ) {
    try {
      const template =
        await this.notificationService.createNotificationTemplate({
          ...data,
          created_by: user.id,
        });
      return {
        success: true,
        message: 'Template created successfully',
        data: template,
      };
    } catch (error) {
      this.logger.error('Error creating notification template', error);
      throw error;
    }
  }

  /**
   * Update notification template
   */
  @Put('templates/:id')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'any' })
  @ApiOperation({ summary: 'Update notification template' })
  @ApiResponse({ status: 200, description: 'Template updated successfully' })
  @CLIENT_TYPE(AppClients.WEB)
  async updateNotificationTemplate(
    @Param('id', CustomParseUUIDPipe) id: string,
    @Body() data: UpdateTemplateDto,
  ) {
    try {
      const template =
        await this.notificationService.updateNotificationTemplate(id, data);
      return {
        success: true,
        message: 'Template updated successfully',
        data: template,
      };
    } catch (error) {
      this.logger.error('Error updating notification template', error);
      throw error;
    }
  }

  /**
   * Get notification types
   */
  @Get('types')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'any' })
  @ApiOperation({ summary: 'Get notification types' })
  @ApiResponse({ status: 200, description: 'Types retrieved successfully' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getNotificationTypes(@Request() req: any) {
    try {
      const types = await this.notificationService.getNotificationTypes();

      // Check if this is a mobile client request
      const isMobileClient = req.clientType === AppClients.MOBILE;

      if (isMobileClient) {
        // Optimize response for mobile clients
        const optimizedTypes =
          MobileNotificationOptimizer.optimizeNotificationTypes(types);
        return { success: true, data: optimizedTypes };
      }

      return { success: true, data: types };
    } catch (error) {
      this.logger.error('Error getting notification types', error);
      throw error;
    }
  }

  /**
   * Create notification type
   * Creates a new notification type with proper code formatting
   */
  @Post('types')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'create', possession: 'any' })
  @ApiOperation({
    summary: 'Create notification type',
    description:
      'Creates a new notification type. The code will be automatically formatted if not provided.',
  })
  @ApiResponse({
    status: 201,
    description: 'Notification type created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid code format or code already exists',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async createNotificationType(@Body() data: CreateNotificationTypeDto) {
    try {
      const type = await this.notificationService.createNotificationType(data);
      return {
        success: true,
        message: 'Notification type created successfully',
        data: type,
      };
    } catch (error) {
      this.logger.error('Error creating notification type', error);
      throw error;
    }
  }

  /**
   * Update notification type
   */
  @Put('types/:id')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'any' })
  @ApiOperation({ summary: 'Update notification type' })
  @ApiResponse({ status: 200, description: 'Type updated successfully' })
  @CLIENT_TYPE(AppClients.WEB)
  async updateNotificationType(
    @Param('id', CustomParseUUIDPipe) id: string,
    @Body() data: UpdateNotificationTypeDto,
  ) {
    try {
      const type = await this.notificationService.updateNotificationType(
        id,
        data,
      );
      return {
        success: true,
        message: 'Notification type updated successfully',
        data: type,
      };
    } catch (error) {
      this.logger.error('Error updating notification type', error);
      throw error;
    }
  }

  /**
   * Get user notification preferences
   */
  @Get('preferences')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'any' })
  @ApiOperation({ summary: 'Get user notification preferences' })
  @ApiResponse({
    status: 200,
    description: 'Preferences retrieved successfully',
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getUserNotificationPreferences(
    @User() user: IUser,
    @Request() req: any,
  ) {
    try {
      const preferences =
        await this.notificationService.getUserNotificationPreferences(user.id);

      const isMobileClient = req.clientType === AppClients.MOBILE;

      if (isMobileClient) {
        // Optimize response for mobile clients
        const optimizedPreferences =
          MobileNotificationOptimizer.optimizeNotificationPreferences(
            preferences,
          );
        return { success: true, data: optimizedPreferences };
      }

      return { success: true, data: preferences };
    } catch (error) {
      this.logger.error('Error getting user notification preferences', error);
      throw error;
    }
  }

  /**
   * Update user notification preferences
   */
  @Put('preferences')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'own' })
  @ApiOperation({ summary: 'Update user notification preferences' })
  @ApiResponse({ status: 200, description: 'Preferences updated successfully' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async updateUserNotificationPreferences(
    @User() user: IUser,
    @Body() data: UpdateBulkPreferencesDto,
  ) {
    try {
      await this.notificationService.updateUserNotificationPreferences(
        user.id,
        data,
      );
      return { success: true, message: 'Preferences updated successfully' };
    } catch (error) {
      this.logger.error('Error updating user notification preferences', error);
      throw error;
    }
  }

  /**
   * Get user notification history
   *
   * Supports filtering by:
   * - read status (read=true/false)
   * - date range (from_date, to_date)
   * - notification type (notification_type_id)
   * - module (module='event', 'opportunity', 'post', etc.)
   * - channels (channels='push', 'email', 'in_app')
   * - status (status='sent', 'failed')
   * - text search (search='keyword')
   */
  @Get('history')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'own' })
  @ApiOperation({
    summary: 'Get user notification history',
    description:
      'Retrieves notification history with support for filtering by module (event, opportunity, post, etc.), channels, read status, and more.',
  })
  @ApiResponse({ status: 200, description: 'History retrieved successfully' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getUserNotificationHistory(
    @User() user: IUser,
    @Query() query: NotificationHistoryQueryDto,
    @Request() req: any,
  ) {
    try {
      const history = await this.notificationService.getUserNotificationHistory(
        user.id,
        query,
      );

      // Check if this is a mobile client request
      const isMobileClient = req.clientType === AppClients.MOBILE;

      if (isMobileClient) {
        try {
          // For mobile clients, prepare an optimized query
          const mobileQuery = { ...query };

          // Default to push notifications only if no channels specified
          if (!query.channels || query.channels.length === 0) {
            this.logger.debug(
              'Mobile client request - defaulting to push notifications only',
            );
            mobileQuery.channels = ['push'];
          }

          // If duplicate channels are specified (like in the example request), normalize them
          if (
            Array.isArray(mobileQuery.channels) &&
            mobileQuery.channels.length > 1
          ) {
            // Remove duplicates
            mobileQuery.channels = [...new Set(mobileQuery.channels)];
          }

          // If module filter is specified, log it
          if (query.module) {
            this.logger.debug(
              `Mobile client request - filtering by module: ${query.module}`,
            );
            // Module filter is already part of the query
          }

          // Ensure reasonable pagination limits for mobile, default to 20 items for mobile
          if (!mobileQuery.limit || mobileQuery.limit > 50) {
            mobileQuery.limit = 20;
          }

          // Get filtered history with the optimized query
          const mobileHistory =
            await this.notificationService.getUserNotificationHistory(
              user.id,
              mobileQuery,
            );

          // Optimize response for mobile clients
          const optimizedData =
            MobileNotificationOptimizer.optimizePaginatedNotifications(
              mobileHistory,
            );

          this.logger.debug(
            `Mobile notification history optimized: ${optimizedData.data.length} items`,
          );

          return { success: true, data: optimizedData };
        } catch (error) {
          this.logger.error(
            'Error optimizing mobile notification history',
            error,
          );
          // Return a safe fallback response
          return {
            success: true,
            data: {
              data: [],
              total: 0,
              page: query.page || 1,
              limit: query.limit || 20,
              pages: 0,
            },
            message: 'An error occurred while retrieving notifications',
          };
        }
      }

      // Return full data for web clients
      return { success: true, data: history };
    } catch (error) {
      this.logger.error('Error getting user notification history', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  @Put('history/:id/read')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'own' })
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async markNotificationAsRead(
    @User() user: IUser,
    @Param('id', CustomParseUUIDPipe) notificationId: string,
  ) {
    try {
      await this.notificationService.markNotificationAsRead(
        user.id,
        notificationId,
      );
      return { success: true, message: 'Notification marked as read' };
    } catch (error) {
      this.logger.error('Error marking notification as read', error);
      throw error;
    }
  }
  @Get('history/:id')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'own' })
  @ApiOperation({ summary: 'Get individual notification log' })
  @ApiResponse({
    status: 200,
    description: 'Notification log retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Notification log not found' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getNotificationLogById(
    @User() user: IUser,
    @Param('id', CustomParseUUIDPipe) logId: string,
    @Request() req: any,
  ) {
    try {
      const log = await this.notificationService.getNotificationLogById(
        user.id,
        logId,
      );

      // Check if this is a mobile client request
      const isMobileClient = req.clientType === AppClients.MOBILE;

      if (isMobileClient && log) {
        // For mobile clients, we only want to return push notifications
        // unless the user explicitly requested a specific notification
        if (log.channels && !log.channels.includes('push')) {
          this.logger.debug(
            `Mobile client requested non-push notification: ${log.id}`,
          );
          // Still optimize but log the unusual request
        }

        // Optimize response for mobile clients
        const optimizedLog =
          MobileNotificationOptimizer.optimizeNotification(log);
        return { success: true, data: optimizedLog };
      }

      return { success: true, data: log };
    } catch (error) {
      this.logger.error('Error retrieving notification log', error);
      throw error;
    }
  }
  /**
   * Get scheduled notifications
   */
  @Get('scheduled')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'set-notification', action: 'read', possession: 'any' })
  @ApiOperation({ summary: 'Get scheduled notifications' })
  @ApiResponse({
    status: 200,
    description: 'Scheduled notifications retrieved successfully',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async getScheduledNotifications(
    @Query() query: ScheduledNotificationsQueryDto,
  ) {
    try {
      const scheduledNotifications =
        await this.notificationService.getScheduledNotifications(query);
      return { success: true, data: scheduledNotifications };
    } catch (error) {
      this.logger.error('Error getting scheduled notifications', error);
      throw error;
    }
  }

  /**
   * Cancel scheduled notification
   */
  @Delete('scheduled/:id')
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'set-notification',
    action: 'delete',
    possession: 'any',
  })
  @ApiOperation({ summary: 'Cancel scheduled notification' })
  @ApiResponse({
    status: 200,
    description: 'Notification cancelled successfully',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async cancelScheduledNotification(
    @Param('id', CustomParseUUIDPipe) notificationId: string,
  ) {
    try {
      await this.notificationService.cancelScheduledNotification(
        notificationId,
      );
      return { success: true, message: 'Notification cancelled successfully' };
    } catch (error) {
      this.logger.error('Error cancelling scheduled notification', error);
      throw error;
    }
  }
  /**
   * Update a scheduled notification
   *
   * Target audience can be filtered by:
   * - roles: Array of user roles (e.g., ['student', 'student_admin'])
   * - filters:
   *   - clubId: Filter by specific club ID
   *   - institutionIds: Array of institution IDs to target
   *   - countryIds: Array of country IDs to target
   */
  @Put('schedule/:id')
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'set-notification',
    action: 'update',
    possession: 'any',
  })
  @ApiOperation({
    summary: 'Update a scheduled notification',
    description:
      'Update a scheduled notification including target audience filters',
  })
  @ApiResponse({
    status: 200,
    description: 'Scheduled notification updated successfully',
  })
  @CLIENT_TYPE(AppClients.WEB)
  async updateScheduledNotification(
    @Param('id', CustomParseUUIDPipe) notificationId: string,
    @Body() data: ScheduleNotificationDto,
    @User() user: IUser,
  ) {
    try {
      const updatedNotification =
        await this.notificationService.updateScheduledNotification(
          notificationId,
          {
            notification_type_id: data.notification_type_id,
            title: data.title,
            body: data.body,
            data: data.data,
            target_audience: data.target_audience,
            scheduled_for: new Date(data.scheduled_for).toISOString(),
            channels: data.channels,
            updatedBy: user.id,
          },
        );
      return {
        success: true,
        message: 'Scheduled notification updated successfully',
        data: updatedNotification,
      };
    } catch (error) {
      this.logger.error('Error updating scheduled notification', error);
      throw error;
    }
  }
  /**
   * Register device token for push notifications
   */
  @Post('device-tokens')
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'push-notification',
    action: 'create',
    possession: 'any',
  })
  @ApiOperation({ summary: 'Register device token for push notifications' })
  @ApiResponse({
    status: 201,
    description: 'Device token registered successfully',
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async registerDeviceToken(
    @User() user: IUser,
    @Body() data: RegisterDeviceTokenDto,
  ) {
    try {
      await this.notificationService.registerDeviceToken(
        user.id,
        data.token,
        data.device_type,
      );
      return { success: true, message: 'Device token registered successfully' };
    } catch (error) {
      this.logger.error('Error registering device token', error);
      throw error;
    }
  }

  /**
   * Unregister device token
   */
  @Delete('device-tokens/:token')
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'push-notification',
    action: 'delete',
    possession: 'own',
  })
  @ApiOperation({ summary: 'Unregister device token' })
  @ApiResponse({
    status: 200,
    description: 'Device token unregistered successfully',
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async unregisterDeviceToken(
    @User() user: IUser,
    @Param('token') token: string,
  ) {
    try {
      await this.notificationService.unregisterDeviceToken(user.id, token);
      return {
        success: true,
        message: 'Device token unregistered successfully',
      };
    } catch (error) {
      this.logger.error('Error unregistering device token', error);
      throw error;
    }
  }

  /**
   * Get in-app notification stream
   */
  @Sse('stream/:eventType')
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'in-app-notification',
    action: 'read',
    possession: 'own',
  })
  @ApiOperation({ summary: 'Get in-app notification stream' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  getNotificationStream(
    @Param('eventType') eventType: string,
    @User() user: IUser,
  ): Observable<MessageEvent> {
    const stream =
      this.notificationService.getInAppNotificationStream(eventType);
    if (!stream) {
      this.logger.error(
        `Notification stream not found for event type: ${eventType}`,
      );
      throw new Error(
        `Notification stream not found for event type: ${eventType}`,
      );
    }

    return stream.pipe(
      map((data) => {
        // Only return notifications for this user
        if (data.userId === user.id) {
          return new MessageEvent('message', {
            data: JSON.stringify(data),
          });
        }
        // Return an empty message event instead of null
        return new MessageEvent('message', {
          data: JSON.stringify({}),
        });
      }),
    );
  }

  /**
   * Delete a notification
   */
  @Delete('history/:id')
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'push-notification',
    action: 'delete',
    possession: 'own',
  })
  @ApiOperation({ summary: 'Delete a notification' })
  @ApiResponse({
    status: 200,
    description: 'Notification deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async deleteNotification(
    @User() user: IUser,
    @Param('id', new CustomParseUUIDPipe()) notificationId: string,
  ) {
    try {
      this.logger.log(
        `Deleting notification ${notificationId} for user ${user.id}`,
      );

      // First, check if the notification exists and belongs to the user
      await this.notificationService.getNotificationLogById(
        user.id,
        notificationId,
      );

      // Delete the notification
      await this.notificationService.deleteNotificationLog(
        user.id,
        notificationId,
      );

      return {
        success: true,
        message: 'Notification deleted successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error deleting notification ${notificationId} for user ${user.id}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get user notification preferences optimized for mobile
   * Groups preferences by module and provides a simplified structure
   */
  @Get('mobile-preferences')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'any' })
  @ApiOperation({
    summary: 'Get user notification preferences for mobile',
    description:
      'Returns notification preferences optimized for mobile UI with module grouping. Groups all notification types by their respective modules (event, opportunity, quiz, etc.) for easier UI rendering.',
  })
  @ApiOkResponse({
    status: 200,
    description: 'Preferences retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            preferences: {
              type: 'object',
              additionalProperties: {
                type: 'array',
                items: {
                  $ref: '#/components/schemas/MobileNotificationPreferenceResponseDto',
                },
              },
              example: {
                event: [
                  {
                    id: 'pref-123',
                    notification_type_id: 'type-456',
                    enabled: true,
                    channels: { email: true, push: true, in_app: true },
                    module: 'event',
                    name: 'New Event Created',
                  },
                ],
                opportunity: [
                  {
                    id: 'pref-789',
                    notification_type_id: 'type-012',
                    enabled: false,
                    channels: { email: false, push: false, in_app: false },
                    module: 'opportunity',
                    name: 'New Opportunity Available',
                  },
                ],
              },
            },
            modules: {
              type: 'array',
              items: { type: 'string' },
              example: ['event', 'opportunity', 'quiz'],
            },
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    status: 500,
    description: 'Internal server error',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'An unexpected error occurred. Please try again later.',
        },
      },
    },
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async getMobileNotificationPreferences(@User() user: IUser) {
    try {
      const preferences =
        await this.notificationService.getMobileNotificationPreferences(
          user.id,
        );

      // Group preferences by module for easier UI rendering
      const groupedPreferences = preferences.reduce((acc, pref) => {
        const module = pref.module || 'general';
        if (!acc[module]) {
          acc[module] = [];
        }
        acc[module].push(pref);
        return acc;
      }, {});

      return {
        success: true,
        data: {
          preferences: groupedPreferences,
          modules: Object.keys(groupedPreferences),
        },
      };
    } catch (error) {
      this.logger.error('Error getting mobile notification preferences', error);
      throw error;
    }
  }

  /**
   * Update user notification preferences from mobile
   * Supports a master toggle and individual channel settings
   */
  @Put('mobile-preferences')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'own' })
  @ApiOperation({
    summary: 'Update user notification preferences for mobile',
    description:
      'Update multiple notification preferences at once. Supports both master toggle and individual channel settings for each notification type.',
  })
  @ApiBody({
    type: UpdateMobilePreferencesDto,
    description: 'Array of notification preferences to update',
    examples: {
      bulkUpdate: {
        summary: 'Update multiple preferences',
        value: [
          {
            notification_type_id: 'type-123',
            enabled: true,
            channels: { email: true, push: true, in_app: false },
          },
          {
            notification_type_id: 'type-456',
            enabled: false,
          },
        ],
      },
    },
  })
  @ApiOkResponse({
    status: 200,
    description: 'Preferences updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Preferences updated successfully',
        },
      },
    },
  })
  @ApiBadRequestResponse({
    status: 400,
    description: 'Invalid request body or notification type IDs',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Invalid notification type ID' },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    status: 500,
    description: 'Internal server error',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'An unexpected error occurred. Please try again later.',
        },
      },
    },
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async updateMobileNotificationPreferences(
    @User() user: IUser,
    @Body() data: UpdateMobilePreferencesDto,
  ) {
    try {
      await this.notificationService.updateMobileNotificationPreferences(
        user.id,
        data,
      );
      return { success: true, message: 'Preferences updated successfully' };
    } catch (error) {
      this.logger.error(
        'Error updating mobile notification preferences',
        error,
      );
      throw error;
    }
  }

  /**
   * Toggle all notifications for a specific module
   */
  @Put('mobile-preferences/module/:module')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'update', possession: 'own' })
  @ApiOperation({
    summary: 'Toggle all notifications for a module',
    description:
      'Enable or disable all notifications for a specific module (events, opportunities, etc.). This endpoint provides a master toggle for all notification types within a module.',
  })
  @ApiParam({
    name: 'module',
    description: 'The notification module to toggle',
    enum: [
      'event',
      'opportunity',
      'quiz',
      'post',
      'announcement',
      'raffle',
      'general',
    ],
    example: 'event',
  })
  @ApiBody({
    type: ModuleToggleDto,
    description: 'Toggle preferences for the module',
    examples: {
      enable: {
        summary: 'Enable all notifications for module',
        value: { enabled: true },
      },
      disable: {
        summary: 'Disable all notifications for module',
        value: { enabled: false },
      },
    },
  })
  @ApiOkResponse({
    status: 200,
    description: 'Module preferences updated successfully',
    type: ModuleToggleResponseDto,
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'event notifications enabled successfully',
        },
      },
    },
  })
  @ApiBadRequestResponse({
    status: 400,
    description: 'Invalid module name or request body',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'No notification types found for module: invalid_module',
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    status: 500,
    description: 'Internal server error',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'An unexpected error occurred. Please try again later.',
        },
      },
    },
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async toggleModuleNotifications(
    @User() user: IUser,
    @Param('module') module: string,
    @Body() data: ModuleToggleDto,
  ) {
    try {
      // Get all notification types for this module
      const notificationTypes =
        await this.notificationService.getNotificationTypesByModule(module);

      if (!notificationTypes.length) {
        return {
          success: false,
          message: `No notification types found for module: ${module}`,
        };
      }

      // Create preferences array with all types in this module
      const preferences = notificationTypes.map((type: any) => ({
        notification_type_id: type.id,
        enabled: data.enabled,
      }));

      // Update all preferences for this module
      await this.notificationService.updateMobileNotificationPreferences(
        user.id,
        preferences,
      );

      return {
        success: true,
        message: `${module} notifications ${data.enabled ? 'enabled' : 'disabled'} successfully`,
      };
    } catch (error) {
      this.logger.error(`Error toggling ${module} notifications`, error);
      throw error;
    }
  }

  /**
   * Test notification entity relationships
   */
  @Get('test-entity-relationships')
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'notification', action: 'read', possession: 'own' })
  @ApiOperation({
    summary: 'Test notification entity relationships',
    description:
      'Test endpoint to verify notification ID relationships work correctly',
  })
  @ApiResponse({
    status: 200,
    description: 'Test results retrieved successfully',
  })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async testEntityRelationships(@User() user: IUser) {
    try {
      // Get a few recent notifications for testing
      const notifications =
        await this.notificationService.getUserNotificationHistory(user.id, {
          page: 1,
          limit: 10,
          sort: 'created_at',
          order: 'desc',
          search: '',
          all: false,
        });

      const testResults = [];

      for (const notification of notifications.data) {
        const data = notification.data as any;
        // Note: We'll need to fetch the notification type separately to get the module
        const module = 'unknown'; // Placeholder - would need to join with notification_types table

        const result = {
          notificationId: notification.id,
          title: notification.title,
          module,
          dataStructure: {
            hasEventId: !!(data?.event_id || data?.eventId),
            hasPostId: !!(data?.post_id || data?.postId),
            hasOpportunityId: !!(data?.opportunity_id || data?.opportunityId),
            allKeys: Object.keys(data || {}),
          },
          expectedFields: this.getExpectedFields(module),
          isValid: this.validateDataStructure(data, module),
        };

        testResults.push(result);
      }

      return {
        success: true,
        message: 'Notification entity relationship test results',
        data: {
          totalTested: testResults.length,
          results: testResults,
          summary: this.generateTestSummary(testResults),
        },
      };
    } catch (error) {
      this.logger.error('Error testing entity relationships', error);
      throw error;
    }
  }

  private getExpectedFields(module: string): string[] {
    switch (module) {
      case 'event':
        return ['event_id', 'post_id'];
      case 'post':
        return ['post_id'];
      case 'opportunity':
        return ['opportunity_id', 'post_id'];
      default:
        return [];
    }
  }

  private validateDataStructure(data: any, module: string): boolean {
    if (!data) return false;

    switch (module) {
      case 'event':
        return (
          !!(data.event_id || data.eventId) && !!(data.post_id || data.postId)
        );
      case 'post':
        return !!(data.post_id || data.postId);
      case 'opportunity':
        return (
          !!(data.opportunity_id || data.opportunityId) &&
          !!(data.post_id || data.postId)
        );
      default:
        return true;
    }
  }

  private generateTestSummary(results: any[]): any {
    const summary = {
      total: results.length,
      valid: results.filter((r) => r.isValid).length,
      invalid: results.filter((r) => !r.isValid).length,
      byModule: {} as any,
    };

    results.forEach((result) => {
      const module = result.module || 'unknown';
      if (!summary.byModule[module]) {
        summary.byModule[module] = { total: 0, valid: 0, invalid: 0 };
      }
      summary.byModule[module].total++;
      if (result.isValid) {
        summary.byModule[module].valid++;
      } else {
        summary.byModule[module].invalid++;
      }
    });

    return summary;
  }
}
