import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  notification_types,
  scheduled_notifications,
} from '@/db/schema/notification_system';
import { and, eq, not, sql } from 'drizzle-orm';
import { users } from '@/db/schema/users';
import { NotificationStatus } from '../constants/notification.constant';
import {
  ScheduledNotificationsQueryDto,
  ScheduleNotificationDto,
} from './dto/enhanced-notification.dto';

@Injectable()
export class ScheduledNotificationService {
  private readonly logger = new Logger(ScheduledNotificationService.name);

  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * Schedule a notification for future delivery
   */
  async scheduleNotification(params: {
    notificationTypeId: string;
    title: string;
    body: string;
    data?: Record<string, any>;
    targetAudience: {
      roles?: string[];
      filters?: Record<string, any>;
    };
    scheduledFor: Date;
    channels: string[];
    createdBy: string;
  }): Promise<any> {
    const {
      notificationTypeId,
      title,
      body,
      data = {},
      targetAudience,
      scheduledFor,
      channels,
      createdBy,
    } = params;

    try {
      // Create scheduled notification
      const [scheduledNotification] = await this.drizzle.db
        .insert(scheduled_notifications)
        .values({
          notification_type_id: notificationTypeId,
          title,
          body,
          data,
          target_audience: targetAudience,
          scheduled_for: scheduledFor.toISOString(),
          channels,
          created_by: createdBy,
          status: 'pending',
        })
        .returning();

      return scheduledNotification;
    } catch (error: any) {
      this.logger.error(`Failed to schedule notification:`, error?.stack);
      throw error;
    }
  }

  /**
   * Get scheduled notifications with all filters applied
   */
  async getScheduledNotifications(
    params: ScheduledNotificationsQueryDto,
  ): Promise<any> {
    const {
      page,
      limit,
      sort,
      order,
      search,
      status,
      from_date,
      to_date,
      notification_type_id,
      channels,
      created_by,
    } = params;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [];

    // Status filter
    if (status) {
      whereConditions.push(eq(scheduled_notifications.status, status));
    }

    // Date range filters
    if (from_date) {
      whereConditions.push(
        sql`${scheduled_notifications.scheduled_for} >= ${from_date}`,
      );
    }

    if (to_date) {
      whereConditions.push(
        sql`${scheduled_notifications.scheduled_for} <= ${to_date}`,
      );
    }

    // Notification type filter
    if (notification_type_id) {
      whereConditions.push(
        eq(scheduled_notifications.notification_type_id, notification_type_id),
      );
    }

    // Text search in title and body
    if (search && search.trim() !== '') {
      whereConditions.push(
        sql`(${scheduled_notifications.title} ILIKE ${`%${search}%`} OR ${scheduled_notifications.body} ILIKE ${`%${search}%`})`,
      );
    }

    // Channels filter
    if (channels && channels.length > 0) {
      whereConditions.push(
        sql`${scheduled_notifications.channels} && ARRAY[${sql.join(channels, ', ')}]::text[]`,
      );
    }

    // Created by filter
    if (created_by) {
      whereConditions.push(eq(scheduled_notifications.created_by, created_by));
    }

    // Determine sort field and order
    const sortField = sort || 'scheduled_for';
    const sortOrder = order === 'desc' ? 'DESC' : 'ASC';

    // Get scheduled notifications
    const scheduledNotifications = await this.drizzle.db
      .select({
        notification: scheduled_notifications,
        notification_type: notification_types,
        created_by: users,
      })
      .from(scheduled_notifications)
      .leftJoin(
        notification_types,
        eq(scheduled_notifications.notification_type_id, notification_types.id),
      )
      .leftJoin(users, eq(scheduled_notifications.created_by, users.id))
      .where(whereConditions.length ? and(...whereConditions) : undefined)
      .orderBy(
        sql`${scheduled_notifications[sortField as keyof typeof scheduled_notifications]} ${sql.raw(sortOrder)}`,
      )
      .limit(limit)
      .offset(offset);

    // Get total count
    const countResult = await this.drizzle.db
      .select({ count: sql<number>`count(*)` })
      .from(scheduled_notifications)
      .where(whereConditions.length ? and(...whereConditions) : undefined);

    return {
      data: scheduledNotifications.map((n) => ({
        ...n.notification,
        notification_type: n.notification_type,
        created_by: n.created_by
          ? {
              id: n.created_by.id,
              email: n.created_by.email,
            }
          : null,
      })),
      total: Number(countResult[0]?.count || 0),
      page,
      limit,
      pages: Math.ceil(Number(countResult[0]?.count || 0) / limit),
    };
  }

  /**
   * Cancel scheduled notification
   */
  async cancelScheduledNotification(id: string): Promise<void> {
    try {
      // Fetch the scheduled notification
      const [notification] = await this.drizzle.db
        .select()
        .from(scheduled_notifications)
        .where(
          and(
            eq(scheduled_notifications.id, id),
            not(
              eq(scheduled_notifications.status, NotificationStatus.CANCELLED),
            ),
          ),
        );

      if (!notification) {
        throw new NotFoundException(
          `Scheduled notification with ID ${id} not found`,
        );
      }

      if (notification.status === NotificationStatus.SENT) {
        this.logger.warn(
          `Scheduled notification ${id} has already been sent and cannot be cancelled.`,
        );
        throw new NotFoundException(
          `Scheduled notification ${id} has already been sent and cannot be cancelled.`,
        );
      }

      await this.drizzle.db
        .update(scheduled_notifications)
        .set({ status: 'cancelled' })
        .where(eq(scheduled_notifications.id, id));
    } catch (error: any) {
      this.logger.error(
        `Failed to cancel scheduled notification ${id}:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Update scheduled notification
   */
  async updateScheduledNotification(
    notificationId: string,
    updateData: Partial<ScheduleNotificationDto & { updatedBy: string }>,
  ) {
    const [notification] = await this.drizzle.db
      .select()
      .from(scheduled_notifications)
      .where(eq(scheduled_notifications.id, notificationId));

    if (!notification) {
      throw new NotFoundException('Scheduled notification not found');
    }
    if (notification.status === NotificationStatus.SENT) {
      throw new NotFoundException(
        'This notification has already been sent and cannot be updated.',
      );
    }
    if (notification.status === 'cancelled') {
      throw new NotFoundException(
        'This notification has been cancelled and cannot be updated.',
      );
    }

    const [updatedNotification] = await this.drizzle.db
      .update(scheduled_notifications)
      .set({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .where(eq(scheduled_notifications.id, notificationId))
      .returning();

    return updatedNotification;
  }

  /**
   * Get due scheduled notifications
   */
  async getDueScheduledNotifications(): Promise<any[]> {
    const now = new Date();
    const dueNotifications = await this.drizzle.db
      .select()
      .from(scheduled_notifications)
      .where(
        and(
          eq(scheduled_notifications.status, 'pending'),
          sql`${scheduled_notifications.scheduled_for} <= ${now}`,
        ),
      );

    return dueNotifications;
  }

  /**
   * Update scheduled notification status
   */
  async updateScheduledNotificationStatus(
    id: string,
    status: string,
    data?: Record<string, any>,
  ): Promise<void> {
    await this.drizzle.db
      .update(scheduled_notifications)
      .set({
        status,
        ...(data ? { data } : {}),
      })
      .where(eq(scheduled_notifications.id, id));
  }
}
