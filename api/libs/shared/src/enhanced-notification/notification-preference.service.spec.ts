import { Test, TestingModule } from '@nestjs/testing';
import { NotificationPreferenceService } from './notification-preference.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CacheService } from '@app/shared/cache/cache.service';
import { CacheConfigService } from '@app/shared/cache/cache-config.service';

describe('NotificationPreferenceService', () => {
  let service: NotificationPreferenceService;

  const mockDrizzleService = {
    db: {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      transaction: jest.fn(),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
    },
  };

  const mockCacheService = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    invalidatePattern: jest.fn(),
    generateKey: jest.fn().mockImplementation((...args) => args.join(':')),
  };

  const mockCacheConfigService = {
    getTTL: jest.fn().mockReturnValue(300),
    isCacheEnabled: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationPreferenceService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: CacheConfigService,
          useValue: mockCacheConfigService,
        },
      ],
    }).compile();

    service = module.get<NotificationPreferenceService>(
      NotificationPreferenceService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Mobile Notification Preference Logic', () => {
    it('should correctly determine master toggle state based on channels', () => {
      // Test the core logic that determines if a preference is enabled
      // Master toggle is true if ANY channel is enabled

      const testCases = [
        {
          channels: { email: true, push: false, in_app: false },
          expected: true,
          description: 'email only enabled',
        },
        {
          channels: { email: false, push: true, in_app: false },
          expected: true,
          description: 'push only enabled',
        },
        {
          channels: { email: false, push: false, in_app: true },
          expected: true,
          description: 'in_app only enabled',
        },
        {
          channels: { email: false, push: false, in_app: false },
          expected: false,
          description: 'all channels disabled',
        },
        {
          channels: { email: true, push: true, in_app: true },
          expected: true,
          description: 'all channels enabled',
        },
        {
          channels: { email: true, push: false, in_app: true },
          expected: true,
          description: 'mixed channels enabled',
        },
      ];

      testCases.forEach((testCase) => {
        const { email, push, in_app } = testCase.channels;
        const enabled = email || push || in_app;
        expect(enabled).toBe(testCase.expected);
      });
    });

    it('should handle master toggle disabled logic correctly', () => {
      // When master toggle is disabled, all channels should be disabled
      const masterToggleEnabled = false; // This means disabled
      const userChannelPreferences = {
        email: true,
        push: true,
        in_app: true,
      };

      // Logic from the service: when enabled is false, all channels are disabled
      const finalChannels = masterToggleEnabled
        ? userChannelPreferences
        : {
            email: false,
            push: false,
            in_app: false,
          };

      expect(finalChannels.email).toBe(false);
      expect(finalChannels.push).toBe(false);
      expect(finalChannels.in_app).toBe(false);
    });

    it('should respect individual channel preferences when master toggle is enabled', () => {
      const masterToggleEnabled = true;
      const userChannelPreferences = {
        email: true,
        push: false,
        in_app: true,
      };

      // Logic from the service: when enabled is true, use individual channel preferences
      const finalChannels = masterToggleEnabled
        ? userChannelPreferences
        : {
            email: false,
            push: false,
            in_app: false,
          };

      expect(finalChannels.email).toBe(true);
      expect(finalChannels.push).toBe(false);
      expect(finalChannels.in_app).toBe(true);
    });
  });
});
