/**
 * Utility functions for optimizing notification data for mobile clients
 */
export class MobileNotificationOptimizer {
  /**
   * Transform a standard notification log to a mobile-optimized version
   * @param notification The original notification log
   * @returns A mobile-optimized notification log
   */
  static optimizeNotification(notification: any): any {
    if (!notification) return notification;

    try {
      // Check if the notification data is extremely large (potential performance issue)

      // Create a simplified notification with only essential fields
      const mobileNotification = {
        id: notification.id,
        title: notification.title || '',
        body: notification.body || '',
        created_at: notification.created_at,
        read_at: notification.read_at,
        channels: notification.channels,
        status: notification.status,
        // Include a simplified notification type
        notification_type: notification.notification_type
          ? {
              id: notification.notification_type.id,
              code: notification.notification_type.code,
              name: notification.notification_type.name,
              module: notification.notification_type.module,
            }
          : null,
        // Initialize an optimized data object
        data: {},
      };

      // If there's no data, return the simplified notification
      if (!notification.data) {
        return mobileNotification;
      }

      // Extract module type from notification_type if available
      const moduleType = notification.notification_type?.module;

      // Handle module-specific data optimization
      switch (moduleType) {
        case 'quiz':
          this._optimizeQuizData(notification, mobileNotification);
          break;
        case 'event':
          this._optimizeEventData(notification, mobileNotification);
          break;
        case 'opportunity':
          this._optimizeOpportunityData(notification, mobileNotification);
          break;
        case 'post':
          this._optimizePostData(notification, mobileNotification);
          break;
        case 'announcement':
          this._optimizeAnnouncementData(notification, mobileNotification);
          break;
        default:
          // For unknown module types, include minimal data
          this._optimizeGenericData(notification, mobileNotification);
          break;
      }

      // Final check for empty title/body - provide defaults if needed
      if (!mobileNotification.title || mobileNotification.title.trim() === '') {
        mobileNotification.title =
          notification.notification_type?.name || 'Notification';
      }

      if (!mobileNotification.body || mobileNotification.body.trim() === '') {
        mobileNotification.body = 'You have a new notification';
      }

      return mobileNotification;
    } catch (error) {
      // If optimization fails, return a minimal safe notification

      return {
        id: notification.id,
        title: notification.notification_type?.name || 'Notification',
        body: 'You have a new notification',
        created_at: notification.created_at,
        read_at: notification.read_at,
        status: notification.status,
        notification_type: notification.notification_type
          ? {
              id: notification.notification_type.id,
              code: notification.notification_type.code,
              name: notification.notification_type.name,
              module: notification.notification_type.module,
            }
          : null,
        data: {},
      };
    }
  }

  // Helper methods for module-specific optimizations
  private static _optimizeQuizData(
    notification: any,
    mobileNotification: any,
  ): void {
    if (
      notification.data?.quizzes &&
      Array.isArray(notification.data.quizzes)
    ) {
      // For mobile, we only need a small subset of quizzes with minimal fields
      // Limit to 5 most recent quizzes with only essential fields
      const simplifiedQuizzes = notification.data.quizzes
        .slice(0, 5)
        .map((quiz: any) => ({
          id: quiz.id,
          title: quiz.title,
          status: quiz.status,
          end_at: quiz.end_at,
          // Only include these fields if they exist
          ...(quiz.created_at && { created_at: quiz.created_at }),
          ...(quiz.due_date && { due_date: quiz.due_date }),
        }));

      // Replace the full quiz array with our simplified version
      mobileNotification.data.quizzes = simplifiedQuizzes;
      mobileNotification.data.total_quizzes = notification.data.quizzes.length;

      // Add a message if there are more quizzes than we're showing
      if (notification.data.quizzes.length > 5) {
        mobileNotification.data.additional_message = `${notification.data.quizzes.length - 5} more quizzes available`;
      }
    }

    // Include any quiz-specific identifiers
    if (notification.data?.quizId) {
      mobileNotification.data.quizId = notification.data.quizId;
    }

    // If title and body are empty, create meaningful defaults
    if (!mobileNotification.title || mobileNotification.title.trim() === '') {
      mobileNotification.title =
        notification.notification_type?.name || 'Quiz Notification';
    }

    if (!mobileNotification.body || mobileNotification.body.trim() === '') {
      const quizCount = notification.data?.quizzes?.length || 0;
      mobileNotification.body = `You have ${quizCount} active quizzes available`;
    }
  }

  private static _optimizeEventData(
    notification: any,
    mobileNotification: any,
  ): void {
    // Include essential event data
    if (notification.data?.eventId) {
      mobileNotification.data.eventId = notification.data.eventId;
    }

    if (notification.data?.eventTitle) {
      mobileNotification.data.eventTitle = notification.data.eventTitle;
    }

    if (notification.data?.eventDate) {
      mobileNotification.data.eventDate = notification.data.eventDate;
    }

    if (notification.data?.eventLocation) {
      mobileNotification.data.eventLocation = notification.data.eventLocation;
    }

    // Optimize participant lists if present
    if (
      notification.data?.participants &&
      Array.isArray(notification.data.participants)
    ) {
      // For participants, we only need counts, not the full list
      mobileNotification.data.total_participants =
        notification.data.participants.length;
    }
  }

  private static _optimizeOpportunityData(
    notification: any,
    mobileNotification: any,
  ): void {
    // Include essential opportunity data
    if (notification.data?.opportunityId) {
      mobileNotification.data.opportunityId = notification.data.opportunityId;
    }

    if (notification.data?.opportunityTitle) {
      mobileNotification.data.opportunityTitle =
        notification.data.opportunityTitle;
    }

    if (notification.data?.deadline) {
      mobileNotification.data.deadline = notification.data.deadline;
    }

    // Optimize applicant lists if present
    if (
      notification.data?.applicants &&
      Array.isArray(notification.data.applicants)
    ) {
      // For applicants, we only need counts, not the full list
      mobileNotification.data.total_applicants =
        notification.data.applicants.length;
    }
  }

  private static _optimizePostData(
    notification: any,
    mobileNotification: any,
  ): void {
    // Include essential post data
    if (notification.data?.postId) {
      mobileNotification.data.postId = notification.data.postId;
    }

    if (notification.data?.postTitle) {
      mobileNotification.data.postTitle = notification.data.postTitle;
    }

    // Optimize comment lists if present
    if (
      notification.data?.comments &&
      Array.isArray(notification.data.comments)
    ) {
      // For comments, we only need counts, not the full list
      mobileNotification.data.total_comments =
        notification.data.comments.length;
    }

    // Optimize engagement data
    if (
      notification.data?.engagements &&
      typeof notification.data.engagements === 'object'
    ) {
      // For mobile, we only need the counts, not the full user details
      mobileNotification.data.engagements = {
        likes_count: notification.data.engagements.likes?.length || 0,
        shares_count: notification.data.engagements.shares?.length || 0,
        comments_count: notification.data.engagements.comments?.length || 0,
      };
    }
  }

  private static _optimizeAnnouncementData(
    notification: any,
    mobileNotification: any,
  ): void {
    // For announcements, include minimal data
    if (notification.data?.announcementId) {
      mobileNotification.data.announcementId = notification.data.announcementId;
    }

    if (notification.data?.announcementTitle) {
      mobileNotification.data.announcementTitle =
        notification.data.announcementTitle;
    }

    // Any other essential announcement data
    if (notification.data?.priority) {
      mobileNotification.data.priority = notification.data.priority;
    }
  }

  private static _optimizeGenericData(
    notification: any,
    mobileNotification: any,
  ): void {
    // For generic notifications, include only essential identifiers
    // This prevents sending large data payloads for unknown notification types

    // Common identifiers that might be present
    const essentialKeys = [
      'id',
      'title',
      'type',
      'date',
      'deadline',
      'priority',
      'status',
      'location',
      'url',
      'imageUrl',
    ];

    essentialKeys.forEach((key) => {
      if (notification.data?.[key]) {
        mobileNotification.data[key] = notification.data[key];
      }
    });
  }

  /**
   * Optimize a paginated list of notifications for mobile clients
   * @param paginatedData The original paginated data
   * @returns Optimized paginated data for mobile clients
   */
  static optimizePaginatedNotifications(paginatedData: any): any {
    if (!paginatedData || !paginatedData.data) {
      return paginatedData;
    }

    // Process each notification in the paginated data
    const optimizedData = {
      ...paginatedData,
      data: paginatedData.data.map((notification: any) => {
        try {
          return this.optimizeNotification(notification);
        } catch (error) {
          // Silent error handling for production
          return {
            id: notification?.id,
            title: notification?.title || 'Notification',
            body: notification?.body || '',
            created_at: notification?.created_at,
            read_at: notification?.read_at,
            // Include minimal data to prevent app crashes
            data: {},
          }; // Return minimal data if optimization fails
        }
      }),
    };

    // Sort by created_at (newest first) for better mobile UX
    optimizedData.data.sort((a: any, b: any) => {
      const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
      const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
      return dateB - dateA;
    });

    return optimizedData;
  }

  /**
   * Optimize notification types for mobile clients
   * @param notificationTypes The original notification types
   * @returns Optimized notification types for mobile clients
   */
  static optimizeNotificationTypes(notificationTypes: any[]): any[] {
    if (!notificationTypes || !Array.isArray(notificationTypes))
      return notificationTypes;

    return notificationTypes.map((type) => {
      // For mobile, we only need essential fields
      return {
        id: type.id,
        code: type.code,
        name: type.name,
        module: type.module,
        default_channels: type.default_channels,
        // Include a simplified template if available
        template: type.template
          ? {
              id: type.template.id,
              name: type.template.name,
            }
          : null,
      };
    });
  }

  /**
   * Optimize notification preferences for mobile clients
   * @param preferences The original notification preferences
   * @returns Optimized notification preferences for mobile clients
   */
  static optimizeNotificationPreferences(preferences: any[]): any[] {
    if (!preferences || !Array.isArray(preferences)) return preferences;

    return preferences.map((pref) => {
      // For mobile, simplify the notification_type object
      return {
        id: pref.id,
        notification_type_id: pref.notification_type_id,
        email_enabled: pref.email_enabled,
        push_enabled: pref.push_enabled,
        in_app_enabled: pref.in_app_enabled,
        notification_type: pref.notification_type
          ? {
              id: pref.notification_type.id,
              name: pref.notification_type.name,
              code: pref.notification_type.code,
              module: pref.notification_type.module,
            }
          : null,
      };
    });
  }

  /**
   * Optimize notification preferences for mobile clients with module grouping
   * @param preferences The original notification preferences
   * @returns Optimized and grouped notification preferences for mobile clients
   */
  static optimizeGroupedNotificationPreferences(preferences: any[]): any {
    if (!preferences || !Array.isArray(preferences)) return {};

    // Group preferences by module
    const groupedPreferences = preferences.reduce((acc, pref) => {
      const module = pref.notification_type?.module || 'general';

      if (!acc[module]) {
        acc[module] = [];
      }

      // Check if any channel is enabled
      const email_enabled = pref.email_enabled;
      const push_enabled = pref.push_enabled;
      const in_app_enabled = pref.in_app_enabled;

      // Master enabled flag is true if any channel is enabled
      const enabled = email_enabled || push_enabled || in_app_enabled;

      acc[module].push({
        id: pref.id,
        notification_type_id: pref.notification_type_id,
        name: pref.notification_type?.name || 'Unknown',
        enabled,
        channels: {
          email: email_enabled,
          push: push_enabled,
          in_app: in_app_enabled,
        },
      });

      return acc;
    }, {});

    return {
      preferences: groupedPreferences,
      modules: Object.keys(groupedPreferences),
    };
  }
}
