import { ApiProperty } from '@nestjs/swagger';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';
import {
  notification_channels,
  notification_statuses,
} from '@/db/schema/notification_system';
import { querySchema } from '@/common/dto/query-params.dto';

// Base schemas
const notificationChannelSchema = z.enum(notification_channels);
const notificationStatusSchema = z.enum(notification_statuses);

// Notification module schema - based on seeded modules with fallback for additional client values
export const notificationModuleSchema = z
  .enum([
    'quiz',
    'post',
    'event',
    'opportunity',
    'announcement',
    'raffle',
    'general',
  ])
  .or(z.string().min(1));

// TypeScript type for notification modules
export type NotificationModule =
  | 'quiz'
  | 'post'
  | 'event'
  | 'opportunity'
  | 'announcement'
  | 'raffle'
  | 'general'
  | string;

// Template schemas
export const createTemplateSchema = z.object({
  name: z.string().min(3).max(100),
  description: z.string().min(3),
  title_template: z.string().min(3),
  body_template: z.string().min(3),
  email_subject_template: z.string().optional(),
  email_body_template: z.string().optional(),
});

export const updateTemplateSchema = createTemplateSchema.partial();

// Notification type schemas
export const createNotificationTypeSchema = z.object({
  code: z.string().min(3).max(50).optional(),
  name: z.string().min(3).max(100),
  description: z.string().min(3),
  module: notificationModuleSchema,
  template_id: z.string().uuid('Invalid template ID'),
  default_channels: z.array(notificationChannelSchema).default([]),
});

export const updateNotificationTypeSchema =
  createNotificationTypeSchema.partial();

// Notification preference schemas
export const updatePreferencesSchema = z.object({
  notification_type_id: z.string().uuid('Invalid notification type ID'),
  email_enabled: z.boolean().optional(),
  push_enabled: z.boolean().optional(),
  in_app_enabled: z.boolean().optional(),
});

export const updateBulkPreferencesSchema = z.array(updatePreferencesSchema);

// Mobile preference update schema
export const mobileNotificationPreferenceSchema = z.object({
  module: notificationModuleSchema.optional(),
  notification_type_id: z.string().uuid('Invalid notification type ID'),
  enabled: z.boolean(),
  channels: z
    .object({
      email: z.boolean().optional(),
      push: z.boolean().optional(),
      in_app: z.boolean().optional(),
    })
    .optional(),
});

export const updateMobilePreferencesSchema = z.array(
  mobileNotificationPreferenceSchema,
);

// Send notification schemas
export const targetAudienceSchema = z.object({
  roles: z.array(z.string()).optional(),
  filters: z
    .object({
      clubId: z.string().uuid('Invalid club ID').optional(),
      institutionIds: z
        .array(z.string().uuid('Invalid institution ID'))
        .optional(),
      countryIds: z.array(z.string().uuid('Invalid country ID')).optional(),
    })
    .optional(),
});

export const sendNotificationSchema = querySchema.extend({
  notification_type_id: z.string().uuid('Invalid notification type ID'),
  title: z.string().optional(),
  body: z.string().optional(),
  data: z.record(z.any()).optional(),
  channels: z.array(notificationChannelSchema).optional(),
  target_audience: targetAudienceSchema,
});

// Schedule notification schema
export const scheduleNotificationSchema = querySchema.extend({
  notification_type_id: z.string().uuid('Invalid notification type ID'),
  title: z.string(),
  body: z.string(),
  data: z.record(z.any()).optional(),
  channels: z.array(notificationChannelSchema),
  target_audience: targetAudienceSchema,
  scheduled_for: z.string().datetime(),
});

// Device token schema
export const registerDeviceTokenSchema = z.object({
  token: z.string(),
  device_type: z.enum(['ios', 'android', 'web']),
});

// Query params schemas
export const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
});
export const notificationHistoryQuerySchema = querySchema
  .omit({ sort: true, order: true })
  .extend({
    read: z
      .union([
        z.boolean(),
        z.enum(['true', 'false']).transform((val) => val === 'true'),
      ])
      .optional(),
    from_date: z.string().datetime().optional(),
    to_date: z.string().datetime().optional(),
    notification_type_id: z
      .string()
      .uuid('Invalid notification type ID')
      .optional(),
    channels: z
      .union([z.array(z.string()), z.string().transform((val) => [val])])
      .optional(),
    status: z.string().optional(),
    module: notificationModuleSchema.optional(),
    sort: z.string().optional().default('created_at'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
  });
export const scheduledNotificationsQuerySchema = querySchema.extend({
  status: notificationStatusSchema.optional(),
  from_date: z.string().datetime().optional(),
  to_date: z.string().datetime().optional(),
  notification_type_id: z
    .string()
    .uuid('Invalid notification type ID')
    .optional(),
  channels: z.array(z.string()).optional(),
  created_by: z.string().uuid('Invalid ID of the creator').optional(),
});

// Create DTO classes
export class CreateTemplateDto extends createZodDto(createTemplateSchema) {}
export class UpdateTemplateDto extends createZodDto(updateTemplateSchema) {}
export class CreateNotificationTypeDto extends createZodDto(
  createNotificationTypeSchema,
) {}
export class UpdateNotificationTypeDto extends createZodDto(
  updateNotificationTypeSchema,
) {}
export class UpdatePreferencesDto extends createZodDto(
  updatePreferencesSchema,
) {}
export class UpdateBulkPreferencesDto extends createZodDto(
  updateBulkPreferencesSchema,
) {}
export class SendNotificationDto extends createZodDto(sendNotificationSchema) {}
export class ScheduleNotificationDto extends createZodDto(
  scheduleNotificationSchema,
) {}
export class RegisterDeviceTokenDto extends createZodDto(
  registerDeviceTokenSchema,
) {}
export class NotificationHistoryQueryDto extends createZodDto(
  notificationHistoryQuerySchema,
) {}
export class ScheduledNotificationsQueryDto extends createZodDto(
  scheduledNotificationsQuerySchema,
) {}
export class UpdateMobilePreferencesDto extends createZodDto(
  updateMobilePreferencesSchema,
) {}

// Module toggle schema
export const moduleToggleSchema = z.object({
  enabled: z.boolean(),
});

export class ModuleToggleDto extends createZodDto(moduleToggleSchema) {
  @ApiProperty({
    description:
      'Whether to enable or disable all notifications for this module',
    example: true,
  })
  override enabled!: boolean;
}

// Response DTOs
export class NotificationTemplateResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  name!: string;

  @ApiProperty()
  description!: string;

  @ApiProperty()
  title_template!: string;

  @ApiProperty()
  body_template!: string;

  @ApiProperty({ required: false })
  email_subject_template?: string;

  @ApiProperty({ required: false })
  email_body_template?: string;

  @ApiProperty()
  created_at!: Date;

  @ApiProperty()
  updated_at!: Date;
}

export class NotificationTypeResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  code!: string;

  @ApiProperty()
  name!: string;

  @ApiProperty()
  description!: string;

  @ApiProperty({
    enum: [
      'quiz',
      'post',
      'event',
      'opportunity',
      'announcement',
      'raffle',
      'general',
    ],
    description: 'Notification module type',
  })
  module!: string;

  @ApiProperty()
  template_id!: string;

  @ApiProperty({ type: [String] })
  default_channels!: string[];

  @ApiProperty()
  created_at!: Date;

  @ApiProperty()
  updated_at!: Date;

  @ApiProperty({ type: NotificationTemplateResponseDto, required: false })
  template?: NotificationTemplateResponseDto;
}

export class NotificationPreferenceResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  notification_type_id!: string;

  @ApiProperty()
  email_enabled!: boolean;

  @ApiProperty()
  push_enabled!: boolean;

  @ApiProperty()
  in_app_enabled!: boolean;

  @ApiProperty({ type: NotificationTypeResponseDto, required: false })
  notification_type?: NotificationTypeResponseDto;
}

export class NotificationLogResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  notification_type_id!: string;

  @ApiProperty()
  title!: string;

  @ApiProperty()
  body!: string;

  @ApiProperty({ required: false })
  data?: Record<string, any>;

  @ApiProperty({ type: [String] })
  channels!: string[];

  @ApiProperty()
  status!: string;

  @ApiProperty({ required: false })
  error?: string;

  @ApiProperty()
  created_at!: Date;

  @ApiProperty({ required: false })
  read_at?: Date;

  @ApiProperty({ type: NotificationTypeResponseDto, required: false })
  notification_type?: NotificationTypeResponseDto;
}

export class ScheduledNotificationResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  notification_type_id!: string;

  @ApiProperty()
  title!: string;

  @ApiProperty()
  body!: string;

  @ApiProperty({ required: false })
  data?: Record<string, any>;

  @ApiProperty({ type: [String] })
  channels!: string[];

  @ApiProperty()
  target_audience!: Record<string, any>;

  @ApiProperty()
  scheduled_for!: Date;

  @ApiProperty()
  status!: string;

  @ApiProperty()
  created_by!: string;

  @ApiProperty()
  created_at!: Date;

  @ApiProperty()
  updated_at!: Date;

  @ApiProperty({ type: NotificationTypeResponseDto, required: false })
  notification_type?: NotificationTypeResponseDto;
}

export class PaginatedResponseDto<T> {
  @ApiProperty()
  data!: T[];

  @ApiProperty()
  total!: number;

  @ApiProperty()
  page!: number;

  @ApiProperty()
  limit!: number;

  @ApiProperty()
  pages!: number;
}

export class MobileNotificationPreferenceResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  notification_type_id!: string;

  @ApiProperty()
  enabled!: boolean;

  @ApiProperty()
  channels!: {
    email: boolean;
    push: boolean;
    in_app: boolean;
  };

  @ApiProperty({
    enum: [
      'quiz',
      'post',
      'event',
      'opportunity',
      'announcement',
      'raffle',
      'general',
    ],
    description: 'Notification module type',
  })
  module!: string;

  @ApiProperty()
  name!: string;
}

export class ModuleToggleResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Success or error message',
    example: 'event notifications enabled successfully',
  })
  message!: string;
}
