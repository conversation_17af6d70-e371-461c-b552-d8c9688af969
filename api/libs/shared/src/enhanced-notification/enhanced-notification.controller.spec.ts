import { Test, TestingModule } from '@nestjs/testing';
import { EnhancedNotificationController } from './enhanced-notification.controller';
import { EnhancedNotificationService } from './enhanced-notification.service';
import { NotificationHistoryService } from '../notification/notification-history.service';
import { NotFoundException } from '@nestjs/common';
import { ROLES_BUILDER_TOKEN } from 'nest-access-control';
import { RolesBuilder } from 'nest-access-control';
import { CacheService } from '@app/shared/cache/cache.service';
import type { User } from '@/db/schema/users';
import { notification_logs } from '@/db/schema/notification_system';

describe('EnhancedNotificationController', () => {
  let controller: EnhancedNotificationController;
  let service: EnhancedNotificationService;
  let historyService: NotificationHistoryService;
  let cacheService: CacheService;

  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    role: 'student',
    state: 'active',
    profile_pic_url: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    deleted: false,
    deleted_at: null,
  } as User;

  // Define a type for the notification log based on the schema
  type NotificationLog = typeof notification_logs.$inferSelect;

  const mockNotification: NotificationLog = {
    id: 'test-notification-id',
    notification_type_id: 'test-type-id',
    user_id: mockUser.id,
    title: 'Test Notification',
    body: 'This is a test notification',
    data: { module: 'test-module', type: 'test-type' },
    channels: ['push', 'email'],
    status: 'sent',
    error: null,
    created_at: new Date().toISOString(),
    read_at: null,
  };

  beforeEach(async () => {
    const mockCacheService = {
      del: jest.fn().mockResolvedValue(true),
      set: jest.fn().mockResolvedValue(true),
      get: jest.fn().mockResolvedValue(null),
      generateKey: jest.fn().mockReturnValue('mock-cache-key'),
      invalidatePattern: jest.fn().mockResolvedValue(1),
      incrementCacheVersion: jest.fn().mockResolvedValue(true),
    };

    const mockHistoryService = {
      getNotificationLogById: jest.fn(),
      deleteNotificationLog: jest.fn(),
      getUserNotificationHistory: jest.fn(),
      markNotificationAsRead: jest.fn(),
      markMultipleNotificationsAsRead: jest.fn(),
      markAllNotificationsAsRead: jest.fn(),
      getUnreadNotificationCount: jest.fn(),
      getNotificationsByType: jest.fn(),
      logNotification: jest.fn(),
      cleanupOldNotificationLogs: jest.fn(),
      invalidateNotificationHistoryCache: jest.fn(),
      invalidateNotificationLogCaches: jest.fn(),
    };

    const mockEnhancedNotificationService = {
      getNotificationLogById: jest.fn(),
      deleteNotificationLog: jest.fn(),
      getUserNotificationHistory: jest.fn(),
      markNotificationAsRead: jest.fn(),
      historyService: mockHistoryService,
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [EnhancedNotificationController],
      providers: [
        {
          provide: EnhancedNotificationService,
          useValue: mockEnhancedNotificationService,
        },
        {
          provide: NotificationHistoryService,
          useValue: mockHistoryService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: ROLES_BUILDER_TOKEN,
          useValue: new RolesBuilder(),
        },
      ],
    }).compile();

    controller = module.get<EnhancedNotificationController>(
      EnhancedNotificationController,
    );
    service = module.get<EnhancedNotificationService>(
      EnhancedNotificationService,
    );
    historyService = module.get<NotificationHistoryService>(
      NotificationHistoryService,
    );
    cacheService = module.get<CacheService>(CacheService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('deleteNotification', () => {
    it('should delete a notification successfully and handle cache invalidation', async () => {
      // Arrange
      const notificationId = 'test-notification-id';

      // Mock the service methods
      jest
        .spyOn(service, 'getNotificationLogById')
        .mockResolvedValue(mockNotification);

      // Mock the deleteNotificationLog method to simulate the actual implementation
      jest
        .spyOn(service, 'deleteNotificationLog')
        .mockImplementation(async (userId, logId) => {
          // This simulates what happens in the actual service
          await historyService.deleteNotificationLog(userId, logId);
          return undefined;
        });

      // Mock the history service method to handle comprehensive cache invalidation
      jest
        .spyOn(historyService, 'deleteNotificationLog')
        .mockImplementation(async (userId, logId) => {
          // Simulate comprehensive cache invalidation

          // 1. Invalidate specific notification log cache
          const logCacheKey = `notification:log:${userId}:${logId}`;
          await cacheService.del(logCacheKey);

          // 2. Invalidate notification history cache with timestamp-based approach
          const invalidationKey = `notification:history:${userId}:last_updated`;
          await cacheService.set(invalidationKey, Date.now().toString(), 86400); // 1 day TTL

          // 3. Invalidate unread count cache
          const unreadCountKey = `notification:unread_count:${userId}`;
          await cacheService.del(unreadCountKey);

          // 4. Try pattern-based invalidation for user-specific caches
          const userPattern = `*:notification:*:${userId}:*`;
          await cacheService.invalidatePattern(userPattern);

          // 5. Try to invalidate type-specific and module-specific caches
          const typeKey = `notification:history:${userId}:type:test-type-id`;
          await cacheService.del(typeKey);

          const typePattern = `*:notification:type:${userId}:test-type-id:*`;
          await cacheService.invalidatePattern(typePattern);

          const moduleKey = `notification:history:${userId}:module:test-module`;
          await cacheService.del(moduleKey);

          return undefined;
        });

      // Act
      const result = await controller.deleteNotification(
        mockUser,
        notificationId,
      );

      // Assert
      expect(service.getNotificationLogById).toHaveBeenCalledWith(
        mockUser.id,
        notificationId,
      );
      expect(service.deleteNotificationLog).toHaveBeenCalledWith(
        mockUser.id,
        notificationId,
      );
      expect(historyService.deleteNotificationLog).toHaveBeenCalledWith(
        mockUser.id,
        notificationId,
      );

      // Verify comprehensive cache invalidation operations

      // 1. Verify specific notification log cache invalidation
      expect(cacheService.del).toHaveBeenCalledWith(
        `notification:log:${mockUser.id}:${notificationId}`,
      );

      // 2. Verify timestamp-based cache invalidation
      expect(cacheService.set).toHaveBeenCalledWith(
        `notification:history:${mockUser.id}:last_updated`,
        expect.any(String),
        86400,
      );

      // 3. Verify unread count cache invalidation
      expect(cacheService.del).toHaveBeenCalledWith(
        `notification:unread_count:${mockUser.id}`,
      );

      // 4. Verify pattern-based invalidation
      expect(cacheService.invalidatePattern).toHaveBeenCalledWith(
        `*:notification:*:${mockUser.id}:*`,
      );

      // 5. Verify type-specific pattern invalidation
      expect(cacheService.invalidatePattern).toHaveBeenCalledWith(
        `*:notification:type:${mockUser.id}:test-type-id:*`,
      );

      // 6. Verify type-specific and module-specific cache invalidation
      expect(cacheService.del).toHaveBeenCalledWith(
        `notification:history:${mockUser.id}:type:test-type-id`,
      );

      expect(cacheService.del).toHaveBeenCalledWith(
        `notification:history:${mockUser.id}:module:test-module`,
      );
      expect(result).toEqual({
        success: true,
        message: 'Notification deleted successfully',
      });
    });

    it('should throw NotFoundException if notification does not exist', async () => {
      // Arrange
      const notificationId = 'non-existent-id';

      // Mock the service method to throw NotFoundException
      jest
        .spyOn(service, 'getNotificationLogById')
        .mockRejectedValue(
          new NotFoundException(
            `Notification log with ID ${notificationId} not found for user ${mockUser.id}`,
          ),
        );

      // Temporarily spy on the logger to suppress the expected error
      const loggerErrorSpy = jest
        .spyOn(controller['logger'], 'error')
        .mockImplementation(() => {});

      // Act & Assert
      try {
        await controller.deleteNotification(mockUser, notificationId);
        // If we get here, the test should fail
        fail(
          'Expected controller.deleteNotification to throw NotFoundException',
        );
      } catch (error: unknown) {
        // Verify the error is a NotFoundException
        expect(error).toBeInstanceOf(NotFoundException);
        if (error instanceof NotFoundException) {
          expect(error.message).toContain('not found for user');
        }

        // Verify the service methods were called correctly
        expect(service.getNotificationLogById).toHaveBeenCalledWith(
          mockUser.id,
          notificationId,
        );
        expect(service.deleteNotificationLog).not.toHaveBeenCalled();

        // Verify that the logger.error was called
        expect(loggerErrorSpy).toHaveBeenCalled();
      } finally {
        // Restore the original logger implementation
        loggerErrorSpy.mockRestore();
      }
    });

    it('should throw error if deleteNotificationLog fails', async () => {
      // Arrange
      const notificationId = 'test-notification-id';
      const testError = new Error('Failed to delete notification');

      jest
        .spyOn(service, 'getNotificationLogById')
        .mockResolvedValue(mockNotification);
      jest.spyOn(service, 'deleteNotificationLog').mockRejectedValue(testError);

      // Temporarily spy on the logger to suppress the expected error
      const loggerErrorSpy = jest
        .spyOn(controller['logger'], 'error')
        .mockImplementation(() => {});

      // Act & Assert
      try {
        await controller.deleteNotification(mockUser, notificationId);
        // If we get here, the test should fail
        fail('Expected controller.deleteNotification to throw an error');
      } catch (error: unknown) {
        // Verify the error is the expected error
        expect(error).toBe(testError);

        // Verify the service methods were called correctly
        expect(service.getNotificationLogById).toHaveBeenCalledWith(
          mockUser.id,
          notificationId,
        );
        expect(service.deleteNotificationLog).toHaveBeenCalledWith(
          mockUser.id,
          notificationId,
        );

        // Verify that the logger.error was called
        expect(loggerErrorSpy).toHaveBeenCalled();
      } finally {
        // Restore the original logger implementation
        loggerErrorSpy.mockRestore();
      }
    });
  });
});
