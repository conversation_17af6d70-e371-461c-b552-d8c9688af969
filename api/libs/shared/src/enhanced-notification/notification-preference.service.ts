import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  notification_preferences,
  notification_templates,
  notification_types,
} from '@/db/schema/notification_system';
import { and, eq } from 'drizzle-orm';
import { CacheService } from '@app/shared/cache/cache.service';
import { CacheConfigService } from '@app/shared/cache/cache-config.service';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { CacheInvalidate } from '@app/shared/cache/decorators/cache-invalidate.decorator';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';

@Injectable()
export class NotificationPreferenceService {
  private readonly logger = new Logger(NotificationPreferenceService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly cacheService: CacheService,
    private readonly cacheConfigService: CacheConfigService,
  ) {}

  /**
   * Get user notification preferences
   * @param userId - The ID of the user to get notification preferences for
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    ttl: CACHE_TTL.FIVE_MINUTES,
    keyGenerator: (args) => ['preferences', args[0]], // args[0] is userId
    condition: (args) => !!args[0], // Only cache if userId is provided
  })
  async getUserNotificationPreferences(userId: string): Promise<any[]> {
    // Get all notification types
    const notificationTypes = await this.drizzle.db
      .select()
      .from(notification_types);

    // Get user preferences
    const userPreferences = await this.drizzle.db
      .select()
      .from(notification_preferences)
      .where(eq(notification_preferences.user_id, userId));

    // Map notification types to preferences
    return notificationTypes.map((type) => {
      const preference = userPreferences.find(
        (p) => p.notification_type_id === type.id,
      );

      return {
        notification_type: type,
        id: preference?.id,
        notification_type_id: type.id,
        email_enabled: preference?.email_enabled ?? true,
        push_enabled: preference?.push_enabled ?? true,
        in_app_enabled: preference?.in_app_enabled ?? true,
      };
    });
  }

  /**
   * Get user notification preference for a specific notification type
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    ttl: CACHE_TTL.FIVE_MINUTES,
    keyGenerator: (args) => ['preference', args[0], args[1]], // args[0] is userId, args[1] is notificationTypeId
    condition: (args) => !!args[0] && !!args[1], // Only cache if both IDs are provided
  })
  async getUserNotificationPreference(
    userId: string,
    notificationTypeId: string,
  ): Promise<any> {
    const [preference] = await this.drizzle.db
      .select()
      .from(notification_preferences)
      .where(
        and(
          eq(notification_preferences.user_id, userId),
          eq(notification_preferences.notification_type_id, notificationTypeId),
        ),
      );

    if (preference) {
      return preference;
    }

    // Return default preferences if not found
    return {
      notification_type_id: notificationTypeId,
      email_enabled: true,
      push_enabled: true,
      in_app_enabled: true,
    };
  }

  /**
   * Update user notification preferences
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    keys: (args) => {
      const userId = args[0];
      const preferences = args[1];
      const keys = [`preferences:${userId}`];

      // Also invalidate individual preference caches
      if (preferences && Array.isArray(preferences)) {
        preferences.forEach((pref) => {
          if (pref.notification_type_id) {
            keys.push(`preference:${userId}:${pref.notification_type_id}`);
          }
        });
      }

      return keys;
    },
  })
  async updateUserNotificationPreferences(
    userId: string,
    preferences: {
      notification_type_id: string;
      email_enabled?: boolean;
      push_enabled?: boolean;
      in_app_enabled?: boolean;
    }[],
  ): Promise<void> {
    try {
      await this.drizzle.db.transaction(async (tx) => {
        for (const preference of preferences) {
          const { notification_type_id, ...settings } = preference;

          // Check if preference exists
          const [existingPreference] = await tx
            .select()
            .from(notification_preferences)
            .where(
              and(
                eq(notification_preferences.user_id, userId),
                eq(
                  notification_preferences.notification_type_id,
                  notification_type_id,
                ),
              ),
            );

          if (existingPreference) {
            // Update existing preference
            await tx
              .update(notification_preferences)
              .set(settings)
              .where(eq(notification_preferences.id, existingPreference.id));
          } else {
            // Create new preference
            await tx.insert(notification_preferences).values({
              user_id: userId,
              notification_type_id,
              ...settings,
            });
          }
        }
      });
    } catch (error: any) {
      this.logger.error(
        `Failed to update notification preferences for user ${userId}:`,
        error?.stack,
      );
      throw error;
    }
  }

  /**
   * Get user notification preferences optimized for mobile
   * Groups preferences by module and includes only essential data
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    ttl: CACHE_TTL.FIVE_MINUTES,
    keyGenerator: (args) => ['mobile-preferences', args[0]], // args[0] is userId
    condition: (args) => !!args[0], // Only cache if userId is provided
  })
  async getMobileNotificationPreferences(userId: string): Promise<any[]> {
    // Get all notification types
    const notificationTypes = await this.drizzle.db
      .select()
      .from(notification_types)
      .leftJoin(
        notification_templates,
        eq(notification_types.template_id, notification_templates.id),
      );

    // Get user preferences
    const userPreferences = await this.drizzle.db
      .select()
      .from(notification_preferences)
      .where(eq(notification_preferences.user_id, userId));

    // Map notification types to preferences with mobile-optimized format
    return notificationTypes.map((type) => {
      const preference = userPreferences.find(
        (p) => p.notification_type_id === type.notification_types.id,
      );

      // Check if any channel is enabled
      const email_enabled = preference?.email_enabled ?? true;
      const push_enabled = preference?.push_enabled ?? true;
      const in_app_enabled = preference?.in_app_enabled ?? true;

      // Master enabled flag is true if any channel is enabled
      const enabled = email_enabled || push_enabled || in_app_enabled;

      return {
        id: preference?.id,
        notification_type_id: type.notification_types.id,
        name: type.notification_types.name,
        module: type.notification_types.module,
        enabled,
        channels: {
          email: email_enabled,
          push: push_enabled,
          in_app: in_app_enabled,
        },
      };
    });
  }

  /**
   * Update user notification preferences from mobile format
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.NOTIFICATION,
    keys: (args) => {
      const userId = args[0];
      return [
        `preferences:${userId}`,
        `mobile-preferences:${userId}`,
        // Individual keys will be invalidated in the method
      ];
    },
  })
  async updateMobileNotificationPreferences(
    userId: string,
    preferences: Array<{
      notification_type_id: string;
      enabled: boolean;
      channels?: {
        email?: boolean;
        push?: boolean;
        in_app?: boolean;
      };
    }>,
  ): Promise<void> {
    try {
      const cacheKeys: string[] = [];

      await this.drizzle.db.transaction(async (tx) => {
        for (const pref of preferences) {
          const { notification_type_id, enabled, channels } = pref;

          // Add to cache invalidation keys
          cacheKeys.push(`preference:${userId}:${notification_type_id}`);

          // If master toggle is off, disable all channels
          const email_enabled = !enabled ? false : (channels?.email ?? true);
          const push_enabled = !enabled ? false : (channels?.push ?? true);
          const in_app_enabled = !enabled ? false : (channels?.in_app ?? true);

          // Check if preference exists
          const [existingPref] = await tx
            .select()
            .from(notification_preferences)
            .where(
              and(
                eq(notification_preferences.user_id, userId),
                eq(
                  notification_preferences.notification_type_id,
                  notification_type_id,
                ),
              ),
            );

          if (existingPref) {
            // Update existing preference
            await tx
              .update(notification_preferences)
              .set({
                email_enabled,
                push_enabled,
                in_app_enabled,
              })
              .where(eq(notification_preferences.id, existingPref.id));
          } else {
            // Create new preference
            await tx.insert(notification_preferences).values({
              user_id: userId,
              notification_type_id,
              email_enabled,
              push_enabled,
              in_app_enabled,
            });
          }
        }
      });

      // Manually invalidate individual preference caches
      for (const key of cacheKeys) {
        await this.cacheService.del(key);
      }
    } catch (error) {
      this.logger.error(
        `Failed to update mobile notification preferences for user ${userId}:`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }
}
