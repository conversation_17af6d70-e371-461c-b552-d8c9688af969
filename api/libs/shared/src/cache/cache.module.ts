import { Global, Module } from '@nestjs/common';
import { CacheService } from './cache.service';
import { CacheConfigService } from './cache-config.service';
import { CacheManagerService } from './cache-manager.service';
import { ScheduleModule } from '@nestjs/schedule';

/**
 * This module provides caching services.
 * Note: It depends on RedisModule which should be registered in the app module
 * using RedisModule.registerAsync() with proper configuration.
 */
@Global()
@Module({
  imports: [ScheduleModule.forRoot()],
  providers: [CacheService, CacheConfigService, CacheManagerService],
  exports: [CacheService, CacheConfigService, CacheManagerService],
})
export class CacheModule {}
