import { Logger } from '@nestjs/common';
import { CacheService } from '../cache.service';

export interface CacheInvalidateOptions {
  /**
   * Cache key prefix (usually entity type)
   */
  prefix: string;

  /**
   * Keys to invalidate (can be patterns with wildcards)
   */
  keys: string[] | ((args: any[], result: any) => string[]);

  /**
   * Whether to invalidate after method execution (true) or before (false)
   */
  afterExecution?: boolean;

  /**
   * Whether to invalidate only on successful execution (no exceptions)
   */
  onlyOnSuccess?: boolean;
}

/**
 * Decorator that invalidates cache entries when a method is called
 * @param options Cache invalidation options
 */
export function CacheInvalidate(options: CacheInvalidateOptions) {
  const logger = new Logger('CacheInvalidateDecorator');
  const { prefix, keys, afterExecution = true, onlyOnSuccess = true } = options;

  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // 'this' refers to the instance of the class where the decorator is applied
      const instance = this as any;

      // Get required services
      const cacheService: CacheService = instance.cacheService;

      if (!cacheService) {
        logger.warn(
          `CacheService not found in ${target.constructor.name}, skipping cache invalidation for ${propertyKey}`,
        );
        return originalMethod.apply(instance, args);
      }

      // Function to invalidate cache
      const invalidateCache = async (methodResult?: any) => {
        try {
          const keysToInvalidate =
            typeof keys === 'function' ? keys(args, methodResult) : keys;

          await Promise.all(
            keysToInvalidate
              .filter((key) => key !== null && key !== undefined)
              .map(async (key) => {
                // Ensure key is a string
                const keyStr = String(key);

                // If key contains wildcard, use pattern invalidation
                if (keyStr.includes('*')) {
                  const fullPattern = prefix
                    ? `*:${prefix}:*:${keyStr}`
                    : `*:${keyStr}`;
                  await cacheService.invalidatePattern(fullPattern);
                } else {
                  // Otherwise invalidate specific key
                  await cacheService.del(
                    cacheService.generateKey(keyStr, prefix),
                  );
                }
              }),
          );

          logger.debug(`Cache invalidated for ${prefix} after ${propertyKey}`);
        } catch (error) {
          logger.error(`Failed to invalidate cache for ${prefix}:`, error);
        }
      };

      // Invalidate before method execution if configured
      if (!afterExecution) {
        await invalidateCache();
      }

      try {
        // Execute the original method
        const result = await originalMethod.apply(instance, args);

        // Invalidate after method execution if configured
        if (afterExecution) {
          await invalidateCache(result);
        }

        return result;
      } catch (error) {
        // If method fails and we only invalidate on success, don't invalidate
        if (afterExecution && !onlyOnSuccess) {
          await invalidateCache();
        }
        throw error;
      }
    };

    return descriptor;
  };
}
