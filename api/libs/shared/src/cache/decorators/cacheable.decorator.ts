import { Logger } from '@nestjs/common';
import { CacheService } from '../cache.service';
import { CacheConfigService } from '../cache-config.service';
import { CacheStrategy } from '../../constants/cache.constant';

export interface CacheableOptions {
  /**
   * Cache key prefix (usually entity type)
   */
  prefix: string;

  /**
   * Time-to-live in seconds
   */
  ttl?: number;

  /**
   * Function to generate cache key from method arguments
   */
  keyGenerator: (args: any[]) => string | string[];

  /**
   * Caching strategy to use
   */
  strategy?: CacheStrategy;

  /**
   * Whether to hash complex objects in the key
   */
  hashObjects?: boolean;

  /**
   * Whether to disable caching in certain conditions
   */
  condition?: (args: any[]) => boolean;
}

/**
 * Decorator that caches method results
 * @param options Caching options
 */
export function Cacheable(options: CacheableOptions) {
  const logger = new Logger('CacheableDecorator');

  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // 'this' refers to the instance of the class where the decorator is applied
      const instance = this as any;

      // Get required services
      const cacheService: CacheService = instance.cacheService;
      const configService: CacheConfigService =
        instance.cacheConfigService ||
        (cacheService && cacheService['configService']);

      if (!cacheService) {
        logger.warn(
          `CacheService not found in ${target.constructor.name}, skipping cache for ${propertyKey}`,
        );
        return originalMethod.apply(instance, args);
      }

      // Check if caching is enabled globally
      if (configService && !configService.isCacheEnabled()) {
        return originalMethod.apply(instance, args);
      }

      // Check if caching should be skipped based on condition
      if (options.condition && !options.condition(args)) {
        return originalMethod.apply(instance, args);
      }

      // Generate cache key
      const key = options.keyGenerator(args);
      const cacheKey = cacheService.generateKey(key, options.prefix);

      // Try to get from cache first (Cache-Aside pattern)
      try {
        const cachedValue = await cacheService.get(cacheKey);
        if (cachedValue !== null) {
          return cachedValue;
        }
      } catch (error) {
        logger.warn(`Cache retrieval failed for ${cacheKey}:`, error);
      }

      // Execute the original method
      const result = await originalMethod.apply(instance, args);

      // Don't cache null or undefined results
      if (result === null || result === undefined) {
        return result;
      }

      // Store in cache
      try {
        await cacheService.set(cacheKey, result, options.ttl);
      } catch (error) {
        logger.warn(`Cache storage failed for ${cacheKey}:`, error);
      }

      return result;
    };

    return descriptor;
  };
}
