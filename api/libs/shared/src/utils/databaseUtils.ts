export function formattedTableName(tableName: string) {
  const camelCased = tableName.includes('_')
    ? tableName
        .split('_')
        .map((word, index) =>
          index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1),
        )
        .join('')
    : tableName;

  const words = camelCased.replace(/([A-Z])/g, '_$1').toLowerCase();

  const parts = words.split('_');

  const singular = parts
    .map((word) => {
      if (word.endsWith('ies')) return word.slice(0, -3) + 'y';
      if (word.endsWith('ves')) return word.slice(0, -3) + 'f';
      if (word.endsWith('oes') || word.endsWith('ses') || word.endsWith('xes'))
        return word.slice(0, -2);
      if (word.endsWith('s') && !word.endsWith('ss')) return word.slice(0, -1);
      return word;
    })
    .join('_');

  return singular;
}
