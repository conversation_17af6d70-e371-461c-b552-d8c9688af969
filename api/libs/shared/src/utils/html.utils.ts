/**
 * HTML utility functions for cleaning and processing HTML content
 */

/**
 * Decode HTML entities (e.g., &lt; to <, &gt; to >)
 * @param text - Text containing HTML entities
 * @returns Decoded text
 */
export function decodeHtmlEntities(text: string): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  const htmlEntities: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
    '&apos;': "'",
    '&nbsp;': ' ',
    '&copy;': '©',
    '&reg;': '®',
    '&trade;': '™',
  };

  return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
    return htmlEntities[entity] || entity;
  });
}

/**
 * Strip HTML tags from text
 * @param html - HTML content
 * @returns Plain text without HTML tags
 */
export function stripHtmlTags(html: string): string {
  if (!html || typeof html !== 'string') {
    return html;
  }

  return html
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Clean HTML content by decoding entities and removing HTML tags
 * This function handles both HTML-encoded content (e.g., &lt;p&gt;) and regular HTML tags
 * @param htmlContent - HTML content to clean
 * @returns Plain text content
 */
export function cleanHtmlContent(htmlContent: string): string {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return htmlContent;
  }

  // First decode HTML entities, then strip HTML tags
  const decoded = decodeHtmlEntities(htmlContent);
  const cleaned = stripHtmlTags(decoded);

  return cleaned;
}

/**
 * Clean notification body content specifically
 * This is a specialized function for cleaning notification body text
 * @param body - Notification body content
 * @returns Cleaned plain text body
 */
export function cleanNotificationBody(body: string): string {
  if (!body || typeof body !== 'string') {
    return body;
  }

  return cleanHtmlContent(body);
}
