import { createHash } from 'crypto';

/**
 * Returns a stable hash string for any object, array, or primitive.
 * Useful for deduplication or cache key generation.
 * @param obj The object or value to hash
 * @param algorithm Hash algorithm (default: 'md5')
 */
export function hashObject(obj: any, algorithm: string = 'md5'): string {
  const json = JSON.stringify(obj, Object.keys(obj).sort());
  return createHash(algorithm).update(json).digest('hex');
}
