import { CacheService } from './cache.service';
import { RedisService } from './redis.service';

describe('CacheService', () => {
  let cacheService: CacheService;
  let redisService: jest.Mocked<RedisService>;

  beforeEach(() => {
    redisService = {
      set: jest.fn(),
      get: jest.fn(),
      del: jest.fn(),
    } as unknown as jest.Mocked<RedisService>;
    cacheService = new CacheService(redisService);
  });

  it('should generate a key with a prefix', () => {
    const key = cacheService.generateKey('testKey', 'prefix');
    expect(key).toBe('prefix:testKey');
  });

  it('should generate hierarchical keys correctly', () => {
    const key = cacheService.generateKey(['service', 'entity', 'action']);
    expect(key).toBe('service:entity:action');
  });

  it('should set a value in the cache with TTL', async () => {
    redisService.set.mockResolvedValue('OK');
    const result = await cacheService.set('testKey', 'testValue', 3600);
    expect(result).toBe(true);
    expect(redisService.set).toHaveBeenCalledWith('testKey', 'testValue', 3600);
  });

  it('should get a value from the cache', async () => {
    redisService.get.mockResolvedValue('testValue');
    const result = await cacheService.get('testKey');
    expect(result).toBe('testValue');
    expect(redisService.get).toHaveBeenCalledWith('testKey');
  });

  it('should delete a key from the cache', async () => {
    redisService.del.mockResolvedValue(1);
    const result = await cacheService.del('testKey');
    expect(result).toBe(true);
    expect(redisService.del).toHaveBeenCalledWith('testKey');
  });

  it('should retry on Redis get failure', async () => {
    redisService.get
      .mockRejectedValueOnce(new Error('Temporary error'))
      .mockResolvedValueOnce('testValue');

    const result = await cacheService.get('testKey');
    expect(result).toBe('testValue');
    expect(redisService.get).toHaveBeenCalledTimes(2);
  });

  it('should calculate dynamic TTL correctly', () => {
    const shortTTL = cacheService['calculateDynamicTTL'](30);
    const longTTL = cacheService['calculateDynamicTTL'](3600);

    expect(shortTTL).toBe(3600);
    expect(longTTL).toBe(3600);
  });
});
