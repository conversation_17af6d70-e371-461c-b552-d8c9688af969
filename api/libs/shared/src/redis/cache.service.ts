import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';
import retry from 'async-retry';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(private readonly redisService: RedisService) {}

  /**
   * Generates a cache key with optional prefix
   * Supports both single keys and hierarchical keys (arrays)
   */
  generateKey(key: string | string[], prefix?: string): string {
    const baseKey = Array.isArray(key) ? key.join(':') : key;
    return prefix ? `${prefix}:${baseKey}` : baseKey;
  }

  /**
   * Generates a resource-specific cache key (alias for generateKey for backward compatibility)
   */
  generateResourceKey(id: string, prefix: string): string {
    return this.generateKey(id, prefix);
  }

  async get(key: string): Promise<any> {
    return await retry(
      async () => {
        try {
          return await this.redisService.get(key);
        } catch (error) {
          this.logger.error(`Failed to get cache for key ${key}`, error);
          throw error;
        }
      },
      { retries: 3 },
    );
  }

  async set(key: string, value: any, ttl?: number): Promise<boolean> {
    return await retry(
      async () => {
        try {
          const dynamicTTL = this.calculateDynamicTTL(ttl);
          await this.redisService.set(key, value, dynamicTTL);
          return true;
        } catch (error) {
          this.logger.error(`Failed to set cache for key ${key}`, error);
          throw error;
        }
      },
      { retries: 3 },
    );
  }

  async del(key: string): Promise<boolean> {
    return await retry(
      async () => {
        try {
          await this.redisService.del(key);
          return true;
        } catch (error) {
          this.logger.error(`Failed to delete cache for key ${key}`, error);
          throw error;
        }
      },
      { retries: 3 },
    );
  }

  async invalidateMany(keys: string[], prefix?: string): Promise<void> {
    try {
      const keysToDelete = [];
      const fullKeys = keys.map((key) => this.generateKey(key, prefix));

      for (const key of fullKeys) {
        if (key.includes(':*')) {
          const matchingKeys = await this.redisService.keys(key);
          keysToDelete.push(...matchingKeys);
        } else {
          keysToDelete.push(key);
        }
      }

      await Promise.all(keysToDelete.map((key) => this.del(key)));
    } catch (error) {
      this.logger.error('Failed to invalidate multiple keys', error);
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redisService.keys(pattern);
      if (keys.length > 0) {
        await Promise.all(keys.map((key: string) => this.del(key)));
      }
    } catch (error) {
      this.logger.error(
        `Failed to invalidate keys for pattern ${pattern}`,
        error,
      );
    }
  }

  private calculateDynamicTTL(ttl?: number): number {
    if (!ttl) return 60 * 60 * 24; // Default to 1 day
    // Example logic: Adjust TTL based on data type or access patterns
    return ttl > 60 * 60 ? ttl : 3600; // Default to 1 hour for short-lived data
  }
}
