import { Injectable, Logger, OnApplicationShutdown } from '@nestjs/common';
import { Redis, Cluster } from 'ioredis';

/**
 * Redis Connection Manager Service
 * Handles connection lifecycle, graceful degradation, and circuit breaker pattern
 * Provides centralized connection management for improved stability
 */
@Injectable()
export class RedisConnectionManagerService implements OnApplicationShutdown {
  private readonly logger = new Logger(RedisConnectionManagerService.name);
  private readonly connections = new Map<string, Redis | Cluster>();
  private readonly connectionStates = new Map<string, ConnectionState>();
  private readonly circuitBreakers = new Map<string, CircuitBreaker>();

  /**
   * Registers a Redis connection for lifecycle management
   */
  registerConnection(name: string, client: Redis | Cluster): void {
    this.connections.set(name, client);
    this.connectionStates.set(name, {
      isConnected: false,
      lastError: null,
      connectionAttempts: 0,
      lastSuccessfulConnection: null,
    });
    this.circuitBreakers.set(name, new CircuitBreaker(name, this.logger));

    this.setupConnectionMonitoring(name, client);
    this.logger.log(`Registered Redis connection: ${name}`);
  }

  /**
   * Gets a connection by name with circuit breaker protection
   */
  getConnection(name: string): Redis | Cluster | null {
    const client = this.connections.get(name);
    const circuitBreaker = this.circuitBreakers.get(name);

    if (!client || !circuitBreaker) {
      this.logger.warn(`Connection not found: ${name}`);
      return null;
    }

    if (circuitBreaker.isOpen()) {
      this.logger.warn(`Circuit breaker is open for connection: ${name}`);
      return null;
    }

    return client;
  }

  /**
   * Checks if a connection is healthy
   */
  async isConnectionHealthy(name: string): Promise<boolean> {
    const client = this.getConnection(name);
    if (!client) return false;

    const circuitBreaker = this.circuitBreakers.get(name)!;

    try {
      await client.ping();
      circuitBreaker.recordSuccess();
      this.updateConnectionState(name, { isConnected: true, lastError: null });
      return true;
    } catch (error) {
      circuitBreaker.recordFailure();
      this.updateConnectionState(name, {
        isConnected: false,
        lastError: error as Error,
      });
      return false;
    }
  }

  /**
   * Gets connection state for monitoring
   */
  getConnectionState(name: string): ConnectionState | null {
    return this.connectionStates.get(name) || null;
  }

  /**
   * Gets all connection states for health checks
   */
  getAllConnectionStates(): Map<string, ConnectionState> {
    return new Map(this.connectionStates);
  }

  /**
   * Gracefully closes all connections
   */
  async onApplicationShutdown(signal?: string): Promise<void> {
    this.logger.log(`Shutting down Redis connections (signal: ${signal})`);

    const shutdownPromises = Array.from(this.connections.entries()).map(
      async ([name, client]) => {
        try {
          this.logger.log(`Closing Redis connection: ${name}`);
          await client.quit();
          this.logger.log(`Successfully closed Redis connection: ${name}`);
        } catch (error) {
          this.logger.warn(`Error closing Redis connection ${name}:`, error);
        }
      },
    );

    await Promise.allSettled(shutdownPromises);
    this.connections.clear();
    this.connectionStates.clear();
    this.circuitBreakers.clear();
  }

  private setupConnectionMonitoring(
    name: string,
    client: Redis | Cluster,
  ): void {
    client.on('connect', () => {
      this.updateConnectionState(name, {
        isConnected: true,
        connectionAttempts: 0,
        lastSuccessfulConnection: new Date(),
      });
      this.logger.log(`Redis connection established: ${name}`);
    });

    client.on('ready', () => {
      this.updateConnectionState(name, { isConnected: true });
      this.logger.log(`Redis client ready: ${name}`);
    });

    client.on('error', (error: Error) => {
      this.updateConnectionState(name, {
        isConnected: false,
        lastError: error,
        connectionAttempts:
          (this.connectionStates.get(name)?.connectionAttempts || 0) + 1,
      });

      const circuitBreaker = this.circuitBreakers.get(name);
      if (circuitBreaker) {
        circuitBreaker.recordFailure();
      }

      // Log error with context but avoid spam
      const state = this.connectionStates.get(name);
      if (
        state &&
        (state.connectionAttempts <= 3 || state.connectionAttempts % 5 === 0)
      ) {
        this.logger.error(`Redis connection error (${name}):`, {
          message: error.message,
          attempts: state.connectionAttempts,
          timestamp: new Date().toISOString(),
        });
      }
    });

    client.on('end', () => {
      this.updateConnectionState(name, { isConnected: false });
      this.logger.warn(`Redis connection closed: ${name}`);
    });

    client.on('reconnecting', () => {
      this.logger.log(`Redis reconnecting: ${name}`);
    });

    // Cluster-specific events
    if (client instanceof Cluster) {
      client.on('node error', (error: Error, node: any) => {
        this.logger.warn(`Redis cluster node error (${name}):`, {
          message: error.message,
          host: node.options?.host,
          port: node.options?.port,
        });
      });
    }
  }

  private updateConnectionState(
    name: string,
    updates: Partial<ConnectionState>,
  ): void {
    const currentState = this.connectionStates.get(name);
    if (currentState) {
      this.connectionStates.set(name, { ...currentState, ...updates });
    }
  }
}

/**
 * Connection state interface
 */
interface ConnectionState {
  isConnected: boolean;
  lastError: Error | null;
  connectionAttempts: number;
  lastSuccessfulConnection: Date | null;
}

/**
 * Circuit Breaker implementation for Redis connections
 */
class CircuitBreaker {
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failureCount = 0;
  private lastFailureTime: Date | null = null;
  private readonly failureThreshold = 5;
  private readonly timeoutMs = 60000; // 1 minute
  private readonly halfOpenMaxCalls = 3;
  private halfOpenCalls = 0;

  constructor(
    private readonly name: string,
    private readonly logger: Logger,
  ) {}

  isOpen(): boolean {
    if (this.state === 'OPEN') {
      if (this.shouldAttemptReset()) {
        this.state = 'HALF_OPEN';
        this.halfOpenCalls = 0;
        this.logger.log(`Circuit breaker half-open: ${this.name}`);
      }
    }
    return this.state === 'OPEN';
  }

  recordSuccess(): void {
    this.failureCount = 0;
    this.lastFailureTime = null;

    if (this.state === 'HALF_OPEN') {
      this.halfOpenCalls++;
      if (this.halfOpenCalls >= this.halfOpenMaxCalls) {
        this.state = 'CLOSED';
        this.logger.log(`Circuit breaker closed: ${this.name}`);
      }
    }
  }

  recordFailure(): void {
    this.failureCount++;
    this.lastFailureTime = new Date();

    if (this.state === 'HALF_OPEN') {
      this.state = 'OPEN';
      this.logger.warn(`Circuit breaker opened from half-open: ${this.name}`);
    } else if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
      this.logger.warn(`Circuit breaker opened: ${this.name}`);
    }
  }

  private shouldAttemptReset(): boolean {
    return (
      this.lastFailureTime !== null &&
      Date.now() - this.lastFailureTime.getTime() > this.timeoutMs
    );
  }
}
