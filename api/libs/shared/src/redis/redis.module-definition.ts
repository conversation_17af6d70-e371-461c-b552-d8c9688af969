import { ConfigurableModuleBuilder } from '@nestjs/common';
import { RedisOptions as IoRedisOptions } from 'ioredis';
import { ConnectionOptions } from 'tls';

/**
 * Redis connection options for standalone and cluster setups
 */
export interface RedisStandaloneOptions {
  host: string;
  port: number;
  password?: string;
  username?: string;
  db?: number;
  tls?: ConnectionOptions | undefined;
}

export interface RedisClusterNode {
  host: string;
  port: number;
}

export interface RedisClusterOptions {
  nodes: RedisClusterNode[];
  redisOptions?: Partial<IoRedisOptions>;
}

/**
 * Unified timeout configuration for consistent Redis behavior
 */
export interface RedisTimeoutConfig {
  commandTimeout: number;
  connectionTimeout: number;
  disconnectTimeout: number;
}

/**
 * Unified connection configuration for consistent Redis behavior across all modules
 */
export interface RedisConnectionConfig {
  maxRetriesPerRequest: number | null;
  enableOfflineQueue: boolean;
  enableReadyCheck: boolean;
  lazyConnect: boolean;
  family: number;
  keepAlive: boolean;
  keepAliveInitialDelay: number;
  retryDelayOnFailover: number;
  enableAutoPipelining: boolean;
}

export interface RedisOptions {
  mode: 'single' | 'cluster' | 'auto';
  single?: RedisStandaloneOptions;
  cluster?: RedisClusterOptions;
  healthCheckInterval?: number;
  fallbackToSingle?: boolean;
  timeouts?: RedisTimeoutConfig;
  connectionConfig?: RedisConnectionConfig;
}

export const {
  ConfigurableModuleClass: ConfigurableRedisModule,
  MODULE_OPTIONS_TOKEN: REDIS_OPTIONS,
} = new ConfigurableModuleBuilder<RedisOptions>({
  moduleName: 'Redis',
})
  .setClassMethodName('forRoot')
  .setFactoryMethodName('forRootAsync')
  .build();
