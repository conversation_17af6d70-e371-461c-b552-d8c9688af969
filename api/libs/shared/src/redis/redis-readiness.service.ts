import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { RedisConnectionManagerService } from './redis-connection-manager.service';

/**
 * Redis Readiness Service
 * Ensures Redis connections are properly initialized before dependent services start
 * Resolves race conditions in application bootstrap sequence
 */
@Injectable()
export class RedisReadinessService implements OnApplicationBootstrap {
  private readonly logger = new Logger(RedisReadinessService.name);
  private isReady = false;
  private readyPromise: Promise<void> | null = null;
  private readonly maxWaitTime = 30000; // 30 seconds max wait
  private readonly checkInterval = 1000; // Check every 1 second

  constructor(
    private readonly connectionManager: RedisConnectionManagerService,
  ) {}

  /**
   * Waits for Redis to be ready during application bootstrap
   */
  async onApplicationBootstrap(): Promise<void> {
    this.logger.log('Starting Redis readiness check...');

    try {
      await this.waitForRedisReady();
      this.isReady = true;
      this.logger.log('Redis is ready - application can proceed');
    } catch (error) {
      this.logger.warn(
        'Redis readiness check failed, but application will continue',
        error,
      );
      // Don't throw error to prevent application startup failure
      // Redis will reconnect automatically when available
    }
  }

  /**
   * Checks if Redis is ready for use
   */
  isRedisReady(): boolean {
    return this.isReady;
  }

  /**
   * Waits for Redis to become ready (for use by other services)
   */
  async waitForReady(timeoutMs: number = 10000): Promise<boolean> {
    if (this.isReady) {
      return true;
    }

    if (this.readyPromise) {
      try {
        await Promise.race([
          this.readyPromise,
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error('Timeout waiting for Redis')),
              timeoutMs,
            ),
          ),
        ]);
        return this.isReady;
      } catch {
        return false;
      }
    }

    // If no ready promise exists, do a quick check
    try {
      const isHealthy =
        await this.connectionManager.isConnectionHealthy('main');
      if (isHealthy) {
        this.isReady = true;
        return true;
      }
    } catch {
      // Ignore errors
    }

    return false;
  }

  /**
   * Gets Redis connection status for monitoring
   */
  getConnectionStatus(): RedisConnectionStatus {
    const connectionStates = this.connectionManager.getAllConnectionStates();
    const mainState = connectionStates.get('main');

    return {
      isReady: this.isReady,
      connections: Array.from(connectionStates.entries()).map(
        ([name, state]) => ({
          name,
          isConnected: state.isConnected,
          connectionAttempts: state.connectionAttempts,
          lastError: state.lastError?.message || null,
          lastSuccessfulConnection: state.lastSuccessfulConnection,
        }),
      ),
      mainConnection: mainState
        ? {
            isConnected: mainState.isConnected,
            connectionAttempts: mainState.connectionAttempts,
            lastError: mainState.lastError?.message || null,
            lastSuccessfulConnection: mainState.lastSuccessfulConnection,
          }
        : null,
    };
  }

  private async waitForRedisReady(): Promise<void> {
    if (this.readyPromise) {
      return this.readyPromise;
    }

    this.readyPromise = this.performReadinessCheck();
    return this.readyPromise;
  }

  private async performReadinessCheck(): Promise<void> {
    const startTime = Date.now();
    let attempts = 0;

    while (Date.now() - startTime < this.maxWaitTime) {
      attempts++;

      try {
        // Check if main Redis connection is healthy
        const isMainHealthy =
          await this.connectionManager.isConnectionHealthy('main');

        if (isMainHealthy) {
          this.logger.log(`Redis readiness check passed (attempt ${attempts})`);
          return;
        }

        // Log progress every 5 attempts to avoid spam
        if (attempts % 5 === 0) {
          this.logger.debug(
            `Redis readiness check attempt ${attempts} - not ready yet`,
          );
        }
      } catch (error) {
        // Log errors less frequently
        if (attempts <= 3 || attempts % 10 === 0) {
          this.logger.debug(
            `Redis readiness check attempt ${attempts} failed:`,
            error,
          );
        }
      }

      // Wait before next attempt
      await new Promise((resolve) => setTimeout(resolve, this.checkInterval));
    }

    throw new Error(
      `Redis readiness check timed out after ${this.maxWaitTime}ms (${attempts} attempts)`,
    );
  }
}

/**
 * Redis connection status interface for monitoring
 */
export interface RedisConnectionStatus {
  isReady: boolean;
  connections: Array<{
    name: string;
    isConnected: boolean;
    connectionAttempts: number;
    lastError: string | null;
    lastSuccessfulConnection: Date | null;
  }>;
  mainConnection: {
    isConnected: boolean;
    connectionAttempts: number;
    lastError: string | null;
    lastSuccessfulConnection: Date | null;
  } | null;
}

/**
 * Decorator to ensure Redis is ready before method execution
 */
export function RequireRedisReady(timeoutMs: number = 5000) {
  return function (
    _target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const instance = this as any;
      const readinessService: RedisReadinessService =
        instance.redisReadinessService;

      if (readinessService) {
        const isReady = await readinessService.waitForReady(timeoutMs);
        if (!isReady) {
          throw new Error(`Redis is not ready for operation: ${propertyKey}`);
        }
      }

      return originalMethod.apply(instance, args);
    };

    return descriptor;
  };
}

/**
 * Helper function to create a Redis readiness check
 */
export async function waitForRedis(
  readinessService: RedisReadinessService,
  timeoutMs: number = 10000,
): Promise<boolean> {
  try {
    return await readinessService.waitForReady(timeoutMs);
  } catch {
    return false;
  }
}
