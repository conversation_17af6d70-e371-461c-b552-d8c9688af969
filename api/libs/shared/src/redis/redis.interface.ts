import { Redis, Cluster } from 'ioredis';

export interface IRedisService {
  readonly client: Redis | Cluster;
  readonly isConnected: boolean;

  /**
   * Tests the Redis connection during application bootstrap
   */
  onApplicationBootstrap(): Promise<void>;

  /**
   * Checks if Redis connection is healthy
   * @returns boolean indicating if <PERSON><PERSON> is responding
   */
  isHealthy(): Promise<boolean>;

  /**
   * Sets a value in Redis
   * @param key - The key to set
   * @param value - The value to store
   * @param ttl - Optional TTL in seconds
   * @returns Promise resolving to 'OK' on success
   */
  set(key: string, value: any, ttl?: number): Promise<'OK'>;

  /**
   * Gets a value from Redis
   * @param key - The key to retrieve
   * @returns Promise resolving to the stored value or null if not found
   */
  get(key: string): Promise<any>;

  /**
   * Deletes a key from Redis
   * @param key - The key to delete
   * @returns Promise resolving to number of keys deleted
   */
  del(key: string): Promise<number>;

  /**
   * Clears all keys from Redis
   * @returns Promise resolving to 'OK' on success
   */
  clearAll(): Promise<'OK'>;

  /**
   * Retrieves keys matching a pattern
   * @param pattern - The pattern to match keys
   * @returns Promise resolving to an array of matching keys
   */
  keys(pattern: string): Promise<string[]>;
}
