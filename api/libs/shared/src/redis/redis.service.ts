import {
  Inject,
  Injectable,
  Logger,
  OnApplicationBootstrap,
  OnApplicationShutdown,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis, { Cluster } from 'ioredis';
import type { RedisOptions } from './redis.module-definition';
import { IRedisService } from './redis.interface';
import { RedisConnectionManagerService } from './redis-connection-manager.service';

@Injectable()
/**
 * Redis service for managing Redis connections and operations
 * Provides resilient connection handling with automatic recovery from:
 * - Command timeouts
 * - READONLY errors (connected to replica)
 * - Failover events (master -> replica transitions)
 * - Connection failures
 */
export class RedisService
  implements IRedisService, OnApplicationBootstrap, OnApplicationShutdown
{
  public readonly client: Redis | Cluster;
  private readonly logger = new Logger(RedisService.name);

  public isConnected = false;
  private connectionAttempts = 0;
  private readonly maxConnectionAttempts = 10;
  private readonly isProduction = process.env.NODE_ENV === 'production';
  private readonly logLevel =
    process.env.REDIS_LOG_LEVEL || (this.isProduction ? 'warn' : 'info');
  private reconnectTimer?: NodeJS.Timeout;
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(
    @Inject('REDIS_OPTIONS') private readonly options: RedisOptions,
    private readonly connectionManager: RedisConnectionManagerService,
    private readonly configService: ConfigService,
  ) {
    this.client = this.initializeClient();
    this.setupEventListeners();
    // Register this connection with the manager
    this.connectionManager.registerConnection('main', this.client);
  }

  /**
   * Determines if a log level should be output based on current configuration
   */
  private shouldLog(level: 'error' | 'warn' | 'info' | 'debug'): boolean {
    const levels = ['error', 'warn', 'info', 'debug'];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const requestedLevelIndex = levels.indexOf(level);
    return requestedLevelIndex <= currentLevelIndex;
  }

  /**
   * Creates retry strategy for Redis connections
   */
  private createRetryStrategy(
    maxAttempts?: number,
  ): (times: number) => number | null {
    const maxRetries = maxAttempts || 10;
    return (times: number) => {
      if (times > maxRetries) {
        return null; // Stop retrying
      }
      // Exponential backoff with jitter: min 100ms, max 3s
      const delay = Math.min(times * 100 + Math.random() * 100, 3000);
      return delay;
    };
  }

  /**
   * Creates reconnect error handler for Redis connections
   */
  private createReconnectOnErrorHandler(): (error: Error) => boolean {
    return (error: Error) => {
      const targetErrors = [
        'READONLY',
        'ECONNRESET',
        'ENOTFOUND',
        'ENETUNREACH',
        'ETIMEDOUT',
        'ECONNREFUSED',
        'Connection is closed',
      ];
      return targetErrors.some((targetError) =>
        error.message.includes(targetError),
      );
    };
  }

  /**
   * Creates BullMQ-specific connection configuration
   */
  createBullMQConnection(): any {
    const isDevelopment = process.env.NODE_ENV === 'development';

    const baseConfig: any = {
      host:
        this.options.single?.host ||
        this.configService.get('REDIS_HOST', 'localhost'),
      port:
        this.options.single?.port ||
        parseInt(this.configService.get('REDIS_PORT', '6379'), 10),
      password:
        this.options.single?.password ||
        this.configService.get('REDIS_PASSWORD', undefined),
      username:
        this.options.single?.username ||
        this.configService.get('REDIS_USERNAME', undefined),
      db:
        this.options.single?.db ||
        parseInt(this.configService.get('REDIS_DB', '0'), 10),

      commandTimeout: isDevelopment ? 20000 : 15000,
      connectTimeout: isDevelopment ? 15000 : 10000,
      disconnectTimeout: isDevelopment ? 3000 : 5000,
      maxRetriesPerRequest: null,
      enableOfflineQueue: true,
      enableReadyCheck: false,
      lazyConnect: false,
      family: 4,
      keepAlive: true,
      keepAliveInitialDelay: isDevelopment ? 5000 : 10000,
      retryDelayOnFailover: 100,
      enableAutoPipelining: false,
      retryStrategy: this.createRetryStrategy(),
      reconnectOnError: this.createReconnectOnErrorHandler(),
    };

    // Add TLS configuration if needed
    if (this.options.single?.tls) {
      baseConfig.tls = this.options.single.tls;
    }

    return baseConfig;
  }

  /**
   * Creates BullMQ configuration with unified settings
   */
  createBullMQConfig(): any {
    const isDevelopment = process.env.NODE_ENV === 'development';

    return {
      connection: this.createBullMQConnection(),
      defaultJobOptions: {
        attempts: isDevelopment ? 3 : 5,
        backoff: {
          type: 'exponential' as const,
          delay: isDevelopment ? 2000 : 1000,
        },
        removeOnComplete: {
          age: 7 * 24 * 60 * 60,
          count: isDevelopment ? 100 : 1000,
        },
        removeOnFail: {
          age: 14 * 24 * 60 * 60,
          count: isDevelopment ? 50 : 500,
        },
      },
      prefix: 'bull',
    };
  }

  private initializeClient(): Redis | Cluster {
    if (this.shouldLog('debug')) {
      this.logger.debug(
        `Initializing Redis client in ${this.options.mode} mode`,
      );
    }

    if (!this.isProduction && this.shouldLog('debug')) {
      this.logger.debug(
        `Redis configuration: ${JSON.stringify({
          ...this.options,
          single: this.options.single
            ? { ...this.options.single, password: '***' }
            : undefined,
        })}`,
      );
    }
    switch (this.options.mode) {
      case 'single':
        if (!this.options.single) {
          throw new Error(
            'Standalone configuration is missing for single mode',
          );
        }
        return new Redis(this.createEnhancedSingleConfig(this.options.single));
      case 'cluster':
        if (!this.options.cluster) {
          throw new Error('Cluster configuration is missing for cluster mode');
        }
        return new Cluster(
          this.options.cluster.nodes,
          this.createEnhancedClusterConfig(this.options.cluster.redisOptions),
        );
      case 'auto':
        return this.initializeAutoMode();
      default:
        throw new Error(`Unsupported Redis mode: ${this.options.mode}`);
    }
  }

  private createEnhancedSingleConfig(config: any): any {
    const timeouts = this.options.timeouts!;
    const connectionConfig = this.options.connectionConfig!;

    return {
      ...config,
      commandTimeout: timeouts.commandTimeout,
      connectTimeout: timeouts.connectionTimeout,
      disconnectTimeout: timeouts.disconnectTimeout,
      maxRetriesPerRequest: connectionConfig.maxRetriesPerRequest,
      enableOfflineQueue: connectionConfig.enableOfflineQueue,
      enableReadyCheck: connectionConfig.enableReadyCheck,
      lazyConnect: connectionConfig.lazyConnect,
      family: connectionConfig.family,
      keepAlive: connectionConfig.keepAlive,
      keepAliveInitialDelay: connectionConfig.keepAliveInitialDelay,
      retryDelayOnFailover: connectionConfig.retryDelayOnFailover,
      enableAutoPipelining: connectionConfig.enableAutoPipelining,
      showFriendlyErrorStack: !this.isProduction,
      retryStrategy: this.createRetryStrategy(this.maxConnectionAttempts),
      reconnectOnError: this.createReconnectOnErrorHandler(),
    };
  }

  private createEnhancedClusterConfig(config: any = {}): any {
    const timeouts = this.options.timeouts!;
    const connectionConfig = this.options.connectionConfig!;

    return {
      commandTimeout: timeouts.commandTimeout,
      connectTimeout: timeouts.connectionTimeout,
      disconnectTimeout: timeouts.disconnectTimeout,
      maxRetriesPerRequest: connectionConfig.maxRetriesPerRequest,
      enableOfflineQueue: connectionConfig.enableOfflineQueue,
      enableReadyCheck: connectionConfig.enableReadyCheck,
      showFriendlyErrorStack: !this.isProduction,
      redisOptions: {
        ...config,
        retryStrategy: this.createRetryStrategy(),
      },
    };
  }

  private initializeAutoMode(): Redis | Cluster {
    if (this.shouldLog('info')) {
      this.logger.log('Initializing Redis in auto mode');
    }

    if (this.options.cluster?.nodes?.length) {
      try {
        if (this.shouldLog('debug')) {
          this.logger.debug('Attempting cluster connection in auto mode');
        }
        return new Cluster(
          this.options.cluster.nodes,
          this.createEnhancedClusterConfig(this.options.cluster.redisOptions),
        );
      } catch (error) {
        this.logger.warn(
          'Cluster connection failed in auto mode, falling back to single instance',
          error,
        );
      }
    }

    if (this.options.single) {
      if (this.shouldLog('debug')) {
        this.logger.debug('Using single instance fallback in auto mode');
      }
      return new Redis(this.createEnhancedSingleConfig(this.options.single));
    }

    throw new Error('No valid Redis configuration found for auto mode');
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = undefined;
    }
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }
  }

  private setupEventListeners(): void {
    this.clearTimers();

    this.client.on('connect', () => {
      this.isConnected = true;
      this.connectionAttempts = 0;
      if (this.shouldLog('info')) {
        this.logger.log('Redis connection established');
      }
    });

    this.client.on('ready', () => {
      this.isConnected = true;
      if (this.shouldLog('info')) {
        this.logger.log('Redis client ready for commands');
      }
    });

    this.client.on('error', (error: Error) => {
      this.isConnected = false;
      this.connectionAttempts++;

      const errorContext = {
        message: error.message,
        name: error.name,
        attempt: this.connectionAttempts,
        maxAttempts: this.maxConnectionAttempts,
        redisMode: this.options.mode,
        timestamp: new Date().toISOString(),
      };

      if (error.message.includes('READONLY')) {
        if (this.shouldLog('warn')) {
          this.logger.warn(
            'Redis READONLY error detected - attempting reconnection',
            errorContext,
          );
        }
      } else if (error.message.includes('ECONNREFUSED')) {
        if (this.connectionAttempts % 5 === 1 || !this.isProduction) {
          if (this.shouldLog('error')) {
            this.logger.error('Redis connection refused', errorContext);
          }
        }
      } else if (error.message.includes('ENOTFOUND')) {
        if (this.shouldLog('error')) {
          this.logger.error('Redis host not found', errorContext);
        }
      } else if (error.message.includes('ETIMEDOUT')) {
        if (this.connectionAttempts % 3 === 1 && this.shouldLog('warn')) {
          this.logger.warn('Redis connection timeout', errorContext);
        }
      } else {
        if (!this.isProduction || this.connectionAttempts <= 3) {
          if (this.shouldLog('error')) {
            this.logger.error('Redis connection error', errorContext);
          }
        }
      }
    });

    this.client.on('end', () => {
      this.isConnected = false;
      if (this.shouldLog('warn')) {
        this.logger.warn('Redis connection closed');
      }
    });

    this.client.on('reconnecting', (delay: number) => {
      if (this.shouldLog('info')) {
        this.logger.log(
          `Redis reconnecting in ${delay}ms... (attempt ${this.connectionAttempts + 1}/${this.maxConnectionAttempts})`,
        );
      }
    });

    if (this.client instanceof Cluster) {
      this.client.on('node error', (error: Error, node: any) => {
        if (this.shouldLog('warn')) {
          this.logger.warn(
            `Redis cluster node error on ${node.options.host}:${node.options.port}`,
            {
              message: error.message,
              host: node.options.host,
              port: node.options.port,
              timestamp: new Date().toISOString(),
            },
          );
        }
      });

      this.client.on('+node', (node: any) => {
        if (this.shouldLog('info')) {
          this.logger.log(
            `Redis cluster node added: ${node.options.host}:${node.options.port}`,
          );
        }
      });

      this.client.on('-node', (node: any) => {
        if (this.shouldLog('warn')) {
          this.logger.warn(
            `Redis cluster node removed: ${node.options.host}:${node.options.port}`,
          );
        }
      });
    }
  }

  async onApplicationBootstrap(): Promise<void> {
    try {
      await this.waitForConnection(2);
      if (this.shouldLog('info')) {
        this.logger.log('Redis service initialized successfully');
      }
    } catch (error) {
      if (this.shouldLog('warn')) {
        this.logger.warn(
          'Redis not immediately available during bootstrap - will retry in background',
          {
            error: error instanceof Error ? error.message : 'Unknown error',
            mode: this.options.mode,
          },
        );
      }
    }
  }

  async onApplicationShutdown(signal?: string): Promise<void> {
    if (this.shouldLog('info')) {
      this.logger.log(`Redis service shutting down (signal: ${signal})`);
    }

    this.clearTimers();

    try {
      if (this.client) {
        await this.client.quit();
      }
    } catch (error) {
      if (this.shouldLog('warn')) {
        this.logger.warn('Error during Redis shutdown', error);
      }
    }
  }

  private async waitForConnection(maxAttempts: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const pingPromise = this.client.ping();
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Ping timeout')), 3000);
        });

        await Promise.race([pingPromise, timeoutPromise]);

        if (this.shouldLog('info')) {
          this.logger.log(
            `Redis connection is healthy (attempt ${attempt}/${maxAttempts})`,
          );
        }
        return;
      } catch (error: any) {
        if (attempt <= 2 || !this.isProduction) {
          if (this.shouldLog('warn')) {
            this.logger.warn(
              `Redis connection attempt ${attempt}/${maxAttempts} failed: ${error.message}`,
            );
          }
        }

        if (attempt === maxAttempts) {
          if (this.shouldLog('warn')) {
            this.logger.warn(
              'Redis connection not available during application bootstrap - will retry in background',
              {
                attempts: maxAttempts,
                lastError: error.message,
                redisMode: this.options.mode,
                timestamp: new Date().toISOString(),
              },
            );
          }
          return;
        }

        const delay = Math.min(attempt * 1000, 3000);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  async isHealthy(): Promise<boolean> {
    return await this.connectionManager.isConnectionHealthy('main');
  }

  async set(key: string, value: any, ttl?: number): Promise<'OK'> {
    const serializedValue = JSON.stringify(value);
    if (ttl) {
      return this.client.set(key, serializedValue, 'EX', ttl);
    }
    return this.client.set(key, serializedValue);
  }

  async get(key: string): Promise<any> {
    const value = await this.client.get(key);
    if (!value) return null;

    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }

  async del(key: string): Promise<number> {
    return this.client.del(key);
  }

  async clearAll(): Promise<'OK'> {
    if (this.options.mode === 'cluster') {
      throw new Error('clearAll is not supported in cluster mode');
    }
    return (this.client as Redis).flushdb();
  }

  async keys(pattern: string): Promise<string[]> {
    if (this.options.mode === 'cluster') {
      throw new Error('keys is not supported in cluster mode');
    }
    return (this.client as Redis).keys(pattern);
  }
}
