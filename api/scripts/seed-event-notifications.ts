import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { SeedModule } from '../src/seed/seed.module';
import { EventNotificationSeedService } from '../src/seed/event-notification-seed.service';

async function bootstrap() {
  const app = await NestFactory.create(SeedModule);
  const seedService = app.get(EventNotificationSeedService);
  const logger = new Logger('EventNotificationSeed');

  try {
    logger.log('Starting event notification seed...');
    await seedService.seed();
    logger.log('Event notification seed completed successfully');
  } catch (error) {
    logger.error('Failed to seed event notifications', error);
    process.exit(1);
  }

  await app.close();
  process.exit(0);
}

bootstrap();
