import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { SeedModule } from '../src/seed/seed.module';
import { NotificationSeedService } from '../src/seed/notification-seed.service';

async function bootstrap() {
  const app = await NestFactory.create(SeedModule);
  const seedService = app.get(NotificationSeedService);
  const logger = new Logger('NotificationSeed');

  try {
    logger.log('Starting notification seed...');
    await seedService.seed();
    logger.log('Notification seed completed successfully');
  } catch (error) {
    logger.error('Failed to seed notifications', error);
    process.exit(1);
  }

  await app.close();
  process.exit(0);
}

bootstrap();
