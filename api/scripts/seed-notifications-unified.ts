import { NestFactory } from '@nestjs/core';
import { SeedModule } from '../src/seed/seed.module';
import { NotificationSeedService } from '../src/seed/notification-seed.service';
import { EventNotificationSeedService } from '../src/seed/event-notification-seed.service';
import { SelectionNotificationSeedService } from '../src/seed/selection-notification-seed.service';

/**
 * Unified notification seeding script
 *
 * Usage:
 * - npm run seed:notifications:unified                  # Seeds all notification types
 * - npm run seed:notifications:unified -- --standard    # Seeds only standard notifications
 * - npm run seed:notifications:unified -- --event       # Seeds only event notifications
 * - npm run seed:notifications:unified -- --selection   # Seeds only selection notifications
 * - npm run seed:notifications:unified -- --standard --event  # Seeds standard and event notifications
 */
async function bootstrap() {
  // Parse command line arguments
  const args = process.argv.slice(2);
  const seedTypes = {
    standard: args.includes('--standard') || args.length === 0,
    event: args.includes('--event') || args.length === 0,
    selection: args.includes('--selection') || args.length === 0,
  };

  console.warn('Starting unified notification seed...');
  console.warn(
    `Seed types: ${Object.entries(seedTypes)
      .filter(([, value]) => value)
      .map(([key]) => key)
      .join(', ')}`,
  );

  const app = await NestFactory.create(SeedModule);

  try {
    // Seed standard notifications if requested
    if (seedTypes.standard) {
      const notificationSeedService = app.get(NotificationSeedService);
      console.warn('Seeding standard notifications...');
      await notificationSeedService.seed();
      console.warn('Standard notifications seeded successfully');
    }

    // Seed event notifications if requested
    if (seedTypes.event) {
      const eventNotificationSeedService = app.get(
        EventNotificationSeedService,
      );
      console.warn('Seeding event notifications...');
      await eventNotificationSeedService.seed();
      console.warn('Event notifications seeded successfully');
    }

    // Seed selection notifications if requested
    if (seedTypes.selection) {
      const selectionNotificationSeedService = app.get(
        SelectionNotificationSeedService,
      );
      console.warn('Seeding selection notifications...');
      await selectionNotificationSeedService.seed();
      console.warn('Selection notifications seeded successfully');
    }

    console.warn('Notification seeding completed successfully');
  } catch (error) {
    console.error('Failed to seed notifications', error);
    process.exit(1);
  }

  await app.close();
  process.exit(0);
}

bootstrap();
