#! /bin/bash
imageName=$1
newTag=$2
port=$3
containerName=$4
destination=$5


sed -i "s|image: 345594579871.dkr.ecr.eu-west-1.amazonaws.com/touching-lives-backend|image: 345594579871.dkr.ecr.eu-west-1.amazonaws.com/$imageName:$newTag|g" "docker-compose.yml"
sed -i "s|3003:3003|$port:3003|g" "docker-compose.yml"
sed -i "s|container_name: touching-lives-backend|container_name: $containerName|g" "docker-compose.yml"
sed -i "s|destination: /home/<USER>/touching-lives|destination: /home/<USER>/$destination|g" "appspec.yml"

cat <<EOF > scripts/start.sh 
#!/bin/bash
cd /home/<USER>/$destination/ && \
docker compose -f docker-compose.yml up --remove-orphans -d
 
 
EOF
cat <<EOF > scripts/after-install.sh 
 #!/bin/bash
aws ecr get-login-password --region 'eu-west-1' | docker login --username AWS --password-stdin 345594579871.dkr.ecr.eu-west-1.amazonaws.com/$imageName
 
EOF







