import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { SeedModule } from '../src/seed/seed.module';
import { SelectionNotificationSeedService } from '../src/seed/selection-notification-seed.service';

async function bootstrap() {
  const app = await NestFactory.create(SeedModule);
  const selectionNotificationSeedService = app.get(
    SelectionNotificationSeedService,
  );
  const logger = new Logger('SelectionNotificationSeed');

  try {
    logger.log('Seeding selection notification templates and types...');
    await selectionNotificationSeedService.seed();
    logger.log(
      'Selection notification templates and types seeded successfully',
    );
  } catch (error) {
    logger.error(
      'Error seeding selection notification templates and types:',
      error,
    );
    process.exit(1);
  }

  await app.close();
  process.exit(0);
}

bootstrap();
