# Notification Seeding System

This document explains how to use the notification seeding system to populate the database with notification templates and types.

## Unified Notification Seeding

The unified notification seeding script (`seed-notifications-unified.ts`) allows you to seed different types of notification templates and types in a single command.

### Usage

```bash
# Seed all notification types (standard, event, and selection)
npm run seed:notifications:unified

# Seed only standard notifications
npm run seed:notifications:unified -- --standard

# Seed only event notifications
npm run seed:notifications:unified -- --event

# Seed only selection notifications
npm run seed:notifications:unified -- --selection

# Seed multiple specific types
npm run seed:notifications:unified -- --standard --event
```

### Convenience Scripts

For common seeding operations, the following convenience scripts are available:

```bash
# Seed all notification types (equivalent to seed:notifications:unified)
npm run seed:all-notifications

# Seed only standard notifications
npm run seed:notifications

# Seed only event notifications
npm run seed:event-notifications

# Seed only selection notifications
npm run seed:selection-notifications
```

## Notification Types

The system currently supports the following notification types:

1. **Standard Notifications**
   - Quiz notifications
   - Post notifications
   - Announcement notifications
   - New opportunity notifications
   - New event notifications

2. **Event Notifications**
   - Event reminder notifications

3. **Selection Notifications**
   - Raffle winner notifications
   - Opportunity selection notifications

## Adding New Notification Types

To add a new notification type:

1. Create a new seed service in `src/seed/` that implements the seeding logic
2. Add the service to the `SeedModule` providers and exports
3. Update the unified seeding script to include the new notification type
4. Add a convenience script to `package.json` if needed

## Legacy Scripts

The following legacy scripts are maintained for backward compatibility:

- `seed:notifications:direct` - Seeds notifications directly using a standalone script
