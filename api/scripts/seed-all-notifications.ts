import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { SeedModule } from '../src/seed/seed.module';
import { NotificationSeedService } from '../src/seed/notification-seed.service';
import { EventNotificationSeedService } from '../src/seed/event-notification-seed.service';

async function bootstrap() {
  const app = await NestFactory.create(SeedModule);
  const notificationSeedService = app.get(NotificationSeedService);
  const eventNotificationSeedService = app.get(EventNotificationSeedService);
  const logger = new Logger('AllNotificationsSeed');

  try {
    logger.log('Starting all notifications seed...');

    // First seed standard notifications
    logger.log('Seeding standard notifications...');
    await notificationSeedService.seed();
    logger.log('Standard notifications seeded successfully');

    // Then seed event notifications
    logger.log('Seeding event notifications...');
    await eventNotificationSeedService.seed();
    logger.log('Event notifications seeded successfully');

    logger.log('All notifications seed completed successfully');
  } catch (error) {
    logger.error('Failed to seed all notifications', error);
    process.exit(1);
  }

  await app.close();
  process.exit(0);
}

bootstrap();
