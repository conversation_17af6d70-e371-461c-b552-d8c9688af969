{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "assets": ["mail/templates/**/*"], "watchAssets": true, "plugins": ["@nestjs/swagger"]}, "projects": {"shared": {"type": "library", "root": "libs/shared", "entryFile": "index", "sourceRoot": "libs/shared/src", "compilerOptions": {"tsConfigPath": "libs/shared/tsconfig.lib.json"}}, "seed": {"type": "application", "entryFile": "seed/seed"}}}